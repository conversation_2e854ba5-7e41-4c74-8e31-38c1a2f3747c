const express = require('express');
const { getDb } = require('../database/db');

const router = express.Router();

// Create new bell alert
router.post('/', (req, res) => {
  const { table_number, message } = req.body;
  
  if (!table_number || !message) {
    return res.status(400).json({ error: 'Table number and message are required' });
  }
  
  const db = getDb();
  
  db.run(
    'INSERT INTO bell_alerts (table_number, message) VALUES (?, ?)',
    [table_number, message],
    function(err) {
      if (err) {
        console.error('Error creating bell alert:', err);
        return res.status(500).json({ error: 'Failed to create bell alert' });
      }
      
      const newAlert = {
        id: this.lastID,
        table_number,
        message,
        status: 'pending',
        created_at: new Date().toISOString()
      };

      // Emit real-time update to counter
      const io = req.app.get('io');
      if (io) {
        io.to('counter').emit('bell-alert-created', newAlert);
      }

      res.status(201).json(newAlert);
    }
  );
});

// Get all bell alerts
router.get('/', (req, res) => {
  const { table_number, status = 'pending' } = req.query;
  const db = getDb();
  
  let query = 'SELECT * FROM bell_alerts WHERE status = ?';
  const params = [status];
  
  if (table_number) {
    query += ' AND table_number = ?';
    params.push(table_number);
  }
  
  query += ' ORDER BY created_at DESC';
  
  db.all(query, params, (err, alerts) => {
    if (err) {
      console.error('Error fetching bell alerts:', err);
      return res.status(500).json({ error: 'Failed to fetch bell alerts' });
    }
    
    res.json(alerts);
  });
});

// Update bell alert status
router.patch('/:id', (req, res) => {
  const { id } = req.params;
  const { status } = req.body;
  
  if (!status) {
    return res.status(400).json({ error: 'Status is required' });
  }
  
  const db = getDb();
  
  db.run(
    'UPDATE bell_alerts SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    [status, id],
    function(err) {
      if (err) {
        console.error('Error updating bell alert:', err);
        return res.status(500).json({ error: 'Failed to update bell alert' });
      }
      
      if (this.changes === 0) {
        return res.status(404).json({ error: 'Bell alert not found' });
      }

      // Emit real-time update to counter
      const io = req.app.get('io');
      if (io) {
        io.to('counter').emit('bell-alert-updated', { id, status });
      }

      res.json({ message: 'Bell alert updated successfully' });
    }
  );
});

// Delete bell alert
router.delete('/:id', (req, res) => {
  const { id } = req.params;
  const db = getDb();
  
  db.run('DELETE FROM bell_alerts WHERE id = ?', [id], function(err) {
    if (err) {
      console.error('Error deleting bell alert:', err);
      return res.status(500).json({ error: 'Failed to delete bell alert' });
    }
    
    if (this.changes === 0) {
      return res.status(404).json({ error: 'Bell alert not found' });
    }
    
    res.json({ message: 'Bell alert deleted successfully' });
  });
});

module.exports = router;
