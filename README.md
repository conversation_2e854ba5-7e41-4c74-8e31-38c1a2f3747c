# 🍜 မြန်မာ့အရသာ - Myanmar Traditional Restaurant Ordering System

မြန်မာ့ရိုးရာအစားအသောက်များကို အွန်လိုင်းမှ အမှာပေးနိုင်သော ပြီးပြည့်စုံသော စနစ်တစ်ခုဖြစ်ပါတယ်။ Local network မှာ အလုပ်လုပ်ပြီး internet လိုအပ်မှု နည်းပါးသော restaurant ordering system ဖြစ်ပါတယ်။

## ✨ အင်္ဂါရပ်များ (Features)

### 🍽️ ဖောက်သည်အတွက် (Customer Features)
- **QR Code Table Ordering**: စားပွဲ QR code ကို scan လုပ်ပြီး အမှာစာ ပေးနိုင်သည်
- **မီနူးကြည့်ရှုခြင်း**: အစားအသောက်များကို အမျိုးအစားအလိုက် ခွဲခြားကြည့်ရှုနိုင်
- **အမှာပေးခြင်း**: လွယ်ကူသော cart system ဖြင့် အမှာပေးနိုင်
- **Real-time Order Status**: အမှာစာ အခြေအနေကို တိုက်ရိုက် ကြည့်ရှုနိုင်
- **Customer Information**: အမည် နှင့် ဖုန်းနံပါတ် ထည့်သွင်းခြင်း
- **Payment Request**: စားပြီးရင် ငွေပေးချေရန် တောင်းဆိုနိုင်
- **Responsive Design**: မိုဘိုင်း၊ တက်ဘလက်၊ လက်တော့ပ် အားလုံးမှာ အသုံးပြုနိုင်
- **Myanmar Language**: မြန်မာဘာသာဖြင့် အပြည့်အစုံ ပံ့ပိုးမှု

### 🧾 ကောင်တာဝန်ထမ်းအတွက် (Counter Staff Features)
- **အမှာများ လက်ခံခြင်း**: Real-time မှာ အမှာများ ရရှိနိုင်
- **Status စီမံခြင်း**: အမှာများ၏ အခြေအနေကို update လုပ်နိုင်
- **Order Management**: အမှာများကို filter လုပ်ပြီး စီမံခြင်း
- **Payment Processing**: ဖောက်သည် ငွေပေးချေမှု လက်ခံခြင်း
- **Customer Service**: ဖောက်သည် ခေါ်ဆိုမှု လက်ခံခြင်း
- **Table Management**: စားပွဲ အခြေအနေ စီမံခြင်း

### 👨‍🍳 မီးဖိုချောင်အတွက် (Kitchen Features)
- **Cooking Queue**: ချက်ရမည့် အမှာများကို priority အလိုက် ကြည့်နিုင်
- **Status Updates**: ချက်ပြုတ်မှု အခြေအနေ update လုပ်နိုင်
- **Individual Item Tracking**: အစားအသောက် တစ်ခုချင်းစီ၏ အခြေအနေ ခြေရာခံခြင်း
- **Priority System**: အချိန်ကြာသော အမှာများကို အရေးကြီးအဖြစ် ပြသခြင်း
- **Real-time Notifications**: အမှာအသစ်များ တိုက်ရိုက် အသိပေးခြင်း

### ⚙️ Admin Panel
- **Dashboard**: ရောင်းအား၊ အမှာများ၏ စာရင်းအင်းများ
- **Advanced Analytics**: ဇယားများ နှင့် ဂရပ်များ ပါသော ခွဲခြမ်းစိတ်ဖြာမှု
- **Financial Management**: ကုန်ကျစရိတ် ခြေရာခံခြင်း နှင့် အမြတ်/အရှုံး ဖော်ပြချက်များ
- **မီနူးစီမံခြင်း**: အစားအသောက်များ ထည့်ခြင်း၊ ပြင်ခြင်း၊ ရရှိမှု toggle လုပ်ခြင်း
- **Table Management**: စားပွဲများ ဖန်တီးခြင်း နှင့် QR code များ ထုတ်လုပ်ခြင်း
- **Customer Database**: ဖောက်သည် အချက်အလက်များ စီမံခြင်း
- **Order History**: အမှာများ၏ မှတ်တမ်းများ
- **Settings**: ဆိုင်၏ အခြေခံ ဆက်တင်များ (နာမည်၊ လိုဂို၊ အိုင်ကွန်)
- **Reports Export**: Excel နှင့် PDF အစီရင်ခံစာများ ထုတ်လုပ်ခြင်း

### 💰 Financial Management (ငွေကြေး စီမံခန့်ခွဲမှု)
- **Expense Tracking**: ကုန်ကျစရိတ် ခြေရာခံခြင်း (အစားအသောက် ပစ္စည်းများ၊ လစာ၊ လျှပ်စစ်ခ၊ ဆိုင်ခ)
- **Profit/Loss Statements**: အမြတ်/အရှုံး ဖော်ပြချက်များ
- **Customer Information**: ဖောက်သည် အမည် နှင့် ဖုန်းနံပါတ် ထိန်းသိမ်းခြင်း
- **Tax Management**: အခွန် စီမံခန့်ခွဲမှု (စီးပွားရေး အခွန် 5%)
- **Cash Payment System**: ငွေသား ပေးချေမှု စနစ်
- **Financial Reports**: ငွေကြေး အစီရင်ခံစာများ နှင့် ခွဲခြမ်းစိတ်ဖြာမှု

## 🚀 စတင်အသုံးပြုခြင်း (Getting Started)

### 🎯 Windows အသုံးပြုသူများအတွက် (One-Click Solution)

#### **Option 1: Quick Start (အကြံပြုထားသော နည်းလမ်း)**
```batch
# ဒီ file ကို double-click လုပ်ပါ
quick-start.bat
```
- ✅ **ပထမဆုံးအကြိမ်**: အလိုအလျောက် အရာအားလုံး install လုပ်ပြီး system စတင်ပေးမည်
- ✅ **နောက်အကြိမ်များ**: System ကိုသာ စတင်ပေးမည်
- ✅ **Smart Detection**: Installation လိုအပ်မလိုအပ် အလိုအလျောက် သိရှိနိုင်သည်

#### **Option 2: Advanced Launcher (အဆင့်မြင့် Menu)**
```batch
# Menu နှင့်အတူ options များ ရရှိရန်
launcher.bat
```
**Menu Options:**
- `[1]` Quick Start (Auto-detect and run)
- `[2]` Fresh Installation (အသစ် install လုပ်ခြင်း)
- `[3]` Start System Only (System ကိုသာ စတင်ခြင်း)
- `[4]` Reset Database (Database ကို reset လုပ်ခြင်း)
- `[5]` Check System Status (System အခြေအနေ စစ်ဆေးခြင်း)
- `[6]` Troubleshooting (ပြဿနာ ဖြေရှင်းခြင်း)
- `[0]` Exit

#### **Option 3: System Health Check (စနစ် ကျန်းမာရေး စစ်ဆေးခြင်း)**
```batch
# အရာအားလုံး မှန်ကန်စွာ အလုပ်လုပ်နေမနေ စစ်ဆေးရန်
check-system.bat
```
- ✅ Node.js installation စစ်ဆေးခြင်း
- ✅ Dependencies များ စစ်ဆေးခြင်း
- ✅ Database connection စစ်ဆေးခြင်း
- ✅ System requirements စစ်ဆေးခြင်း
- ✅ Auto-fix options ရရှိနိုင်သည်

### 💻 Manual Installation (လက်ဖြင့် ထည့်သွင်းခြင်း - All Platforms)

#### **Option 1: Automatic Installation (အလိုအလျောက် ထည့်သွင်းခြင်း)**
```bash
# Complete auto-setup
npm run setup-complete
```

### Option 2: Manual Installation (လက်ဖြင့် ထည့်သွင်းခြင်း)

1. **Dependencies များ ထည့်သွင်းပါ:**
   ```bash
   npm run install-all
   ```

2. **Database ကို setup လုပ်ပါ:**
   ```bash
   npm run setup
   ```

3. **System ကို စတင်ပါ:**
   ```bash
   npm run start-all
   ```

### လိုအပ်ချက်များ (Prerequisites)
- Node.js (v18 သို့မဟုတ် ပိုမြင့်သော version)
- npm သို့မဟုတ် yarn

### Installation

1. **Repository ကို clone လုပ်ပါ**
```bash
git clone <repository-url>
cd restaurant-ordering-app
```

2. **Dependencies များ install လုပ်ပါ**
```bash
npm install
```

3. **Sample data ထည့်ပါ**
```bash
node server/add-sample-data.js
```

4. **Development server များ run လုပ်ပါ**
```bash
# Frontend နှင့် Backend နှစ်ခုလုံး တစ်ပြိုင်နက် run လုပ်ရန်
npm run dev:full

# သို့မဟုတ် သီးခြားစီ run လုပ်ရန်
npm run dev        # Frontend (port 3000)
npm run dev:server # Backend (port 5000)
```

5. **Browser မှာ ဖွင့်ပါ**
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000

## 🌐 Local Network Setup

### WiFi Network မှာ အသုံးပြုရန်
1. Server run လုပ်ပြီးနောက် console မှာ network IP address ကို ကြည့်ပါ
2. အခြား devices များမှ `http://[YOUR-IP]:3000` ဖြင့် access လုပ်နိုင်ပါတယ်
3. ဥပမာ: `http://*************:3000`

### Mobile Devices များအတွက်
- ဆိုင်၏ WiFi network ကို connect လုပ်ပါ
- Browser မှာ network IP address ကို ရိုက်ထည့်ပါ
- Home screen မှာ "Add to Home Screen" လုပ်ပြီး app လို အသုံးပြုနိုင်ပါတယ်

## 🛠️ Technology Stack

### Frontend
- **Next.js 15** - React framework
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **Responsive Design** - Mobile-first approach

### Backend
- **Express.js** - Web server
- **Socket.io** - Real-time communication
- **SQLite3** - Database
- **JWT** - Authentication
- **Multer** - File uploads

### Database Schema
- **Users** - Admin, counter staff, kitchen staff accounts
- **Categories** - Menu categories
- **Menu Items** - Food and beverage items
- **Orders** - Customer orders
- **Order Items** - Individual items in orders
- **Settings** - Restaurant configuration

## 🌐 Access Points / ဝင်ရောက်မည့် လိပ်စာများ

System ကို စတင်ပြီးရင် အောက်ပါ လိပ်စာများမှ ဝင်ရောက်နိုင်ပါသည်:

- **🏠 Customer Home Page**: http://localhost:3002
- **🍽️ Menu Page**: http://localhost:3002/menu
- **⚙️ Admin Panel**: http://localhost:3002/admin
- **🧾 Counter Dashboard**: http://localhost:3002/counter
- **�‍🍳 Kitchen Dashboard**: http://localhost:3002/kitchen
- **🪑 Table View**: http://localhost:3002/table/[table-number]

### 🔐 Default Login Credentials
- **Username**: admin
- **Password**: admin123

## �📱 အသုံးပြုပုံ အသေးစိတ် (Detailed Usage Guide)

### 🍽️ ဖောက်သည်များအတွက် (For Customers)

#### **Option 1: QR Code ဖြင့် အမှာပေးခြင်း**
1. **စားပွဲ QR Code ကို Scan လုပ်ပါ**
   - ဖုန်းရဲ့ camera app ကို ဖွင့်ပါ
   - စားပွဲပေါ်က QR code ကို scan လုပ်ပါ
   - Browser မှာ table-specific menu page ပွင့်လာပါမယ်

2. **Menu ကြည့်ရှုပြီး အမှာပေးခြင်း**
   - အမျိုးအစားများ (🍜🍛🥗🍱) ကို နှိပ်ပြီး ကြည့်ရှုပါ
   - ကြိုက်တဲ့ အစားအသောက်ကို နှိပ်ပါ
   - "🛒 Cart ထဲထည့်ရန်" button ကို နှိပ်ပါ
   - အရေအတွက် ပြောင်းလဲချင်ရင် +/- buttons များ သုံးပါ

3. **Checkout လုပ်ခြင်း**
   - "🛒 Cart ကြည့်ရန်" ကို နှိပ်ပါ
   - အမှာစာများ စစ်ဆေးပါ
   - **Customer Information ဖြည့်ပါ:**
     - အမည် (မြန်မာ): ဥပမာ - မောင်မောင်
     - အမည် (English): ဥပမာ - Maung Maung
     - ဖုန်းနံပါတ်: ဥပမာ - 09123456789
   - "📝 အမှာစာ တင်ရန်" ကို နှိပ်ပါ

4. **Order Status ကြည့်ရှုခြင်း**
   - အမှာပေးပြီးရင် order status page ပေါ်လာပါမယ်
   - Real-time မှာ status update များ ကြည့်နိုင်ပါတယ်:
     - 🟡 **Pending**: အမှာစာ စောင့်ဆိုင်းနေ
     - 🔵 **Confirmed**: ကောင်တာမှ အတည်ပြုပြီး
     - 🟠 **Preparing**: မီးဖိုချောင်မှ ပြင်ဆင်နေ
     - 🟢 **Ready**: အစားအသောက် အဆင်သင့်ဖြစ်ပြီး

5. **Payment Request လုပ်ခြင်း**
   - စားပြီးရင် "💰 ငွေပေးချေရန် တောင်းဆို" button ကို နှိပ်ပါ
   - ကောင်တာမှ notification ရရှိပါမယ်

#### **Option 2: Direct Menu Access**
1. **http://localhost:3002/menu** သို့ တိုက်ရိုက် သွားပါ
2. အပေါ်က steps များ အတိုင်း လုပ်ဆောင်ပါ

### 🧾 ကောင်တာဝန်ထမ်းများအတွက် (For Counter Staff)

1. **Counter Dashboard ဝင်ရောက်ခြင်း**
   - **http://localhost:3002/counter** သို့ သွားပါ
   - Login လုပ်ပါ (admin/admin123)

2. **အမှာများ စီမံခြင်း**
   - **New Orders Section**: အမှာအသစ်များ ကြည့်ရှုပါ
   - **Order Details**: အမှာစာ အသေးစိတ် ကြည့်ရှုပါ
     - ဖောက်သည် အချက်အလက်များ
     - အမှာစာ items များ
     - စုစုပေါင်း ကုန်ကျစရိတ်
     - Table number

3. **Order Status Update လုပ်ခြင်း**
   - **"✅ Confirm Order"**: အမှာစာ အတည်ပြုပါ
   - **"🍳 Send to Kitchen"**: မီးဖိုချောင်သို့ ပို့ပါ
   - **"❌ Cancel Order"**: လိုအပ်ရင် အမှာစာ ပယ်ဖျက်ပါ

4. **Payment Processing**
   - **Payment Requests**: ဖောက်သည် payment တောင်းဆိုမှုများ ကြည့်ပါ
   - **"💰 Payment Received"**: ငွေလက်ခံပြီး ဟု mark လုပ်ပါ
   - **"🧾 Print Receipt"**: လိုအပ်ရင် receipt print လုပ်ပါ

5. **Customer Service**
   - **Bell Alerts**: ဖောက်သည် service တောင်းဆိုမှုများ ကြည့်ပါ
   - **"🔔 Respond to Call"**: ဖောက်သည်ထံ သွားရောက် ဝန်ဆောင်ပါ

### 👨‍🍳 မီးဖိုချောင်ဝန်ထမ်းများအတွက် (For Kitchen Staff)

1. **Kitchen Dashboard ဝင်ရောက်ခြင်း**
   - **http://localhost:3002/kitchen** သို့ သွားပါ
   - Login လုပ်ပါ (admin/admin123)

2. **Cooking Queue ကြည့်ရှုခြင်း**
   - **Pending Orders**: ချက်ရမည့် အမှာများ priority အလိုက် ပြသ
   - **Order Time**: အမှာပေးချိန် ကြည့်ပြီး priority သတ်မှတ်ပါ
   - **Table Information**: ဘယ် table အတွက်လဲ ကြည့်ပါ

3. **Individual Item Tracking**
   - **Item List**: အမှာစာထဲက item တစ်ခုချင်းစီ ကြည့်ပါ
   - **"🍳 Start Cooking"**: ချက်စတင်ပါ
   - **"✅ Item Ready"**: item တစ်ခု ပြီးရင် mark လုပ်ပါ
   - **"🚫 Item Cancelled"**: လိုအပ်ရင် item ပယ်ဖျက်ပါ

4. **Order Completion**
   - **All Items Ready**: item အားလုံး ပြီးရင် order status "Ready" ဖြစ်သွားပါမယ်
   - **Notification**: ကောင်တာမှ notification ရရှိပါမယ်

### ⚙️ Admin များအတွက် (For Administrators)

1. **Admin Panel ဝင်ရောက်ခြင်း**
   - **http://localhost:3002/admin** သို့ သွားပါ
   - **Username**: admin
   - **Password**: admin123

2. **Dashboard Overview**
   - **Today's Statistics**: ယနေ့ ရောင်းအား၊ အမှာများ၊ ဖောက်သည်များ
   - **Recent Orders**: လတ်တလော အမှာများ
   - **Quick Actions**: မြန်ဆန်သော လုပ်ဆောင်ချက်များ

3. **Analytics Tab (📈 ခွဲခြမ်းစိတ်ဖြာမှု)**
   - **Date Range Selection**: ရက်စွဲ အပိုင်းအခြား ရွေးချယ်ပါ
   - **Charts & Graphs**:
     - Daily Sales Line Chart
     - Popular Items Bar Chart
     - Hourly Pattern Chart
     - Category Performance Doughnut Chart
   - **Data Tables**: အသေးစိတ် ဇယားများ ကြည့်ရှုပါ

4. **Financial Tab (💰 ငွေကြေးစီမံခန့်ခွဲမှု)**
   - **Financial Overview**: လစဉ် ရောင်းအား၊ ကုန်ကျစရိတ်၊ အမြတ်
   - **Add Expense**: ကုန်ကျစရိတ် ထည့်သွင်းပါ
     - အမျိုးအစား ရွေးချယ်ပါ (🥬 အစားအသောက် ပစ္စည်းများ၊ 👥 လစာ၊ ⚡ လျှပ်စစ်ခ)
     - ဖော်ပြချက် ရေးပါ
     - ပမာဏ ထည့်ပါ
     - ရောင်းချသူ အချက်အလက် (ရွေးချယ်ခွင့်ရှိ)
   - **Customer Information**: ဖောက်သည် အချက်အလက်များ ထည့်သွင်းပါ

5. **Menu Management (🍽️ မီနူးများ)**
   - **Categories**: အမျိုးအစားများ ထည့်ခြင်း/ပြင်ခြင်း
     - အမည် (မြန်မာ/English)
     - Icon ရွေးချယ်ခြင်း (🍜🍛🥗🍱)
   - **Menu Items**: အစားအသောက်များ ထည့်ခြင်း/ပြင်ခြင်း
     - အမည် (မြန်မာ/English)
     - ဖော်ပြချက်
     - ဈေးနှုန်း
     - ပုံ upload လုပ်ခြင်း
     - ရရှိမှု toggle လုပ်ခြင်း

6. **Table Management (🪑 စားပွဲများ)**
   - **Add New Table**: စားပွဲအသစ် ထည့်ပါ
   - **QR Code Generation**: QR code အလိုအလျောက် ထုတ်လုပ်ပါ
   - **Table Status**: စားပွဲ အခြေအနေ ကြည့်ရှုပါ
   - **Print QR Codes**: QR codes များ print လုပ်ပါ

7. **Settings (⚙️ ဆက်တင်များ)**
   - **Restaurant Information**:
     - ဆိုင်အမည် (မြန်မာ/English)
     - Logo upload လုပ်ခြင်း
     - Restaurant icon ရွေးချယ်ခြင်း
   - **Admin Account**: Username/Password ပြောင်းလဲခြင်း
   - **Tax Settings**: အခွန် နှုန်းထား သတ်မှတ်ခြင်း

## 🔧 Configuration

### Default Settings
- Restaurant Name: မြန်မာ့အရသာ
- Currency: Myanmar Kyat (MMK)
- Default Admin: username: `admin`, password: `admin123`

### Customization
- Restaurant name နှင့် settings များကို Admin panel မှ ပြောင်းလဲနိုင်ပါတယ်
- Menu categories နှင့် items များကို အလွယ်တကူ ထည့်ခြင်း၊ ပြင်ခြင်း လုပ်နိုင်ပါတယ်

## 🤝 Contributing

ဒီ project ကို ပိုကောင်းအောင် လုပ်ချင်ရင်:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🚨 Troubleshooting / ပြဿနာ ဖြေရှင်းခြင်း

### အဖြစ်များသော ပြဿနာများ

#### **1. Port Already in Use Error**
```
Error: listen EADDRINUSE: address already in use :::3002
```
**ဖြေရှင်းနည်း:**
```bash
# Port ကို သုံးနေတဲ့ process ကို ရှာပါ
netstat -ano | findstr :3002

# Process ကို kill လုပ်ပါ (Windows)
taskkill /f /pid [PID_NUMBER]

# သို့မဟုတ် အခြား port သုံးပါ
npm run dev -- -p 3003
```

#### **2. Database Connection Error**
```
Error: SQLITE_CANTOPEN: unable to open database file
```
**ဖြေရှင်းနည်း:**
```bash
# Database ကို ပြန် setup လုပ်ပါ
cd server
node database/migrate-analytics.js
node database/migrate-financial.js
node database/migrate-logo.js
node database/seed.js
```

#### **3. Module Not Found Error**
```
Error: Cannot find module 'next'
```
**ဖြေရှင်းနည်း:**
```bash
# Dependencies များ ပြန် install လုပ်ပါ
rm -rf node_modules package-lock.json
npm install

# Backend dependencies လဲ install လုပ်ပါ
cd server
rm -rf node_modules package-lock.json
npm install
```

#### **4. Images Not Loading**
**ဖြေရှင်းနည်း:**
- `server/uploads` folder ရှိမရှိ စစ်ဆေးပါ
- File permissions စစ်ဆေးပါ
- Image file size 5MB အောက် ရှိမရှိ စစ်ဆေးပါ

#### **5. Real-time Updates Not Working**
**ဖြေရှင်းနည်း:**
- Backend server running ဖြစ်မဖြစ် စစ်ဆေးပါ
- Browser console မှာ WebSocket errors ရှိမရှိ ကြည့်ပါ
- Page ကို refresh လုပ်ကြည့်ပါ

### 💡 Performance Tips

#### **1. Database Optimization**
```bash
# Database ကို optimize လုပ်ရန်
cd server
node -e "
const sqlite3 = require('sqlite3').verbose();
const db = new sqlite3.Database('./database/restaurant.db');
db.run('VACUUM');
db.run('ANALYZE');
db.close();
"
```

#### **2. Image Optimization**
- Menu item images ကို 800x600px အောက် resize လုပ်ပါ
- JPEG format သုံးပါ (PNG ထက် file size သေး)
- Logo images ကို 200x200px အောက် သုံးပါ

#### **3. Network Performance**
- Local network မှာ သုံးရင် router ရဲ့ QoS settings ကို စစ်ဆေးပါ
- WiFi signal strength ကောင်းမကောင်း စစ်ဆေးပါ

### 🔧 Advanced Configuration

#### **1. Custom Port Configuration**
```bash
# Frontend port ပြောင်းရန်
npm run dev -- -p 3005

# Backend port ပြောင်းရန် (server/index.js မှာ)
const PORT = process.env.PORT || 5001;
```

#### **2. Database Backup**
```bash
# Database backup လုပ်ရန်
cp server/database/restaurant.db server/database/restaurant_backup_$(date +%Y%m%d).db

# Backup restore လုပ်ရန်
cp server/database/restaurant_backup_YYYYMMDD.db server/database/restaurant.db
```

#### **3. Custom Restaurant Branding**
- Admin Panel > Settings မှာ restaurant information ပြောင်းပါ
- Logo upload လုပ်ပါ
- Restaurant icon ရွေးချယ်ပါ
- Color scheme ကို CSS မှ customize လုပ်နိုင်ပါတယ်

### 📊 Data Management

#### **1. Sample Data Reset**
```bash
# Sample data ကို ပြန် load လုပ်ရန်
cd server
node database/seed.js
```

#### **2. Clear All Orders**
```bash
# အမှာများ အားလုံး ရှင်းလင်းရန်
cd server
node -e "
const sqlite3 = require('sqlite3').verbose();
const db = new sqlite3.Database('./database/restaurant.db');
db.run('DELETE FROM orders');
db.run('DELETE FROM order_items');
db.close();
console.log('All orders cleared');
"
```

#### **3. Export Data**
```bash
# Database data ကို CSV format ထုတ်ရန်
cd server
node -e "
const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');
const db = new sqlite3.Database('./database/restaurant.db');

db.all('SELECT * FROM orders', (err, rows) => {
  if (err) throw err;
  const csv = rows.map(row => Object.values(row).join(',')).join('\n');
  fs.writeFileSync('orders_export.csv', csv);
  console.log('Orders exported to orders_export.csv');
  db.close();
});
"
```

## 🆘 Support

အကူအညီ လိုအပ်ရင်:
1. **Documentation ကို ပြန်ကြည့်ပါ** - အပေါ်က အသုံးပြုပုံ အသေးစိတ် ကို ကြည့်ပါ
2. **Browser Console ကို စစ်ဆေးပါ** - F12 နှိပ်ပြီး error messages များ ကြည့်ပါ
3. **Server Logs ကို စစ်ဆေးပါ** - Terminal မှာ error messages များ ကြည့်ပါ
4. **Issue တစ်ခု create လုပ်ပါ** - GitHub repository မှာ

### 📞 Quick Help Commands

```bash
# System status စစ်ဆေးရန်
npm run status

# Complete reinstall လုပ်ရန်
npm run setup-complete

# Development mode စတင်ရန်
npm run dev:full

# Production mode စတင်ရန်
npm run start-all
```

## 🚀 Automated Setup with Batch Files

For Windows users, we provide convenient batch files for easy setup and management:

### Available Batch Files:

1. **`Step 0 - Installation and Setup (English).bat`**
   - First-time system setup with comprehensive validation
   - Install dependencies and initialize database
   - Enhanced system requirements check with detailed feedback
   - Installation verification with health checks
   - Automatic folder creation and dependency validation

2. **`Step 1 - Main Launcher (English).bat`**
   - Main menu for all operations
   - Access to all other batch files
   - System overview

3. **`Step 2 - Server Management (English).bat`**
   - Start/stop backend and frontend servers
   - Check server status
   - View server logs

4. **`Step 3 - Database Management (English).bat`**
   - View database tables and data
   - Database backup and reset
   - Database status monitoring

5. **`Step 4 - Data Management (English).bat`**
   - Insert sample data (categories, menu items, users, etc.)
   - Test data insertion
   - Data status overview

6. **`Step 5 - API Testing (English).bat`**
   - Test all API endpoints
   - Health checks
   - API documentation

### Quick Start with Batch Files:

1. **First Time Setup:**
   ```cmd
   "Step 0 - Installation and Setup (English).bat"
   ```
   Choose option [1] for first-time setup
   ⏱️ Takes 3-5 minutes

2. **Daily Usage:**
   ```cmd
   "Step 1 - Main Launcher (English).bat"
   ```
   Use this as your main entry point
   🎯 Recommended starting point

3. **Start Servers:**
   From Main Launcher → [1] Server Management → [3] Start Both Servers
   ⚡ Starts both backend and frontend

4. **Insert Sample Data:**
   From Main Launcher → [3] Data Management → [7] Insert Complete Sample Data
   📊 Adds 6 categories, 8 menu items, 5 users, 10 tables

5. **Open Web Interface:**
   From Main Launcher → [5] Open Web Interface
   🌐 Opens http://localhost:3002

### ✅ Features Added:
- **Error Handling**: Comprehensive error checking and user guidance
- **Progress Indicators**: Visual feedback during operations
- **PowerShell Integration**: Replaced curl with PowerShell for better Windows compatibility
- **User-Friendly Messages**: Clear instructions and status indicators
- **Validation Checks**: Automatic verification of installations and operations
- **Navigation Flow**: All sub-menus return to main menu (no more exit issues)
- **Help System**: Built-in help and documentation in Step 1

### Default Login Credentials:
- **Username:** admin
- **Password:** admin123

---

**🍜 မြန်မာ့အရသာ Restaurant Ordering System**

*မြန်မာ့ရိုးရာအစားအသောက်များကို နည်းပညာဖြင့် ပေါင်းစပ်ထားသော ခေတ်မီ စနစ်*

**Built with ❤️ for Myanmar Restaurants**
