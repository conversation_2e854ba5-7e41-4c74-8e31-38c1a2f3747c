const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'restaurant.db');
const db = new sqlite3.Database(dbPath);

console.log('Adding financial management tables to database...');

db.serialize(() => {
  // Expenses table
  db.run(`
    CREATE TABLE IF NOT EXISTS expenses (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      category TEXT NOT NULL,
      category_mm TEXT NOT NULL,
      description TEXT NOT NULL,
      description_mm TEXT NOT NULL,
      amount DECIMAL(10,2) NOT NULL,
      expense_date DATE NOT NULL,
      receipt_image TEXT,
      vendor_name TEXT,
      vendor_phone TEXT,
      payment_method TEXT DEFAULT 'cash',
      notes TEXT,
      created_by TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `, (err) => {
    if (err) {
      console.error('Error creating expenses table:', err);
    } else {
      console.log('expenses table created successfully');
    }
  });

  // Expense categories table
  db.run(`
    CREATE TABLE IF NOT EXISTS expense_categories (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      name_mm TEXT NOT NULL,
      description TEXT,
      description_mm TEXT,
      icon TEXT DEFAULT '💰',
      is_active BOOLEAN DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `, (err) => {
    if (err) {
      console.error('Error creating expense_categories table:', err);
    } else {
      console.log('expense_categories table created successfully');
    }
  });

  // Customer information table
  db.run(`
    CREATE TABLE IF NOT EXISTS customers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT,
      name_mm TEXT,
      phone TEXT UNIQUE,
      total_orders INTEGER DEFAULT 0,
      total_spent DECIMAL(10,2) DEFAULT 0,
      last_order_date DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `, (err) => {
    if (err) {
      console.error('Error creating customers table:', err);
    } else {
      console.log('customers table created successfully');
    }
  });

  // Daily financial summary table
  db.run(`
    CREATE TABLE IF NOT EXISTS daily_financials (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      date DATE UNIQUE NOT NULL,
      total_revenue DECIMAL(10,2) DEFAULT 0,
      total_expenses DECIMAL(10,2) DEFAULT 0,
      gross_profit DECIMAL(10,2) DEFAULT 0,
      net_profit DECIMAL(10,2) DEFAULT 0,
      tax_amount DECIMAL(10,2) DEFAULT 0,
      cash_sales DECIMAL(10,2) DEFAULT 0,
      total_orders INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `, (err) => {
    if (err) {
      console.error('Error creating daily_financials table:', err);
    } else {
      console.log('daily_financials table created successfully');
    }
  });

  // Tax settings table
  db.run(`
    CREATE TABLE IF NOT EXISTS tax_settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      tax_name TEXT NOT NULL,
      tax_name_mm TEXT NOT NULL,
      tax_rate DECIMAL(5,2) NOT NULL,
      is_active BOOLEAN DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `, (err) => {
    if (err) {
      console.error('Error creating tax_settings table:', err);
    } else {
      console.log('tax_settings table created successfully');
    }
  });

  // Insert default expense categories
  const defaultExpenseCategories = [
    { name: 'Food Ingredients', name_mm: 'အစားအသောက် ပစ္စည်းများ', icon: '🥬' },
    { name: 'Staff Salary', name_mm: 'ဝန်ထမ်း လစာ', icon: '👥' },
    { name: 'Utilities', name_mm: 'လျှပ်စစ်၊ ရေ၊ အင်တာနက်', icon: '⚡' },
    { name: 'Rent', name_mm: 'ဆိုင်ခ', icon: '🏠' },
    { name: 'Equipment', name_mm: 'စက်ပစ္စည်းများ', icon: '🔧' },
    { name: 'Marketing', name_mm: 'ကြော်ငြာ', icon: '📢' },
    { name: 'Transportation', name_mm: 'သယ်ယူပို့ဆောင်ရေး', icon: '🚚' },
    { name: 'Maintenance', name_mm: 'ပြုပြင်ထိန်းသိမ်းမှု', icon: '🔨' },
    { name: 'Office Supplies', name_mm: 'ရုံးသုံးပစ္စည်းများ', icon: '📝' },
    { name: 'Other', name_mm: 'အခြား', icon: '💼' }
  ];

  defaultExpenseCategories.forEach(category => {
    db.run(`
      INSERT OR IGNORE INTO expense_categories (name, name_mm, icon)
      VALUES (?, ?, ?)
    `, [category.name, category.name_mm, category.icon], (err) => {
      if (err) {
        console.error('Error inserting expense category:', err);
      }
    });
  });

  // Insert default tax settings
  db.run(`
    INSERT OR IGNORE INTO tax_settings (tax_name, tax_name_mm, tax_rate)
    VALUES ('Commercial Tax', 'စီးပွားရေး အခွန်', 5.00)
  `, (err) => {
    if (err) {
      console.error('Error inserting tax settings:', err);
    } else {
      console.log('Default tax settings inserted');
    }
  });

  // Add customer info columns to orders table if not exists
  db.run(`
    ALTER TABLE orders ADD COLUMN customer_name TEXT
  `, (err) => {
    if (err && !err.message.includes('duplicate column name')) {
      console.error('Error adding customer_name column:', err);
    }
  });

  db.run(`
    ALTER TABLE orders ADD COLUMN customer_name_mm TEXT
  `, (err) => {
    if (err && !err.message.includes('duplicate column name')) {
      console.error('Error adding customer_name_mm column:', err);
    }
  });

  db.run(`
    ALTER TABLE orders ADD COLUMN payment_method TEXT DEFAULT 'cash'
  `, (err) => {
    if (err && !err.message.includes('duplicate column name')) {
      console.error('Error adding payment_method column:', err);
    }
  });

  db.run(`
    ALTER TABLE orders ADD COLUMN tax_amount DECIMAL(10,2) DEFAULT 0
  `, (err) => {
    if (err && !err.message.includes('duplicate column name')) {
      console.error('Error adding tax_amount column:', err);
    }
  });

  console.log('Financial management migration completed successfully!');
});

db.close();
