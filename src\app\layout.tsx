import type { Metadata, Viewport } from "next";
import "./globals.css";
import { AuthProvider } from "../contexts/AuthContext";

export const metadata: Metadata = {
  title: "Menu, a taste of Myanmar - Myanmar Traditional Restaurant",
  description: "မြန်မာ့ရိုးရာအစားအသောက်များကို အွန်လိုင်းမှ အမှာပေးနိုင်သော စနစ်",
  keywords: "restaurant, myanmar, food, ordering, mobile, menu, အစားအသောက်, မြန်မာ, ဆိုင်",
  authors: [{ name: "Myanmar Restaurant System" }],
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="my">
      <head>
        <meta name="theme-color" content="#5f6d87" />
        <meta name="robots" content="noindex, nofollow" />
        <meta name="google" content="notranslate" />
        <meta name="msapplication-config" content="none" />
        <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍜</text></svg>" />
      </head>
      <body className="antialiased myanmar-text">
        <AuthProvider>
          <div className="min-h-screen">
            {children}
          </div>
        </AuthProvider>
      </body>
    </html>
  );
}
