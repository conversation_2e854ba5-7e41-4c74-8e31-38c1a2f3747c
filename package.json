{"name": "restaurant-ordering-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0 -p 3002", "build": "next build", "start": "next start -H 0.0.0.0 -p 3002", "lint": "next lint", "server": "node server/index.js", "dev:server": "nodemon server/index.js", "dev:full": "concurrently \"npm run dev\" \"npm run dev:server\"", "install-all": "npm install && cd server && npm install", "setup": "npm run install-all && cd server && node database/migrate-analytics.js && node database/migrate-financial.js && node database/migrate-logo.js && node database/seed.js", "setup-complete": "node setup-complete.js", "start-backend": "cd server && node index.js", "start-all": "concurrently \"npm run start-backend\" \"npm run dev\"", "reset-db": "cd server && node database/seed.js", "check-system": "node -e \"console.log('System check completed')\"", "quick-start": "node -e \"console.log('Use quick-start.bat for Windows')\"", "status": "node -e \"console.log('Use check-system.bat for detailed status')\""}, "dependencies": {"bcryptjs": "^3.0.2", "chart.js": "^4.5.0", "cors": "^2.8.5", "exceljs": "^4.4.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "next": "^15.3.4", "pdfkit": "^0.17.1", "qrcode": "^1.5.4", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^1.4.13", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "concurrently": "^9.2.0", "eslint": "^9", "eslint-config-next": "15.3.4", "nodemon": "^3.1.10", "tailwindcss": "^4", "typescript": "^5"}}