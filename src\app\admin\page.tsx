'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../contexts/AuthContext';
import Navigation from '../../components/Navigation';
import ImageUpload from '../../components/ImageUpload';
import { DailySalesChart, PopularItemsChart, HourlyPatternChart, CategoryPerformanceChart } from '../../components/Charts';
import io from 'socket.io-client';

interface Table {
  id: number;
  table_number: string;
  table_name: string;
  capacity: number;
  qr_code: string;
  is_active: boolean;
}

interface Category {
  id: number;
  name: string;
  name_mm: string;
  description?: string;
  image_url?: string;
  icon?: string;
  sort_order: number;
  is_active: boolean;
}

interface MenuItem {
  id: number;
  name: string;
  name_mm: string;
  description?: string;
  description_mm?: string;
  price: number;
  category_id: number;
  category_name?: string;
  category_name_mm?: string;
  is_available: boolean;
  is_today_special: boolean;
  is_active: boolean;
  image_url?: string;
  sort_order: number;
}

// Table Form Component
function TableForm({ table, onSubmit, onClose }: { table?: Table; onSubmit: (data: any) => void; onClose: () => void }) {
  const [formData, setFormData] = useState({
    table_number: table?.table_number || '',
    table_name: table?.table_name || '',
    capacity: table?.capacity || 4,
    qr_code: table?.qr_code || ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4 border border-gray-700">
        <h3 className="text-xl font-bold text-white mb-4">
          {table ? 'စားပွဲ ပြင်ဆင်ရန်' : 'စားပွဲအသစ် ထည့်ရန်'}
        </h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              စားပွဲနံပါတ်
            </label>
            <input
              type="text"
              value={formData.table_number}
              onChange={(e) => setFormData({...formData, table_number: e.target.value})}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400"
              placeholder="ဥပမာ: T001"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              စားပွဲအမည်
            </label>
            <input
              type="text"
              value={formData.table_name}
              onChange={(e) => setFormData({...formData, table_name: e.target.value})}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400"
              placeholder="ဥပမာ: ပင်မစားပွဲ"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              ဆံ့်နိုင်သူအရေအတွက်
            </label>
            <input
              type="number"
              value={formData.capacity}
              onChange={(e) => setFormData({...formData, capacity: parseInt(e.target.value)})}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400"
              min="1"
              max="20"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              QR ကုဒ်
            </label>
            <input
              type="text"
              value={formData.qr_code}
              onChange={(e) => setFormData({...formData, qr_code: e.target.value})}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400"
              placeholder="QR ကုဒ် (ရွေးချယ်ရန်)"
            />
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              className="flex-1 bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors"
            >
              သိမ်းရန်
            </button>
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-600 text-gray-300 py-2 px-4 rounded-lg hover:bg-gray-500 transition-colors"
            >
              ပယ်ဖျက်ရန်
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Category Form Component
function CategoryForm({ category, onSubmit, onClose }: { category?: Category; onSubmit: (data: any) => void; onClose: () => void }) {
  const [formData, setFormData] = useState({
    name: category?.name || '',
    name_mm: category?.name_mm || '',
    description: category?.description || '',
    icon: category?.icon || '🍽️',
    sort_order: category?.sort_order || 1
  });

  const allFoodIcons = [
    // Main dishes & Rice
    '🍽️', '🍚', '🍛', '🍱', '🍲', '🥘', '🍳', '🥞', '🧆', '🍙', '🍘',

    // Noodles & Pasta
    '🍜', '🍝', '🥟', '🥠', '🥡', '🍄', '🥬', '🥒', '🌶️',

    // Bread & Bakery
    '🥨', '🥖', '🍞', '🥐', '🧈', '🥯', '🥪', '🌯', '🥙',

    // Fast Food & Snacks
    '🍕', '🍔', '🍟', '🌭', '🥓', '🍗', '🍖', '🥩', '🌮',

    // Seafood
    '🍤', '🦐', '🦀', '🐟', '🍣', '🦞', '🐠', '🐙', '🦑', '🐚',

    // Vegetables & Salads
    '🥗', '🥕', '🍅', '🥑', '🌽', '🥦', '🧄', '🧅', '🍆', '🥔', '🍠',

    // Fruits
    '🍎', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🫐', '🍈', '🍑', '🍒', '🥭', '🍍', '🥥', '🥝',

    // Desserts & Sweets
    '🍦', '🍧', '🍨', '🍩', '🍪', '🎂', '🧁', '🥧', '🍰', '🍫', '🍬', '🍭', '🍮', '🍯', '🧊',

    // Beverages
    '☕', '🍵', '🧃', '🥤', '🧋', '🍷', '🍺', '🍻', '🥂', '🍾', '🥃', '🍸', '🍹', '🥛', '💧',

    // Cooking & Kitchen
    '🔥', '🧂', '🌿', '🍃', '🌱', '⭐', '✨', '🎯', '💎', '🏆'
  ];

  // Remove duplicates and create unique array
  const foodIcons = [...new Set(allFoodIcons)];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('🔧 Category form submitting data:', formData);
    onSubmit(formData);
  };

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto border border-gray-700">
        <h3 className="text-xl font-bold text-white mb-4">
          {category ? 'အမျိုးအစား ပြင်ဆင်ရန်' : 'အမျိုးအစားအသစ် ထည့်ရန်'}
        </h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              အမည် (English)
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400"
              placeholder="Category Name"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              အမည် (မြန်မာ)
            </label>
            <input
              type="text"
              value={formData.name_mm}
              onChange={(e) => setFormData({...formData, name_mm: e.target.value})}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400"
              placeholder="အမျိုးအစားအမည်"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              ဖော်ပြချက် (English)
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400"
              rows={2}
              placeholder="Description"
            />
          </div>



          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              အိုင်ကွန်
            </label>
            <div className="grid grid-cols-10 gap-2 max-h-48 overflow-y-auto bg-gray-700 p-3 rounded-lg border border-gray-600">
              {foodIcons.map((icon, index) => (
                <button
                  key={`icon-${index}-${icon.codePointAt(0)}`}
                  type="button"
                  onClick={() => setFormData({...formData, icon})}
                  className={`text-xl p-2 rounded-lg hover:bg-gray-600 transition-all duration-200 hover:scale-110 ${
                    formData.icon === icon ? 'bg-purple-600 ring-2 ring-purple-400' : 'hover:ring-1 hover:ring-gray-500'
                  }`}
                >
                  {icon}
                </button>
              ))}
            </div>
            <div className="mt-2 text-center">
              <span className="text-3xl">{formData.icon}</span>
              <p className="text-xs text-gray-400">ရွေးချယ်ထားသော အိုင်ကွန်</p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              အစီအစဉ်
            </label>
            <input
              type="number"
              value={formData.sort_order || ''}
              onChange={(e) => setFormData({...formData, sort_order: parseInt(e.target.value) || 0})}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400"
              min="1"
            />
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              className="flex-1 bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors"
            >
              သိမ်းရန်
            </button>
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-600 text-gray-300 py-2 px-4 rounded-lg hover:bg-gray-500 transition-colors"
            >
              ပယ်ဖျက်ရန်
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Menu Form Component
function MenuForm({ menuItem, categories, onSubmit, onClose }: {
  menuItem?: MenuItem;
  categories: Category[];
  onSubmit: (data: any) => void;
  onClose: () => void
}) {
  const [formData, setFormData] = useState({
    name: menuItem?.name || '',
    name_mm: menuItem?.name_mm || '',
    description: menuItem?.description || '',
    description_mm: menuItem?.description_mm || '',
    price: menuItem?.price || 0,
    category_id: menuItem?.category_id || 0,
    image_url: menuItem?.image_url || '',
    is_available: menuItem?.is_available ?? true,
    is_today_special: menuItem?.is_today_special ?? false,
    is_active: menuItem?.is_active ?? true,
    sort_order: menuItem?.sort_order || 1
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      ...formData,
      price: parseFloat(formData.price.toString())
    });
  };

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto border border-gray-700">
        <h3 className="text-xl font-bold text-white mb-4">
          {menuItem ? 'မီနူး ပြင်ဆင်ရန်' : 'မီနူးအသစ် ထည့်ရန်'}
        </h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              အမည် (English)
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400"
              placeholder="Menu Item Name"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              အမည် (မြန်မာ)
            </label>
            <input
              type="text"
              value={formData.name_mm}
              onChange={(e) => setFormData({...formData, name_mm: e.target.value})}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400"
              placeholder="မီနူးအမည်"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              အမျိုးအစား
            </label>
            <select
              value={formData.category_id}
              onChange={(e) => setFormData({...formData, category_id: parseInt(e.target.value)})}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white"
              required
            >
              <option value="">ရွေးချယ်ပါ</option>
              {categories.filter((c: any) => c.is_active).map((category: any) => (
                <option key={category.id} value={category.id}>
                  {category.icon || '🍽️'} {category.name_mm} ({category.name})
                </option>
              ))}
            </select>
            {formData.category_id && (
              <div className="mt-2 p-2 bg-gray-600 rounded text-center">
                <span className="text-2xl">
                  {categories.find((c: any) => c.id === formData.category_id)?.icon || '🍽️'}
                </span>
                <p className="text-xs text-gray-300 mt-1">ရွေးချယ်ထားသော အမျိုးအစား</p>
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              ဈေး (ကျပ်)
            </label>
            <input
              type="number"
              value={formData.price}
              onChange={(e) => setFormData({...formData, price: parseFloat(e.target.value)})}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400"
              min="0"
              step="100"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              ဖော်ပြချက် (English)
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400"
              rows={2}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              ဖော်ပြချက် (မြန်မာ)
            </label>
            <textarea
              value={formData.description_mm}
              onChange={(e) => setFormData({...formData, description_mm: e.target.value})}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400"
              rows={2}
            />
          </div>

          <ImageUpload
            currentImage={formData.image_url}
            onImageChange={(imageUrl) => setFormData({...formData, image_url: imageUrl})}
            label="မီနူး ပုံ"
          />

          <div className="flex space-x-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_available}
                onChange={(e) => setFormData({...formData, is_available: e.target.checked})}
                className="mr-2 rounded"
              />
              <span className="text-sm text-gray-300">ရရှိနိုင်သည်</span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_today_special}
                onChange={(e) => setFormData({...formData, is_today_special: e.target.checked})}
                className="mr-2 rounded"
              />
              <span className="text-sm text-gray-300">ယနေ့အထူး</span>
            </label>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              အစီအစဉ်
            </label>
            <input
              type="number"
              value={formData.sort_order}
              onChange={(e) => setFormData({...formData, sort_order: parseInt(e.target.value)})}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400"
              min="0"
            />
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              className="flex-1 bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors"
            >
              သိမ်းရန်
            </button>
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-600 text-gray-300 py-2 px-4 rounded-lg hover:bg-gray-500 transition-colors"
            >
              ပယ်ဖျက်ရန်
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Settings Tab Component
function SettingsTab({
  settings,
  onSettingsChange,
  onSave,
  tables,
  categories,
  menuItems,
  setTables,
  setCategories,
  setMenuItems,
  setSettings,
  fetchData
}: {
  settings: any;
  onSettingsChange: (field: string, value: string) => void;
  onSave: (settings: any) => void;
  tables: any[];
  categories: any[];
  menuItems: any[];
  setTables: (tables: any[]) => void;
  setCategories: (categories: any[]) => void;
  setMenuItems: (menuItems: any[]) => void;
  setSettings: (settings: any) => void;
  fetchData: () => Promise<void>;
}) {
  const [saving, setSaving] = useState(false);
  const [users, setUsers] = useState<any[]>([]);
  const [showUserForm, setShowUserForm] = useState(false);
  const [editingUser, setEditingUser] = useState<any>(null);
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [newUser, setNewUser] = useState({
    username: '',
    password: '',
    role: 'counter',
    name: ''
  });
  const [newPassword, setNewPassword] = useState('');
  const [resetting, setResetting] = useState(false);
  const [showIconSection, setShowIconSection] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);

  useEffect(() => {
    fetchUsers();
    fetchCurrentUser();
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/admin/users');
      if (response.ok) {
        const usersData = await response.json();
        setUsers(usersData);
        console.log('✅ Users fetched successfully:', usersData.length, 'users');
      } else {
        console.error('Failed to fetch users:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  const fetchCurrentUser = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        console.log('No token found, skipping user fetch');
        return;
      }

      const response = await fetch('http://localhost:5000/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (response.ok) {
        const userData = await response.json();
        setCurrentUser(userData);
        console.log('✅ Current user fetched:', userData.name);
      } else {
        console.log('User authentication failed, token may be invalid');
      }
    } catch (error) {
      console.error('Error fetching current user:', error);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      await onSave(settings);
    } finally {
      setSaving(false);
    }
  };

  const handleChange = (key: string, value: string) => {
    onSettingsChange(key, value);
  };

  const handleResetDatabase = async () => {
    console.log('🚀 Reset button clicked!');

    if (!confirm('သတိပေးချက်: ဒါက database အကုန်လုံးကို reset လုပ်မယ်!\n\n- အော်ဒါများ အကုန်လုံး ပျောက်သွားမယ်\n- မီနူး items တွေ default ပြန်ဖြစ်မယ်\n- ဖောက်သည် data တွေ ရှင်းသွားမယ်\n- Settings တွေ reset ဖြစ်သွားမယ်\n\nသေချာပါသလား?')) {
      console.log('❌ Reset cancelled by user');
      return;
    }

    console.log('✅ Reset confirmed by user');
    setResetting(true);

    try {
      console.log('🔄 Starting database reset...');
      console.log('📡 Making API call to reset database...');

      const response = await fetch('http://localhost:5000/api/admin/reset-database', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Reset successful:', result);

        alert('Database reset အောင်မြင်ပါတယ်! Page ကို refresh လုပ်နေပါတယ်...');

        // Clear all local state first
        setTables([]);
        setCategories([]);
        setMenuItems([]);
        setSettings({});

        // Force refresh data
        await fetchData();

        // Also force page reload as backup
        setTimeout(() => {
          window.location.reload();
        }, 1000);

      } else {
        const error = await response.json();
        console.error('❌ Reset failed:', error);
        alert(`Database reset မအောင်မြင်ပါ: ${error.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('❌ Error resetting database:', error);
      alert('Database reset မအောင်မြင်ပါ။ Network error ဖြစ်နေပါတယ်။');
    } finally {
      setResetting(false);
    }
  };

  const handleCreateUser = async () => {
    if (!newUser.username || !newUser.password || !newUser.name) {
      alert('အချက်အလက်များ အပြည့်အစုံ ဖြည့်ပါ။');
      return;
    }

    try {
      const response = await fetch('http://localhost:5000/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newUser),
      });

      if (response.ok) {
        alert('အသုံးပြုသူ အသစ် ဖန်တီးပြီးပါပြီ!');
        setNewUser({ username: '', password: '', role: 'counter', name: '' });
        setShowUserForm(false);
        fetchUsers();
      } else {
        const error = await response.json();
        alert(error.error || 'အသုံးပြုသူ ဖန်တီးရာတွင် အမှားရှိပါသည်။');
      }
    } catch (error) {
      console.error('Error creating user:', error);
      alert('အသုံးပြုသူ ဖန်တီးရာတွင် အမှားရှိပါသည်။');
    }
  };

  const handleUpdatePassword = async () => {
    if (!newPassword || newPassword.length < 6) {
      alert('စကားဝှက်သည် အနည်းဆုံး ၆ လုံး ရှိရမည်။');
      return;
    }

    try {
      const response = await fetch(`http://localhost:5000/api/admin/users/${editingUser.id}/password`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password: newPassword }),
      });

      if (response.ok) {
        alert('စကားဝှက် ပြောင်းလဲပြီးပါပြီ!');
        setNewPassword('');
        setShowPasswordForm(false);
        setEditingUser(null);
      } else {
        const error = await response.json();
        alert(error.error || 'စကားဝှက် ပြောင်းလဲရာတွင် အမှားရှိပါသည်။');
      }
    } catch (error) {
      console.error('Error updating password:', error);
      alert('စကားဝှက် ပြောင်းလဲရာတွင် အမှားရှိပါသည်။');
    }
  };

  const handleDeleteUser = async (userId: number, username: string) => {
    if (!confirm(`သေချာပါသလား? "${username}" အသုံးပြုသူကို ဖျက်မယ်?`)) {
      return;
    }

    try {
      const response = await fetch(`http://localhost:5000/api/admin/users/${userId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        alert('အသုံးပြုသူ ဖျက်ပြီးပါပြီ!');
        fetchUsers();
      } else {
        const error = await response.json();
        alert(error.error || 'အသုံးပြုသူ ဖျက်ရာတွင် အမှားရှိပါသည်။');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      alert('အသုံးပြုသူ ဖျက်ရာတွင် အမှားရှိပါသည်။');
    }
  };

  return (
    <div>
      <div className="flex items-center space-x-3 mb-6">
        <div className="bg-blue-500/20 rounded-lg p-2">
          <span className="text-xl">⚙️</span>
        </div>
        <div>
          <h2 className="text-lg font-bold text-white">ဆက်တင်များ</h2>
          <p className="text-xs text-gray-400">System Settings & Management</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Column 1: Restaurant Information */}
        <div className="lg:col-span-1">
          <form onSubmit={(e) => { e.preventDefault(); handleSave(); }}>
            <div className="bg-gray-800/60 backdrop-blur-sm rounded-xl p-4 border border-gray-700 h-fit">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-bold text-white">🏪 စားသောက်ဆိုင် အချက်အလက်များ</h3>
                <button
                  type="submit"
                  disabled={saving}
                  className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-500/50 text-white px-3 py-1.5 rounded text-xs font-medium"
                >
                  {saving ? '💾 သိမ်းနေ...' : '💾 သိမ်း'}
                </button>
              </div>

              <div className="space-y-3">
                {/* Restaurant Name */}
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    မြန်မာ အမည်
                  </label>
                  <input
                    type="text"
                    value={settings.restaurant_name_mm || ''}
                    onChange={(e) => handleChange('restaurant_name_mm', e.target.value)}
                    className="w-full px-2 py-1.5 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 text-xs"
                    placeholder="အရှင်စားသောက်ဆိုင်"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    English Name
                  </label>
                  <input
                    type="text"
                    value={settings.restaurant_name || ''}
                    onChange={(e) => handleChange('restaurant_name', e.target.value)}
                    className="w-full px-2 py-1.5 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 text-xs"
                    placeholder="A Shin Restaurant"
                  />
                </div>

                {/* Restaurant Icon Selection - Collapsible */}
                <div className="border border-gray-600 rounded">
                  <button
                    type="button"
                    onClick={() => setShowIconSection(!showIconSection)}
                    className="w-full flex items-center justify-between p-2 text-left hover:bg-gray-700/30 transition-colors"
                  >
                    <div className="flex items-center space-x-2">
                      <span className="text-xs font-medium text-gray-300">🎨 အိုင်ကွန်</span>
                      {settings.restaurant_icon && (
                        <span className="text-sm">{settings.restaurant_icon}</span>
                      )}
                    </div>
                    <span className={`text-gray-400 transition-transform text-xs ${showIconSection ? 'rotate-180' : ''}`}>
                      ▼
                    </span>
                  </button>

                  {showIconSection && (
                    <div className="p-2 border-t border-gray-600">
                      <div className="grid grid-cols-6 gap-1">
                        {[
                          '🍜', '🍛', '🍝', '🍕', '🍔', '🌮', '🥗', '🍱', '🍙', '🍘',
                          '🥟', '🍳', '🥘', '🍲', '🥙', '🌯', '🥪', '🍖', '🍗', '🥓',
                          '🍤', '🦐', '🐟', '🐠', '🦀', '🦞', '🍣', '🥠', '🍡', '☕',
                          '🍵', '🧃', '🥤', '🍺', '🍷', '🥂'
                        ].map((icon, index) => (
                          <button
                            key={index}
                            type="button"
                            onClick={() => handleChange('restaurant_icon', icon)}
                            className={`w-6 h-6 rounded border flex items-center justify-center text-xs transition-all hover:scale-110 ${
                              settings.restaurant_icon === icon
                                ? 'border-blue-400 bg-blue-500/20'
                                : 'border-gray-600 bg-gray-700/50 hover:border-gray-500'
                            }`}
                          >
                            {icon}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Restaurant Details */}
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    ခေါင်းစဉ် (မြန်မာ)
                  </label>
                  <input
                    type="text"
                    value={settings.restaurant_title_mm || ''}
                    onChange={(e) => handleChange('restaurant_title_mm', e.target.value)}
                    className="w-full px-2 py-1.5 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 text-xs"
                    placeholder="မြန်မာ့အရသာ"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    ခေါင်းစဉ် (English)
                  </label>
                  <input
                    type="text"
                    value={settings.restaurant_title_en || ''}
                    onChange={(e) => handleChange('restaurant_title_en', e.target.value)}
                    className="w-full px-2 py-1.5 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 text-xs"
                    placeholder="Myanmar Traditional Cuisine"
                  />
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    လိပ်စာ
                  </label>
                  <textarea
                    value={settings.address || ''}
                    onChange={(e) => handleChange('address', e.target.value)}
                    className="w-full px-2 py-1.5 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 text-xs"
                    rows={2}
                    placeholder="စားသောက်ဆိုင် လိပ်စာ"
                  />
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    ဖုန်းနံပါတ်
                  </label>
                  <input
                    type="text"
                    value={settings.phone || ''}
                    onChange={(e) => handleChange('phone', e.target.value)}
                    className="w-full px-2 py-1.5 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 text-xs"
                    placeholder="ဖုန်းနံပါတ်"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    အီးမေးလ်
                  </label>
                  <input
                    type="email"
                    value={settings.email || ''}
                    onChange={(e) => handleChange('email', e.target.value)}
                    className="w-full px-2 py-1.5 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 text-xs"
                    placeholder="အီးမေးလ်လိပ်စာ"
                  />
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    ဖွင့်ချိန် (မြန်မာ)
                  </label>
                  <input
                    type="text"
                    value={settings.opening_hours_mm || ''}
                    onChange={(e) => handleChange('opening_hours_mm', e.target.value)}
                    className="w-full px-2 py-1.5 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 text-xs"
                    placeholder="၂၄ နာရီ ဖွင့်ထားသည်"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    ဖွင့်ချိန် (English)
                  </label>
                  <input
                    type="text"
                    value={settings.opening_hours_en || ''}
                    onChange={(e) => handleChange('opening_hours_en', e.target.value)}
                    className="w-full px-2 py-1.5 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 text-xs"
                    placeholder="Always Open for You"
                  />
                </div>

                {/* Logo Upload */}
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    လိုဂို (ရွေးချယ်ခွင့်ရှိ)
                  </label>
                  <p className="text-xs text-gray-500 mb-1">အိုင်ကွန် မရွေးချယ်ပါက လိုဂို ပုံကို အသုံးပြုပါမယ်</p>
                  <ImageUpload
                    currentImage={settings.restaurant_logo || ''}
                    onImageChange={(imageUrl) => handleChange('restaurant_logo', imageUrl)}
                    label="လိုဂို ပုံ"
                    className="scale-75"
                  />
                  {settings.restaurant_logo && (
                    <div className="mt-1 p-1 bg-gray-700 rounded">
                      <img
                        src={settings.restaurant_logo}
                        alt="Restaurant Logo"
                        className="h-8 w-auto object-contain bg-white rounded p-0.5"
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </form>
        </div>

        {/* Column 2: User Management */}
        <div className="lg:col-span-1">
          <div className="bg-gray-800/60 backdrop-blur-sm rounded-xl p-4 border border-gray-700 h-fit">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-bold text-white">👥 အသုံးပြုသူများ စီမံခန့်ခွဲမှု</h3>
              <button
                type="button"
                onClick={() => setShowUserForm(true)}
                className="bg-green-500 hover:bg-green-600 text-white px-2 py-1 rounded text-xs transition-colors font-medium"
              >
                + အသစ်
              </button>
            </div>

            {/* Current User Info */}
            {currentUser && (
              <div className="bg-gradient-to-r from-blue-900/40 to-purple-900/40 border border-blue-500/30 rounded-lg p-3 mb-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-blue-200 text-xs font-medium">🎯 လက်ရှိ အသုံးပြုသူ</span>
                  <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                    currentUser.role === 'admin' ? 'bg-red-500/20 text-red-300 border border-red-500/30' :
                    currentUser.role === 'counter' ? 'bg-blue-500/20 text-blue-300 border border-blue-500/30' :
                    'bg-green-500/20 text-green-300 border border-green-500/30'
                  }`}>
                    {currentUser.role === 'admin' ? '👑 အက်ဒမင်' :
                     currentUser.role === 'counter' ? '💰 ကောင်တာ' : '🍳 မီးဖိုချောင်'}
                  </span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold border-2 ${
                    currentUser.role === 'admin' ? 'bg-red-500 text-white border-red-400' :
                    currentUser.role === 'counter' ? 'bg-blue-500 text-white border-blue-400' :
                    'bg-green-500 text-white border-green-400'
                  }`}>
                    {currentUser.name?.charAt(0) || 'A'}
                  </div>
                  <div className="flex-1">
                    <p className="text-white font-bold text-sm">{currentUser.name}</p>
                    <p className="text-blue-200 text-xs font-medium">@{currentUser.username}</p>
                    {currentUser.created_at && (
                      <p className="text-gray-400 text-xs">
                        📅 {new Date(currentUser.created_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric'
                        })} မှ စတင်
                      </p>
                    )}
                  </div>
                </div>

                {/* Admin Credentials Display */}
                {currentUser.role === 'admin' && (
                  <div className="mt-3 p-2 bg-gray-800/50 rounded border border-gray-600/50">
                    <h5 className="text-xs font-bold text-gray-300 mb-2">🔐 Default Admin Credentials</h5>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="bg-gray-700/50 rounded p-1.5">
                        <span className="text-gray-400">Username:</span>
                        <div className="text-white font-mono">admin</div>
                      </div>
                      <div className="bg-gray-700/50 rounded p-1.5">
                        <span className="text-gray-400">Password:</span>
                        <div className="text-white font-mono">admin123</div>
                      </div>
                    </div>
                    <p className="text-yellow-400 text-xs mt-1">⚠️ စကားဝှက်ကို ပြောင်းလဲဖို့ အကြံပြုပါတယ်</p>
                  </div>
                )}

                <div className="mt-3 pt-2 border-t border-blue-500/20">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-blue-300 font-medium">User ID: #{currentUser.id}</span>
                    <button
                      type="button"
                      onClick={() => {
                        setEditingUser(currentUser);
                        setShowPasswordForm(true);
                      }}
                      className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white px-3 py-1 rounded-full text-xs transition-all duration-200 font-medium"
                    >
                      🔑 စကားဝှက် ပြောင်း
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Default Accounts Info */}
            <div className="bg-gray-700/30 rounded p-2 mb-3">
              <h4 className="text-xs font-medium text-gray-300 mb-2">🔐 Default User Accounts</h4>
              <div className="space-y-1">
                <div className="bg-red-500/10 border border-red-500/20 rounded p-1.5">
                  <div className="flex items-center justify-between">
                    <span className="text-red-300 text-xs font-medium">👑 Admin</span>
                    <span className="text-red-400 text-xs">admin / admin123</span>
                  </div>
                </div>
                <div className="bg-green-500/10 border border-green-500/20 rounded p-1.5">
                  <div className="flex items-center justify-between">
                    <span className="text-green-300 text-xs font-medium">🍳 Kitchen</span>
                    <span className="text-green-400 text-xs">kitchen / kitchen123</span>
                  </div>
                </div>
                <div className="bg-blue-500/10 border border-blue-500/20 rounded p-1.5">
                  <div className="flex items-center justify-between">
                    <span className="text-blue-300 text-xs font-medium">💰 Counter</span>
                    <span className="text-blue-400 text-xs">counter / counter123</span>
                  </div>
                </div>
              </div>
            </div>

            {/* User Statistics */}
            <div className="bg-gray-700/30 rounded p-2 mb-3">
              <h4 className="text-xs font-medium text-gray-300 mb-2">📊 အသုံးပြုသူ စာရင်းအင်း</h4>
              <div className="grid grid-cols-3 gap-2 text-center">
                <div className="bg-gray-600/50 rounded p-1">
                  <div className="text-white font-bold text-sm">{users.length}</div>
                  <div className="text-gray-400 text-xs">စုစုပေါင်း</div>
                </div>
                <div className="bg-red-500/20 rounded p-1">
                  <div className="text-red-300 font-bold text-sm">{users.filter(u => u.role === 'admin').length}</div>
                  <div className="text-red-400 text-xs">အက်ဒမင်</div>
                </div>
                <div className="bg-blue-500/20 rounded p-1">
                  <div className="text-blue-300 font-bold text-sm">{users.filter(u => u.role === 'counter').length}</div>
                  <div className="text-blue-400 text-xs">ကောင်တာ</div>
                </div>
              </div>
              <div className="mt-2 text-center">
                <div className="bg-green-500/20 rounded p-1">
                  <div className="text-green-300 font-bold text-sm">{users.filter(u => u.role === 'kitchen').length}</div>
                  <div className="text-green-400 text-xs">မီးဖိုချောင်</div>
                </div>
              </div>
            </div>

            {/* Users List */}
            <div className="space-y-1 max-h-80 overflow-y-auto">
              <div className="flex items-center justify-between text-xs text-gray-400 mb-2">
                <span>👥 အသုံးပြုသူများ စာရင်း</span>
                <span className="text-gray-500">({users.length} ယောက်)</span>
              </div>

              {users.map((user) => (
                <div key={user.id} className={`rounded p-2 border transition-all ${
                  currentUser && user.id === currentUser.id
                    ? 'bg-blue-900/40 border-blue-500/50'
                    : 'bg-gray-700/50 border-gray-600/50 hover:bg-gray-700/70'
                }`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                        user.role === 'admin' ? 'bg-red-500 text-white' :
                        user.role === 'counter' ? 'bg-blue-500 text-white' :
                        'bg-green-500 text-white'
                      }`}>
                        {user.name?.charAt(0) || 'U'}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-1">
                          <span className="text-white font-medium text-xs">{user.name}</span>
                          {currentUser && user.id === currentUser.id && (
                            <span className="text-blue-300 text-xs">(မင်း)</span>
                          )}
                          <span className={`px-1 py-0.5 rounded text-xs ${
                            user.role === 'admin' ? 'bg-red-500/20 text-red-300' :
                            user.role === 'counter' ? 'bg-blue-500/20 text-blue-300' :
                            'bg-green-500/20 text-green-300'
                          }`}>
                            {user.role === 'admin' ? '🔑' :
                             user.role === 'counter' ? '💰' : '🍳'}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <p className="text-gray-400 text-xs">@{user.username}</p>
                          <span className="text-gray-500 text-xs">#{user.id}</span>
                        </div>
                        {user.created_at && (
                          <p className="text-gray-500 text-xs">
                            📅 {new Date(user.created_at).toLocaleDateString('my-MM', {
                              month: 'short',
                              day: 'numeric'
                            })}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex flex-col space-y-1">
                      <button
                        type="button"
                        onClick={() => {
                          setEditingUser(user);
                          setShowPasswordForm(true);
                        }}
                        className="bg-blue-500 hover:bg-blue-600 text-white px-1.5 py-0.5 rounded text-xs transition-colors"
                        title="စကားဝှက် ပြောင်းလဲရန်"
                      >
                        🔑
                      </button>
                      {user.role !== 'admin' && (
                        <button
                          type="button"
                          onClick={() => handleDeleteUser(user.id, user.username)}
                          className="bg-red-500 hover:bg-red-600 text-white px-1.5 py-0.5 rounded text-xs transition-colors"
                          title="အသုံးပြုသူ ဖျက်ရန်"
                        >
                          🗑️
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Column 3: Database Management */}
        <div className="lg:col-span-1">
          <div className="bg-red-900/30 backdrop-blur-sm rounded-xl p-4 border border-red-500/30 h-fit">
            <h3 className="text-sm font-bold text-red-300 mb-3">🗄️ Database စီမံခန့်ခွဲမှု</h3>

            <div className="bg-red-800/20 rounded p-2 mb-3">
              <h4 className="text-xs font-bold text-red-200 mb-1">⚠️ သတိပေးချက်</h4>
              <p className="text-xs text-red-300 mb-1">
                Database reset လုပ်ခြင်းသည် အောက်ပါ data များကို လုံးဝ ဖျက်သိမ်းပါမည်:
              </p>
              <ul className="text-xs text-red-300 space-y-0.5 ml-3">
                <li>• အော်ဒါများ အကုန်လုံး</li>
                <li>• ဖောက်သည် အချက်အလက်များ</li>
                <li>• ရောင်းအား မှတ်တမ်းများ</li>
                <li>• ဝင်ငွေ/ထွက်ငွေ မှတ်တမ်းများ</li>
                <li>• Custom menu items</li>
              </ul>
            </div>

            <button
              type="button"
              onClick={handleResetDatabase}
              disabled={resetting}
              className="w-full bg-red-500 hover:bg-red-600 disabled:bg-red-500/50 disabled:cursor-not-allowed text-white px-3 py-2 rounded transition-colors text-xs font-medium"
            >
              {resetting ? (
                <span className="flex items-center justify-center gap-1">
                  <span className="animate-spin">🔄</span>
                  Reset လုပ်နေ...
                </span>
              ) : (
                '🗑️ Database Reset'
              )}
            </button>
          </div>
        </div>
      </div>



      {/* User Form Modal */}
      {showUserForm && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-xl p-6 w-full max-w-md border border-gray-700">
            <h3 className="text-lg font-bold text-white mb-4">အသုံးပြုသူ အသစ် ဖန်တီးရန်</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  အမည်
                </label>
                <input
                  type="text"
                  value={newUser.name}
                  onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400"
                  placeholder="အမည် ရိုက်ထည့်ပါ"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  အသုံးပြုသူအမည်
                </label>
                <input
                  type="text"
                  value={newUser.username}
                  onChange={(e) => setNewUser({...newUser, username: e.target.value})}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400"
                  placeholder="အသုံးပြုသူအမည် ရိုက်ထည့်ပါ"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  စကားဝှက်
                </label>
                <input
                  type="password"
                  value={newUser.password}
                  onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400"
                  placeholder="စကားဝှက် ရိုက်ထည့်ပါ"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  အခန်းကဏ္ဍ
                </label>
                <select
                  value={newUser.role}
                  onChange={(e) => setNewUser({...newUser, role: e.target.value})}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"
                >
                  <option value="counter">ကောင်တာ</option>
                  <option value="kitchen">မီးဖိုချောင်</option>
                  <option value="admin">အက်ဒမင်</option>
                </select>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowUserForm(false);
                  setNewUser({ username: '', password: '', role: 'counter', name: '' });
                }}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded transition-colors"
              >
                မလုပ်တော့ပါ
              </button>
              <button
                onClick={handleCreateUser}
                className="flex-1 bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded transition-colors"
              >
                ဖန်တီးရန်
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Password Change Modal */}
      {showPasswordForm && editingUser && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-xl p-6 w-full max-w-md border border-gray-700">
            <h3 className="text-lg font-bold text-white mb-4">
              {editingUser.name} ၏ စကားဝှက် ပြောင်းလဲရန်
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  စကားဝှက် အသစ်
                </label>
                <input
                  type="password"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400"
                  placeholder="စကားဝှက် အသစ် ရိုက်ထည့်ပါ (အနည်းဆုံး ၆ လုံး)"
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowPasswordForm(false);
                  setEditingUser(null);
                  setNewPassword('');
                }}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded transition-colors"
              >
                မလုပ်တော့ပါ
              </button>
              <button
                onClick={handleUpdatePassword}
                className="flex-1 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors"
              >
                ပြောင်းလဲရန်
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default function AdminPage() {
  const { isAuthenticated, user, logout } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [tables, setTables] = useState<Table[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [settings, setSettings] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [analyticsData, setAnalyticsData] = useState<any>({});
  const [analyticsLoading, setAnalyticsLoading] = useState(false);
  const [analyticsDateRange, setAnalyticsDateRange] = useState({
    start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end_date: new Date().toISOString().split('T')[0]
  });
  const [financialData, setFinancialData] = useState<any>({});
  const [financialLoading, setFinancialLoading] = useState(false);
  const [expenses, setExpenses] = useState<any[]>([]);
  const [customers, setCustomers] = useState<any[]>([]);
  const [expenseCategories, setExpenseCategories] = useState<any[]>([]);
  const [showAddExpense, setShowAddExpense] = useState(false);
  const [showAddCustomer, setShowAddCustomer] = useState(false);
  const [newExpense, setNewExpense] = useState({
    category: '',
    description: '',
    description_mm: '',
    amount: '',
    expense_date: new Date().toISOString().split('T')[0],
    vendor_name: '',
    vendor_phone: '',
    notes: ''
  });
  const [newCustomer, setNewCustomer] = useState({
    name: '',
    name_mm: '',
    phone: ''
  });
  const [currentTime, setCurrentTime] = useState(new Date());

  // Form states
  const [showTableForm, setShowTableForm] = useState(false);
  const [showCategoryForm, setShowCategoryForm] = useState(false);
  const [showMenuForm, setShowMenuForm] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [menuFilter, setMenuFilter] = useState<'all' | 'today_special' | 'available' | 'unavailable'>('all');
  const [draggedCategory, setDraggedCategory] = useState<Category | null>(null);
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState<number | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleteItemData, setDeleteItemData] = useState<{type: string, id: number, name: string} | null>(null);
  const [showQRCodes, setShowQRCodes] = useState(false);
  const [qrCodes, setQrCodes] = useState<any[]>([]);
  const [networkInfo, setNetworkInfo] = useState<any>({});
  const [salesData, setSalesData] = useState<any>({});
  const [salesLoading, setSalesLoading] = useState(false);
  const [socket, setSocket] = useState<any>(null);

  useEffect(() => {
    // Only redirect if we're sure authentication is loaded and user is not authenticated
    if (isAuthenticated === false) {
      router.push('/login');
      return;
    }

    // Only fetch data if authenticated
    if (isAuthenticated === true) {
      fetchData();
      fetchSalesData();

      // Set up Socket.IO for real-time updates
      const newSocket = io('http://localhost:5000');
      setSocket(newSocket);

      // Listen for menu updates
      newSocket.on('menu-updated', (data) => {
        console.log('Menu updated:', data);
        fetchData(); // Refresh all data
      });

      // Listen for settings updates
      newSocket.on('settings-updated', (data) => {
        console.log('Settings updated:', data);
        setSettings(data);
      });

      // Update time every second
      const timer = setInterval(() => {
        setCurrentTime(new Date());
      }, 1000);

      return () => {
        newSocket.disconnect();
        clearInterval(timer);
      };
    }
  }, [isAuthenticated, router]);

  const fetchData = async () => {
    try {
      setLoading(true);
      console.log('🔄 Fetching data...');

      const [tablesRes, categoriesRes, menuRes, settingsRes] = await Promise.all([
        fetch('http://localhost:5000/api/tables'),
        fetch('http://localhost:5000/api/admin/categories'),
        fetch('http://localhost:5000/api/menu/items'),
        fetch('http://localhost:5000/api/settings')
      ]);

      console.log('📡 Response statuses:', {
        tables: tablesRes.status,
        categories: categoriesRes.status,
        menu: menuRes.status,
        settings: settingsRes.status
      });

      if (tablesRes.ok) {
        const tablesData = await tablesRes.json();
        console.log('📊 Fetched tables:', tablesData.length);
        setTables(tablesData);
      }

      if (categoriesRes.ok) {
        const categoriesData = await categoriesRes.json();
        console.log('📊 Fetched categories:', categoriesData.length, categoriesData);
        setCategories(categoriesData);
      } else {
        console.error('❌ Categories fetch failed:', categoriesRes.status);
      }

      if (menuRes.ok) {
        const menuData = await menuRes.json();
        console.log('📊 Fetched menu items:', menuData.length);
        setMenuItems(menuData);
      }

      if (settingsRes.ok) {
        const settingsData = await settingsRes.json();
        console.log('📊 Fetched settings');
        setSettings(settingsData);
      }

      console.log('✅ Data fetch completed');
    } catch (error) {
      console.error('❌ Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTableSubmit = async (formData: any) => {
    try {
      const url = editingItem
        ? `http://localhost:5000/api/tables/${editingItem.id}`
        : 'http://localhost:5000/api/tables';

      const response = await fetch(url, {
        method: editingItem ? 'PUT' : 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        fetchData();
        setShowTableForm(false);
        setEditingItem(null);
        alert('စားပွဲ အောင်မြင်စွာ သိမ်းဆည်းပြီးပါပြီ!');
      } else {
        alert('စားပွဲ သိမ်းဆည်းရာတွင် အမှားရှိပါသည်။');
      }
    } catch (error) {
      console.error('Error saving table:', error);
      alert('စားပွဲ သိမ်းဆည်းရာတွင် အမှားရှိပါသည်။');
    }
  };

  const handleCategorySubmit = async (formData: any) => {
    try {
      console.log('🔧 Submitting category data:', formData);
      console.log('🔧 Form data icon:', formData.icon);

      // Ensure is_active is included for updates
      const submitData = {
        ...formData,
        is_active: editingItem ? (editingItem.is_active ?? 1) : 1
      };

      console.log('🔧 Final submit data:', submitData);

      const url = editingItem
        ? `http://localhost:5000/api/admin/categories/${editingItem.id}`
        : 'http://localhost:5000/api/admin/categories';

      console.log('📡 Request URL:', url);
      console.log('📡 Request method:', editingItem ? 'PUT' : 'POST');

      const response = await fetch(url, {
        method: editingItem ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(submitData)
      });

      console.log('📡 Response status:', response.status);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Category saved successfully:', result);
        console.log('✅ Saved category icon:', result.category?.icon);

        // Refresh all data to ensure consistency
        await fetchData();

        // Close form
        setShowCategoryForm(false);
        setEditingItem(null);

        alert('အမျိုးအစား အောင်မြင်စွာ သိမ်းဆည်းပြီးပါပြီ!');
      } else {
        const errorData = await response.text();
        console.error('❌ Error response:', errorData);
        alert('အမျိုးအစား သိမ်းဆည်းရာတွင် အမှားရှိပါသည်။ Status: ' + response.status);
      }
    } catch (error) {
      console.error('❌ Error saving category:', error);
      alert('အမျိုးအစား သိမ်းဆည်းရာတွင် အမှားရှိပါသည်။ Error: ' + error.message);
    }
  };

  const handleCategoryReorder = async (draggedId: number, targetId: number) => {
    try {
      const draggedIndex = categories.findIndex(c => c.id === draggedId);
      const targetIndex = categories.findIndex(c => c.id === targetId);

      if (draggedIndex === -1 || targetIndex === -1) return;

      // Create new order
      const newCategories = [...categories];
      const [draggedItem] = newCategories.splice(draggedIndex, 1);
      newCategories.splice(targetIndex, 0, draggedItem);

      // Update sort_order for all affected categories
      const updates = newCategories.map((category, index) => ({
        id: category.id,
        sort_order: index + 1
      }));

      // Send batch update to server
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:5000/api/admin/categories/reorder', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ updates })
      });

      if (response.ok) {
        fetchData(); // Refresh data
      } else {
        alert('အမျိုးအစားများ ပြန်လည်စီစဉ်ရာတွင် အမှားရှိပါသည်။');
      }
    } catch (error) {
      console.error('Error reordering categories:', error);
      alert('အမျိုးအစားများ ပြန်လည်စီစဉ်ရာတွင် အမှားရှိပါသည်။');
    }
  };

  const handleMenuSubmit = async (formData: any) => {
    try {
      const url = editingItem
        ? `http://localhost:5000/api/menu/items/${editingItem.id}`
        : 'http://localhost:5000/api/menu/items';

      const response = await fetch(url, {
        method: editingItem ? 'PUT' : 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        fetchData();
        setShowMenuForm(false);
        setEditingItem(null);
        alert('မီနူး အောင်မြင်စွာ သိမ်းဆည်းပြီးပါပြီ!');
      } else {
        alert('မီနူး သိမ်းဆည်းရာတွင် အမှားရှိပါသည်။');
      }
    } catch (error) {
      console.error('Error saving menu item:', error);
      alert('မီနူး သိမ်းဆည်းရာတွင် အမှားရှိပါသည်။');
    }
  };

  const deleteItem = (type: string, id: number, name?: string) => {
    const itemNames = {
      table: 'စားပွဲ',
      category: 'အမျိုးအစား',
      menu: 'မီနူး'
    };

    setDeleteItemData({
      type,
      id,
      name: name || itemNames[type as keyof typeof itemNames]
    });
    setShowDeleteConfirm(true);
  };

  const confirmDelete = async () => {
    if (!deleteItemData) return;

    try {
      const token = localStorage.getItem('token');
      const urls = {
        table: `http://localhost:5000/api/tables/${deleteItemData.id}`,
        category: `http://localhost:5000/api/admin/categories/${deleteItemData.id}`,
        menu: `http://localhost:5000/api/menu/items/${deleteItemData.id}`
      };

      const response = await fetch(urls[deleteItemData.type as keyof typeof urls], {
        method: 'DELETE'
      });

      if (response.ok) {
        // Update state directly instead of full refresh
        if (deleteItemData.type === 'category') {
          setCategories(prevCategories =>
            prevCategories.filter(cat => cat.id !== deleteItemData.id)
          );
        } else if (deleteItemData.type === 'table') {
          setTables(prevTables =>
            prevTables.filter(table => table.id !== deleteItemData.id)
          );
        } else if (deleteItemData.type === 'menu') {
          setMenuItems(prevItems =>
            prevItems.filter(item => item.id !== deleteItemData.id)
          );
        }

        setShowDeleteConfirm(false);
        setDeleteItemData(null);
      } else {
        alert('ဖျက်ရာတွင် အမှားရှိပါသည်။');
      }
    } catch (error) {
      console.error('Error deleting item:', error);
      alert('ဖျက်ရာတွင် အမှားရှိပါသည်။');
    }
  };

  const toggleTodaySpecial = async (id: number, current: boolean) => {
    try {
      const response = await fetch(`http://localhost:5000/api/menu/items/${id}/today-special`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_today_special: !current })
      });

      if (response.ok) {
        // Update local state immediately without refresh
        setMenuItems(prev => prev.map(item =>
          item.id === id ? { ...item, is_today_special: !current } : item
        ));
      }
    } catch (error) {
      console.error('Error toggling today special:', error);
    }
  };

  const updateSettings = async (newSettings: any) => {
    try {
      const response = await fetch('http://localhost:5000/api/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newSettings),
      });

      if (response.ok) {
        setSettings(newSettings);
        // Emit settings update event for real-time sync
        if (socket) {
          socket.emit('settings-updated', newSettings);
        }
        alert('ဆက်တင်များ အောင်မြင်စွာ သိမ်းဆည်းပြီးပါပြီ!');
      } else {
        alert('ဆက်တင်များ သိမ်းဆည်းရာတွင် အမှားရှိပါသည်။');
      }
    } catch (error) {
      console.error('Error updating settings:', error);
      alert('ဆက်တင်များ သိမ်းဆည်းရာတွင် အမှားရှိပါသည်။');
    }
  };

  const handleSettingsChange = (field: string, value: string) => {
    setSettings((prev: any) => ({
      ...prev,
      [field]: value
    }));
  };

  const toggleItemAvailability = async (itemId: number, currentStatus: boolean) => {
    try {
      const response = await fetch(`http://localhost:5000/api/menu/items/${itemId}/availability`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_available: !currentStatus })
      });

      if (response.ok) {
        // Update local state immediately without refresh
        setMenuItems(prev => prev.map(item =>
          item.id === itemId ? { ...item, is_available: !currentStatus } : item
        ));
      } else {
        alert('အမှားရှိပါသည်။ ထပ်မံကြိုးစားပါ။');
      }
    } catch (error) {
      console.error('Error toggling item availability:', error);
      alert('အမှားရှိပါသည်။ ထပ်မံကြိုးစားပါ။');
    }
  };

  const generateQRCodes = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/tables/qr/all');
      if (response.ok) {
        const data = await response.json();
        setQrCodes(data.qr_codes);
        setNetworkInfo({
          network_ip: data.network_ip,
          frontend_port: data.frontend_port
        });
        setShowQRCodes(true);
      } else {
        alert('QR ကုဒ်များ ထုတ်ရာတွင် အမှားရှိပါသည်။');
      }
    } catch (error) {
      console.error('Error generating QR codes:', error);
      alert('QR ကုဒ်များ ထုတ်ရာတွင် အမှားရှိပါသည်။');
    }
  };

  const fetchSalesData = async () => {
    setSalesLoading(true);
    try {
      const response = await fetch('http://localhost:5000/api/reports/sales');
      if (response.ok) {
        const data = await response.json();
        setSalesData(data);
      } else {
        console.error('Error fetching sales data');
      }
    } catch (error) {
      console.error('Error fetching sales data:', error);
    } finally {
      setSalesLoading(false);
    }
  };

  const downloadSalesReport = async (format: 'excel' | 'pdf') => {
    try {
      const response = await fetch(`http://localhost:5000/api/reports/sales/download?format=${format}`, {
        method: 'GET',
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        const today = new Date().toISOString().split('T')[0];
        const filename = `sales_report_${today}.${format === 'excel' ? 'xlsx' : 'pdf'}`;
        link.download = filename;

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } else {
        alert('Report download မှာ အမှားရှိပါသည်။');
      }
    } catch (error) {
      console.error('Error downloading report:', error);
      alert('Report download မှာ အမှားရှိပါသည်။');
    }
  };

  // Analytics functions
  const fetchAnalyticsData = async () => {
    setAnalyticsLoading(true);
    try {
      const baseUrl = 'http://localhost:5000/api/analytics';
      const params = new URLSearchParams(analyticsDateRange);

      const [overview, dailySales, popularItems, hourlyPattern, categoryPerformance, tablePerformance] = await Promise.all([
        fetch(`${baseUrl}/overview?${params}`).then(res => res.json()),
        fetch(`${baseUrl}/daily-sales?${params}`).then(res => res.json()),
        fetch(`${baseUrl}/popular-items?${params}&limit=10`).then(res => res.json()),
        fetch(`${baseUrl}/hourly-pattern?${params}`).then(res => res.json()),
        fetch(`${baseUrl}/category-performance?${params}`).then(res => res.json()),
        fetch(`${baseUrl}/table-performance?${params}`).then(res => res.json())
      ]);

      setAnalyticsData({
        overview,
        dailySales,
        popularItems,
        hourlyPattern,
        categoryPerformance,
        tablePerformance
      });
    } catch (error) {
      console.error('Error fetching analytics:', error);
      alert('Error loading analytics data');
    } finally {
      setAnalyticsLoading(false);
    }
  };

  // Financial functions
  const fetchFinancialData = async () => {
    setFinancialLoading(true);
    try {
      const baseUrl = 'http://localhost:5000/api/financial';
      const params = new URLSearchParams(analyticsDateRange);

      const [overview, profitLoss, expensesList, customersList, categories] = await Promise.all([
        fetch(`${baseUrl}/overview?period=month`).then(res => res.json()),
        fetch(`${baseUrl}/profit-loss?${params}`).then(res => res.json()),
        fetch(`${baseUrl}/expenses?${params}&limit=20`).then(res => res.json()),
        fetch(`${baseUrl}/customers?limit=20`).then(res => res.json()),
        fetch(`${baseUrl}/expense-categories`).then(res => res.json())
      ]);

      setFinancialData({ overview, profitLoss });
      setExpenses(Array.isArray(expensesList) ? expensesList : []);
      setCustomers(Array.isArray(customersList) ? customersList : []);
      setExpenseCategories(Array.isArray(categories) ? categories : []);
    } catch (error) {
      console.error('Error fetching financial data:', error);
      alert('Error loading financial data');
    } finally {
      setFinancialLoading(false);
    }
  };

  const addExpense = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/financial/expenses', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...newExpense,
          category_mm: expenseCategories.find(cat => cat.name === newExpense.category)?.name_mm || newExpense.category,
          created_by: 'admin'
        })
      });

      if (response.ok) {
        alert('ကုန်ကျစရိတ် ထည့်သွင်းပြီးပါပြီ');
        setShowAddExpense(false);
        setNewExpense({
          category: '',
          description: '',
          description_mm: '',
          amount: '',
          expense_date: new Date().toISOString().split('T')[0],
          vendor_name: '',
          vendor_phone: '',
          notes: ''
        });
        fetchFinancialData();
      } else {
        alert('ကုန်ကျစရိတ် ထည့်သွင်းမှု မအောင်မြင်ပါ');
      }
    } catch (error) {
      console.error('Error adding expense:', error);
      alert('ကုန်ကျစရိတ် ထည့်သွင်းမှု မအောင်မြင်ပါ');
    }
  };

  const addCustomer = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/financial/customers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newCustomer)
      });

      if (response.ok) {
        alert('ဖောက်သည် အချက်အလက် ထည့်သွင်းပြီးပါပြီ');
        setShowAddCustomer(false);
        setNewCustomer({ name: '', name_mm: '', phone: '' });
        fetchFinancialData();
      } else {
        alert('ဖောက်သည် အချက်အလက် ထည့်သွင်းမှု မအောင်မြင်ပါ');
      }
    } catch (error) {
      console.error('Error adding customer:', error);
      alert('ဖောက်သည် အချက်အလက် ထည့်သွင်းမှု မအောင်မြင်ပါ');
    }
  };

  const downloadQRCode = (qrCodeUrl: string, tableName: string) => {
    const link = document.createElement('a');
    link.href = qrCodeUrl;
    link.download = `QR_${tableName}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const printQRCodes = () => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const printContent = `
        <html>
          <head>
            <title>QR Codes - ${typeof settings.restaurant_name === 'object' ? settings.restaurant_name?.value || 'Restaurant' : settings.restaurant_name || 'Restaurant'}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .qr-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; }
              .qr-item { text-align: center; page-break-inside: avoid; border: 1px solid #ddd; padding: 15px; border-radius: 8px; }
              .qr-item img { max-width: 200px; height: auto; }
              .qr-item h3 { margin: 10px 0 5px 0; font-size: 18px; }
              .qr-item p { margin: 5px 0; font-size: 12px; color: #666; }
              @media print { .qr-grid { grid-template-columns: repeat(2, 1fr); } }
            </style>
          </head>
          <body>
            <h1 style="text-align: center; margin-bottom: 30px;">
              ${typeof settings.restaurant_name === 'object' ? settings.restaurant_name?.value || 'Restaurant' : settings.restaurant_name || 'Restaurant'} - QR Codes
            </h1>
            <div class="qr-grid">
              ${qrCodes.map(qr => `
                <div class="qr-item">
                  <img src="${qr.qr_code_url}" alt="QR Code for ${qr.table.table_name}" />
                  <h3>${qr.table.table_name}</h3>
                  <p>Table: ${qr.table.table_number}</p>
                  <p>Capacity: ${qr.table.capacity} persons</p>
                  <p style="font-size: 10px; word-break: break-all;">${qr.menu_url}</p>
                </div>
              `).join('')}
            </div>
          </body>
        </html>
      `;
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.print();
    }
  };

  const getFilteredMenuItems = () => {
    let filtered = menuItems.filter(m => m.is_active);

    // Filter by category first
    if (selectedCategoryFilter) {
      filtered = filtered.filter(m => m.category_id === selectedCategoryFilter);
    }

    // Then filter by status
    switch (menuFilter) {
      case 'today_special':
        return filtered.filter(m => m.is_today_special);
      case 'available':
        return filtered.filter(m => m.is_available);
      case 'unavailable':
        return filtered.filter(m => !m.is_available);
      default:
        return filtered;
    }
  };

  // Show loading while authentication is being checked or data is being fetched
  if (loading || isAuthenticated === null) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center bg-gray-800 rounded-2xl p-8 border border-gray-700">
          <div className="text-4xl mb-4">⚙️</div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p className="text-gray-300 font-medium">အချက်အလက်များ ရယူနေသည်...</p>
        </div>
      </div>
    );
  }

  // Don't render anything if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-900 relative">
      {/* Food Background Pattern */}
      <div className="fixed inset-0 opacity-10 pointer-events-none">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.2'%3E%3Ctext x='10' y='20' font-size='16'%3E🍜%3C/text%3E%3Ctext x='35' y='35' font-size='12'%3E🍽️%3C/text%3E%3Ctext x='5' y='50' font-size='14'%3E🥘%3C/text%3E%3Ctext x='40' y='15' font-size='10'%3E🍛%3C/text%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '60px 60px'
        }}></div>
      </div>

      {/* Admin Navigation */}
      <header className="bg-gray-800/95 backdrop-blur-sm shadow-2xl border-b border-gray-700 relative z-10">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg">
                <span className="text-white text-xl font-bold">⚙️</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-indigo-400 bg-clip-text text-transparent">
                  Admin Panel
                </h1>
                <p className="text-gray-400 text-sm">စီမံခန့်ခွဲမှု စနစ်</p>
              </div>
            </div>

            {/* Center Navigation Links */}
            <div className="hidden md:flex items-center space-x-4">
              <Link href="/" className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-2 rounded-full hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg text-sm font-medium">
                🏠 ပင်မစာမျက်နှာ
              </Link>
              <Link href="/counter" className="bg-gradient-to-r from-green-600 to-green-700 text-white px-4 py-2 rounded-full hover:from-green-700 hover:to-green-800 transition-all duration-300 shadow-lg text-sm font-medium">
                🧾 ကောင်တာ
              </Link>
              <Link href="/kitchen" className="bg-gradient-to-r from-red-600 to-red-700 text-white px-4 py-2 rounded-full hover:from-red-700 hover:to-red-800 transition-all duration-300 shadow-lg text-sm font-medium">
                👨‍🍳 မီးဖိုချောင်
              </Link>
            </div>

            {/* Right Side - Time, Profile, Logout */}
            <div className="flex items-center space-x-4">
              {/* Time Display */}
              <div className="hidden md:block text-right">
                <p className="text-gray-300 text-sm font-medium">
                  {currentTime.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: true
                  })}
                </p>
                <p className="text-gray-400 text-xs">
                  {currentTime.toLocaleDateString('en-US', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric'
                  })}
                </p>
              </div>

              {/* Profile */}
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">👤</span>
                </div>
                <div className="hidden md:block">
                  <p className="text-gray-300 text-sm font-medium">{user?.username}</p>
                  <p className="text-gray-400 text-xs capitalize">{user?.role}</p>
                </div>
              </div>

              {/* Logout Button */}
              <button
                onClick={logout}
                className="bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 rounded-full hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-lg text-sm font-medium"
              >
                🚪 Logout
              </button>
            </div>
          </div>

          {/* Mobile Navigation Links */}
          <div className="md:hidden mt-4 flex justify-center space-x-2">
            <Link href="/" className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-3 py-2 rounded-full hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg text-xs font-medium">
              🏠 ပင်မ
            </Link>
            <Link href="/counter" className="bg-gradient-to-r from-green-600 to-green-700 text-white px-3 py-2 rounded-full hover:from-green-700 hover:to-green-800 transition-all duration-300 shadow-lg text-xs font-medium">
              🧾 ကောင်တာ
            </Link>
            <Link href="/kitchen" className="bg-gradient-to-r from-red-600 to-red-700 text-white px-3 py-2 rounded-full hover:from-red-700 hover:to-red-800 transition-all duration-300 shadow-lg text-xs font-medium">
              👨‍🍳 မီးဖိုချောင်
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-6">

        {/* Navigation Tabs */}
        <div className="bg-gray-800/80 backdrop-blur-sm rounded-2xl p-2 border border-gray-700 mb-6">
          <div className="flex flex-wrap sm:flex-nowrap gap-1 sm:gap-2">
            {[
              { id: 'dashboard', name: '📊 Dashboard', name_mm: 'ခြုံငုံကြည့်ရှုမှု' },
              { id: 'analytics', name: '📈 Analytics', name_mm: 'ခွဲခြမ်းစိတ်ဖြာမှု' },
              { id: 'financial', name: '💰 Financial', name_mm: 'ငွေကြေးစီမံခန့်ခွဲမှု' },
              { id: 'tables', name: '🪑 Tables', name_mm: 'စားပွဲများ' },
              { id: 'categories', name: '📂 Categories', name_mm: 'အမျိုးအစားများ' },
              { id: 'menu', name: '🍽️ Menu Items', name_mm: 'မီနူးများ' },
              { id: 'settings', name: '⚙️ Settings', name_mm: 'ဆက်တင်များ' }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 min-w-0 py-2 sm:py-3 px-2 sm:px-4 rounded-lg sm:rounded-xl font-medium transition-all duration-300 ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-lg'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <div className="text-xs sm:text-sm truncate">{tab.name}</div>
                <div className="text-xs opacity-80 truncate hidden sm:block">{tab.name_mm}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Dashboard Tab */}
        {activeTab === 'dashboard' && (
          <div className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
              <div className="bg-gray-800/80 backdrop-blur-sm rounded-xl sm:rounded-2xl p-4 sm:p-6 border border-gray-700 text-center hover:bg-gray-700/80 transition-all duration-300">
                <div className="text-2xl sm:text-3xl mb-2">🪑</div>
                <p className="text-xl sm:text-2xl font-bold text-white">{tables.filter(t => t.is_active).length}</p>
                <p className="text-gray-300 text-xs sm:text-sm truncate">စားပွဲများ</p>
              </div>
              <div className="bg-gray-800/80 backdrop-blur-sm rounded-xl sm:rounded-2xl p-4 sm:p-6 border border-gray-700 text-center hover:bg-gray-700/80 transition-all duration-300">
                <div className="text-2xl sm:text-3xl mb-2">📂</div>
                <p className="text-xl sm:text-2xl font-bold text-white">{categories.filter(c => c.is_active).length}</p>
                <p className="text-gray-300 text-xs sm:text-sm truncate">အမျိုးအစားများ</p>
              </div>
              <div className="bg-gray-800/80 backdrop-blur-sm rounded-xl sm:rounded-2xl p-4 sm:p-6 border border-gray-700 text-center hover:bg-gray-700/80 transition-all duration-300">
                <div className="text-2xl sm:text-3xl mb-2">🍽️</div>
                <p className="text-xl sm:text-2xl font-bold text-white">{menuItems.filter(m => m.is_active).length}</p>
                <p className="text-gray-300 text-xs sm:text-sm truncate">မီနူးများ</p>
              </div>
              <div className="bg-gray-800/80 backdrop-blur-sm rounded-xl sm:rounded-2xl p-4 sm:p-6 border border-gray-700 text-center hover:bg-gray-700/80 transition-all duration-300">
                <div className="text-2xl sm:text-3xl mb-2">⭐</div>
                <p className="text-xl sm:text-2xl font-bold text-white">{menuItems.filter(m => m.is_today_special).length}</p>
                <p className="text-gray-300 text-xs sm:text-sm truncate">ယနေ့အထူး</p>
              </div>
            </div>

            {/* Sales Report Section */}
            <div className="bg-gray-800/60 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="bg-green-500/20 rounded-lg p-2">
                    <span className="text-xl">📊</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-white">ရောင်းအား အစီရင်ခံစာ</h3>
                    <p className="text-xs text-gray-400">Sales Report</p>
                  </div>
                </div>
                <button
                  onClick={fetchSalesData}
                  disabled={salesLoading}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors disabled:opacity-50"
                >
                  {salesLoading ? '🔄 ရယူနေသည်...' : '🔄 ပြန်လည်ရယူ'}
                </button>
              </div>

              {/* Sales Summary */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                  <div className="text-2xl mb-2">💰</div>
                  <p className="text-2xl font-bold text-green-400">
                    {salesData.today_total ? `${salesData.today_total.toLocaleString()} ကျပ်` : '0 ကျပ်'}
                  </p>
                  <p className="text-gray-300 text-sm">ယနေ့ ရောင်းအား</p>
                </div>
                <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                  <div className="text-2xl mb-2">📅</div>
                  <p className="text-2xl font-bold text-blue-400">
                    {salesData.month_total ? `${salesData.month_total.toLocaleString()} ကျပ်` : '0 ကျပ်'}
                  </p>
                  <p className="text-gray-300 text-sm">ဒီလ ရောင်းအား</p>
                </div>
                <div className="bg-gray-700/50 rounded-lg p-4 text-center">
                  <div className="text-2xl mb-2">🛒</div>
                  <p className="text-2xl font-bold text-purple-400">
                    {salesData.today_orders || 0}
                  </p>
                  <p className="text-gray-300 text-sm">ယနေ့ အမှာစာ</p>
                </div>
              </div>

              {/* Top Selling Items Table */}
              {salesData.top_items && salesData.top_items.length > 0 && (
                <div className="mb-6">
                  <h4 className="text-sm font-bold text-white mb-3">ယနေ့ အရောင်းရဆုံး မီနူးများ</h4>
                  <div className="bg-gray-700/50 rounded-lg overflow-hidden">
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead className="bg-gray-600/50">
                          <tr>
                            <th className="px-3 py-2 text-left text-gray-300 font-medium">#</th>
                            <th className="px-3 py-2 text-left text-gray-300 font-medium">မီနူး</th>
                            <th className="px-3 py-2 text-center text-gray-300 font-medium">အရေအတွက်</th>
                            <th className="px-3 py-2 text-right text-gray-300 font-medium">ရောင်းအား</th>
                          </tr>
                        </thead>
                        <tbody>
                          {salesData.top_items.slice(0, 5).map((item: any, index: number) => (
                            <tr key={index} className="border-t border-gray-600/30">
                              <td className="px-3 py-2 text-gray-400">{index + 1}</td>
                              <td className="px-3 py-2 text-white">
                                <div>
                                  <div className="font-medium">{item.name_mm}</div>
                                  <div className="text-xs text-gray-400">{item.name}</div>
                                </div>
                              </td>
                              <td className="px-3 py-2 text-center text-blue-400 font-medium">
                                {item.total_quantity}
                              </td>
                              <td className="px-3 py-2 text-right text-green-400 font-medium">
                                {item.total_revenue.toLocaleString()} ကျပ်
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              )}

              {/* Download Buttons */}
              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={() => downloadSalesReport('excel')}
                  className="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
                >
                  📊 Excel ဖိုင် ဒေါင်းလုဒ်
                </button>
                <button
                  onClick={() => downloadSalesReport('pdf')}
                  className="flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
                >
                  📄 PDF ဖိုင် ဒေါင်းလုဒ်
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Analytics Tab */}
        {activeTab === 'analytics' && (
          <div className="space-y-6">
            {/* Analytics Header */}
            <div className="bg-gray-800/60 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="bg-purple-500/20 rounded-lg p-2">
                    <span className="text-xl">📈</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-white">ခွဲခြမ်းစိတ်ဖြာမှု</h3>
                    <p className="text-xs text-gray-400">Advanced Analytics</p>
                  </div>
                </div>
                <button
                  onClick={fetchAnalyticsData}
                  disabled={analyticsLoading}
                  className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm transition-colors disabled:opacity-50"
                >
                  {analyticsLoading ? '🔄 ရယူနေသည်...' : '🔄 ပြန်လည်ရယူ'}
                </button>
              </div>

              {/* Date Range Selector */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    စတင်ရက်စွဲ
                  </label>
                  <input
                    type="date"
                    value={analyticsDateRange.start_date}
                    onChange={(e) => setAnalyticsDateRange(prev => ({ ...prev, start_date: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    ပြီးဆုံးရက်စွဲ
                  </label>
                  <input
                    type="date"
                    value={analyticsDateRange.end_date}
                    onChange={(e) => setAnalyticsDateRange(prev => ({ ...prev, end_date: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                  />
                </div>
              </div>

              {/* Overview Stats */}
              {analyticsData.overview && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-blue-500/20 rounded-lg p-4 text-center">
                    <div className="text-2xl mb-2">🛒</div>
                    <p className="text-xl font-bold text-blue-400">{analyticsData.overview.totalOrders || 0}</p>
                    <p className="text-gray-300 text-xs">စုစုပေါင်း အမှာစာ</p>
                  </div>
                  <div className="bg-green-500/20 rounded-lg p-4 text-center">
                    <div className="text-2xl mb-2">💰</div>
                    <p className="text-xl font-bold text-green-400">
                      {analyticsData.overview.totalRevenue ? `${analyticsData.overview.totalRevenue.toLocaleString()} ကျပ်` : '0 ကျပ်'}
                    </p>
                    <p className="text-gray-300 text-xs">စုစုပေါင်း ရောင်းအား</p>
                  </div>
                  <div className="bg-purple-500/20 rounded-lg p-4 text-center">
                    <div className="text-2xl mb-2">📊</div>
                    <p className="text-xl font-bold text-purple-400">
                      {analyticsData.overview.avgOrderValue ? `${analyticsData.overview.avgOrderValue.toLocaleString()} ကျပ်` : '0 ကျပ်'}
                    </p>
                    <p className="text-gray-300 text-xs">ပျမ်းမျှ အမှာစာ တန်ဖိုး</p>
                  </div>
                  <div className="bg-orange-500/20 rounded-lg p-4 text-center">
                    <div className="text-2xl mb-2">👥</div>
                    <p className="text-xl font-bold text-orange-400">{analyticsData.overview.totalCustomers || 0}</p>
                    <p className="text-gray-300 text-xs">စုစုပေါင်း ဖောက်သည်</p>
                  </div>
                </div>
              )}
            </div>

            {/* Charts Section */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Daily Sales Chart */}
              {analyticsData.dailySales && analyticsData.dailySales.length > 0 && (
                <div className="bg-gray-800/60 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
                  <DailySalesChart data={analyticsData.dailySales} />
                </div>
              )}

              {/* Popular Items Chart */}
              {analyticsData.popularItems && analyticsData.popularItems.length > 0 && (
                <div className="bg-gray-800/60 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
                  <PopularItemsChart data={analyticsData.popularItems} />
                </div>
              )}

              {/* Hourly Pattern Chart */}
              {analyticsData.hourlyPattern && analyticsData.hourlyPattern.length > 0 && (
                <div className="bg-gray-800/60 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
                  <HourlyPatternChart data={analyticsData.hourlyPattern} />
                </div>
              )}

              {/* Category Performance Chart */}
              {analyticsData.categoryPerformance && analyticsData.categoryPerformance.length > 0 && (
                <div className="bg-gray-800/60 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
                  <CategoryPerformanceChart data={analyticsData.categoryPerformance} />
                </div>
              )}
            </div>

            {/* Popular Items Table */}
            {analyticsData.popularItems && analyticsData.popularItems.length > 0 && (
              <div className="bg-gray-800/60 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
                <h4 className="text-lg font-bold text-white mb-4 flex items-center">
                  <span className="mr-2">🏆</span>
                  အရောင်းရဆုံး မီနူးများ (အသေးစိတ်)
                </h4>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead className="bg-gray-700/50">
                      <tr>
                        <th className="px-4 py-3 text-left text-gray-300 font-medium">#</th>
                        <th className="px-4 py-3 text-left text-gray-300 font-medium">မီနူး</th>
                        <th className="px-4 py-3 text-center text-gray-300 font-medium">အရေအတွက်</th>
                        <th className="px-4 py-3 text-center text-gray-300 font-medium">အမှာစာ</th>
                        <th className="px-4 py-3 text-right text-gray-300 font-medium">ရောင်းအား</th>
                        <th className="px-4 py-3 text-center text-gray-300 font-medium">လူကြိုက်များမှု</th>
                      </tr>
                    </thead>
                    <tbody>
                      {analyticsData.popularItems.slice(0, 10).map((item: any, index: number) => (
                        <tr key={index} className="border-t border-gray-600/30 hover:bg-gray-700/30">
                          <td className="px-4 py-3">
                            <div className="bg-yellow-500/20 rounded-full w-6 h-6 flex items-center justify-center">
                              <span className="text-yellow-400 font-bold text-xs">#{index + 1}</span>
                            </div>
                          </td>
                          <td className="px-4 py-3">
                            <div>
                              <div className="font-medium text-white">{item.name_mm}</div>
                              <div className="text-xs text-gray-400">{item.name}</div>
                            </div>
                          </td>
                          <td className="px-4 py-3 text-center">
                            <span className="text-blue-400 font-medium">{item.total_sold} ခု</span>
                          </td>
                          <td className="px-4 py-3 text-center">
                            <span className="text-purple-400 font-medium">{item.order_count} ခု</span>
                          </td>
                          <td className="px-4 py-3 text-right">
                            <span className="text-green-400 font-medium">{item.total_revenue.toLocaleString()} ကျပ်</span>
                          </td>
                          <td className="px-4 py-3 text-center">
                            <div className="flex items-center justify-center">
                              <div className="bg-gray-700 rounded-full h-2 w-16 mr-2">
                                <div
                                  className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full"
                                  style={{ width: `${Math.min(item.popularity_percentage || 0, 100)}%` }}
                                ></div>
                              </div>
                              <span className="text-xs text-gray-300">
                                {item.popularity_percentage ? `${item.popularity_percentage.toFixed(1)}%` : '0%'}
                              </span>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Category Performance Table */}
            {analyticsData.categoryPerformance && analyticsData.categoryPerformance.length > 0 && (
              <div className="bg-gray-800/60 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
                <h4 className="text-lg font-bold text-white mb-4 flex items-center">
                  <span className="mr-2">📂</span>
                  အမျိုးအစား စွမ်းဆောင်ရည် (အသေးစိတ်)
                </h4>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead className="bg-gray-700/50">
                      <tr>
                        <th className="px-4 py-3 text-left text-gray-300 font-medium">အမျိုးအစား</th>
                        <th className="px-4 py-3 text-center text-gray-300 font-medium">အရေအတွက်</th>
                        <th className="px-4 py-3 text-center text-gray-300 font-medium">အမှာစာ</th>
                        <th className="px-4 py-3 text-right text-gray-300 font-medium">ရောင်းအား</th>
                        <th className="px-4 py-3 text-center text-gray-300 font-medium">ရောင်းအား %</th>
                      </tr>
                    </thead>
                    <tbody>
                      {analyticsData.categoryPerformance.map((category: any, index: number) => (
                        <tr key={index} className="border-t border-gray-600/30 hover:bg-gray-700/30">
                          <td className="px-4 py-3">
                            <div className="flex items-center space-x-3">
                              <span className="text-xl">{category.icon || '🍽️'}</span>
                              <div>
                                <div className="font-medium text-white">{category.name_mm}</div>
                                <div className="text-xs text-gray-400">{category.name}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-4 py-3 text-center">
                            <span className="text-blue-400 font-medium">{category.items_sold || 0} ခု</span>
                          </td>
                          <td className="px-4 py-3 text-center">
                            <span className="text-purple-400 font-medium">{category.order_count || 0} ခု</span>
                          </td>
                          <td className="px-4 py-3 text-right">
                            <span className="text-green-400 font-medium">
                              {category.total_revenue ? `${category.total_revenue.toLocaleString()} ကျပ်` : '0 ကျပ်'}
                            </span>
                          </td>
                          <td className="px-4 py-3 text-center">
                            <div className="flex items-center justify-center">
                              <div className="bg-gray-700 rounded-full h-2 w-16 mr-2">
                                <div
                                  className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full"
                                  style={{ width: `${Math.min(category.revenue_percentage || 0, 100)}%` }}
                                ></div>
                              </div>
                              <span className="text-xs text-gray-300">
                                {category.revenue_percentage ? `${category.revenue_percentage.toFixed(1)}%` : '0%'}
                              </span>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Table Performance */}
            {analyticsData.tablePerformance && analyticsData.tablePerformance.length > 0 && (
              <div className="bg-gray-800/60 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
                <h4 className="text-lg font-bold text-white mb-4 flex items-center">
                  <span className="mr-2">🪑</span>
                  စားပွဲ စွမ်းဆောင်ရည်
                </h4>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead className="bg-gray-700/50">
                      <tr>
                        <th className="px-4 py-3 text-left text-gray-300 font-medium">စားပွဲ</th>
                        <th className="px-4 py-3 text-center text-gray-300 font-medium">အမှာစာ</th>
                        <th className="px-4 py-3 text-right text-gray-300 font-medium">ရောင်းအား</th>
                        <th className="px-4 py-3 text-center text-gray-300 font-medium">ပျမ်းမျှ တန်ဖိုး</th>
                        <th className="px-4 py-3 text-center text-gray-300 font-medium">ရောင်းအား %</th>
                      </tr>
                    </thead>
                    <tbody>
                      {analyticsData.tablePerformance.map((table: any, index: number) => (
                        <tr key={index} className="border-t border-gray-600/30 hover:bg-gray-700/30">
                          <td className="px-4 py-3">
                            <div className="flex items-center space-x-2">
                              <span className="text-lg">🪑</span>
                              <span className="font-medium text-white">စားပွဲ {table.table_number}</span>
                            </div>
                          </td>
                          <td className="px-4 py-3 text-center">
                            <span className="text-blue-400 font-medium">{table.order_count} ခု</span>
                          </td>
                          <td className="px-4 py-3 text-right">
                            <span className="text-green-400 font-medium">{table.total_revenue.toLocaleString()} ကျပ်</span>
                          </td>
                          <td className="px-4 py-3 text-center">
                            <span className="text-purple-400 font-medium">{table.avg_order_value.toLocaleString()} ကျပ်</span>
                          </td>
                          <td className="px-4 py-3 text-center">
                            <div className="flex items-center justify-center">
                              <div className="bg-gray-700 rounded-full h-2 w-16 mr-2">
                                <div
                                  className="bg-gradient-to-r from-orange-500 to-red-500 h-2 rounded-full"
                                  style={{ width: `${Math.min(table.revenue_percentage || 0, 100)}%` }}
                                ></div>
                              </div>
                              <span className="text-xs text-gray-300">
                                {table.revenue_percentage ? `${table.revenue_percentage.toFixed(1)}%` : '0%'}
                              </span>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Financial Tab */}
        {activeTab === 'financial' && (
          <div className="space-y-6">
            {/* Financial Header */}
            <div className="bg-gray-800/60 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="bg-green-500/20 rounded-lg p-2">
                    <span className="text-xl">💰</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-white">ငွေကြေးစီမံခန့်ခွဲမှု</h3>
                    <p className="text-xs text-gray-400">Financial Management</p>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setShowAddExpense(true)}
                    className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
                  >
                    💸 ကုန်ကျစရိတ် ထည့်ရန်
                  </button>
                  <button
                    onClick={() => setShowAddCustomer(true)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
                  >
                    👤 ဖောက်သည် ထည့်ရန်
                  </button>
                  <button
                    onClick={fetchFinancialData}
                    disabled={financialLoading}
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm transition-colors disabled:opacity-50"
                  >
                    {financialLoading ? '🔄 ရယူနေသည်...' : '🔄 ပြန်လည်ရယူ'}
                  </button>
                </div>
              </div>

              {/* Financial Overview */}
              {financialData.overview && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-green-500/20 rounded-lg p-4 text-center">
                    <div className="text-2xl mb-2">💰</div>
                    <p className="text-xl font-bold text-green-400">
                      {financialData.overview.revenue ? `${financialData.overview.revenue.toLocaleString()} ကျပ်` : '0 ကျပ်'}
                    </p>
                    <p className="text-gray-300 text-xs">လစဉ် ရောင်းအား</p>
                  </div>
                  <div className="bg-red-500/20 rounded-lg p-4 text-center">
                    <div className="text-2xl mb-2">💸</div>
                    <p className="text-xl font-bold text-red-400">
                      {financialData.overview.expenses ? `${financialData.overview.expenses.toLocaleString()} ကျပ်` : '0 ကျပ်'}
                    </p>
                    <p className="text-gray-300 text-xs">လစဉ် ကုန်ကျစရိတ်</p>
                  </div>
                  <div className="bg-blue-500/20 rounded-lg p-4 text-center">
                    <div className="text-2xl mb-2">📈</div>
                    <p className="text-xl font-bold text-blue-400">
                      {financialData.overview.profit ? `${financialData.overview.profit.toLocaleString()} ကျပ်` : '0 ကျပ်'}
                    </p>
                    <p className="text-gray-300 text-xs">လစဉ် အမြတ်</p>
                  </div>
                  <div className="bg-purple-500/20 rounded-lg p-4 text-center">
                    <div className="text-2xl mb-2">📊</div>
                    <p className="text-xl font-bold text-purple-400">
                      {financialData.overview.profit_margin ? `${financialData.overview.profit_margin.toFixed(1)}%` : '0%'}
                    </p>
                    <p className="text-gray-300 text-xs">အမြတ် ရာခိုင်နှုန်း</p>
                  </div>
                </div>
              )}
            </div>

            {/* Recent Expenses */}
            <div className="bg-gray-800/60 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
              <h4 className="text-lg font-bold text-white mb-4 flex items-center">
                <span className="mr-2">💸</span>
                လတ်တလော ကုန်ကျစရိတ်များ
              </h4>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="bg-gray-700/50">
                    <tr>
                      <th className="px-4 py-3 text-left text-gray-300 font-medium">ရက်စွဲ</th>
                      <th className="px-4 py-3 text-left text-gray-300 font-medium">အမျိုးအစား</th>
                      <th className="px-4 py-3 text-left text-gray-300 font-medium">ဖော်ပြချက်</th>
                      <th className="px-4 py-3 text-right text-gray-300 font-medium">ပမာဏ</th>
                      <th className="px-4 py-3 text-left text-gray-300 font-medium">ရောင်းချသူ</th>
                    </tr>
                  </thead>
                  <tbody>
                    {(Array.isArray(expenses) ? expenses : []).slice(0, 10).map((expense: any, index: number) => (
                      <tr key={index} className="border-t border-gray-600/30 hover:bg-gray-700/30">
                        <td className="px-4 py-3 text-gray-300">
                          {new Date(expense.expense_date).toLocaleDateString('my-MM')}
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex items-center space-x-2">
                            <span className="text-lg">{expense.category_icon || '💰'}</span>
                            <div>
                              <div className="text-white text-sm">{expense.category_name_mm}</div>
                              <div className="text-gray-400 text-xs">{expense.category_name}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          <div className="text-white text-sm">{expense.description_mm || expense.description}</div>
                          <div className="text-gray-400 text-xs">{expense.description}</div>
                        </td>
                        <td className="px-4 py-3 text-right">
                          <span className="text-red-400 font-medium">{expense.amount.toLocaleString()} ကျပ်</span>
                        </td>
                        <td className="px-4 py-3 text-gray-300">
                          {expense.vendor_name || '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Recent Customers */}
            <div className="bg-gray-800/60 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
              <h4 className="text-lg font-bold text-white mb-4 flex items-center">
                <span className="mr-2">👥</span>
                ဖောက်သည် အချက်အလက်များ
              </h4>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="bg-gray-700/50">
                    <tr>
                      <th className="px-4 py-3 text-left text-gray-300 font-medium">အမည်</th>
                      <th className="px-4 py-3 text-left text-gray-300 font-medium">ဖုန်းနံပါတ်</th>
                      <th className="px-4 py-3 text-center text-gray-300 font-medium">အမှာစာ</th>
                      <th className="px-4 py-3 text-right text-gray-300 font-medium">စုစုပေါင်း ကုန်ကျ</th>
                      <th className="px-4 py-3 text-center text-gray-300 font-medium">နောက်ဆုံး အမှာ</th>
                    </tr>
                  </thead>
                  <tbody>
                    {customers.slice(0, 10).map((customer: any, index: number) => (
                      <tr key={index} className="border-t border-gray-600/30 hover:bg-gray-700/30">
                        <td className="px-4 py-3">
                          <div>
                            <div className="text-white text-sm">{customer.name_mm || customer.name || '-'}</div>
                            <div className="text-gray-400 text-xs">{customer.name || '-'}</div>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-blue-400">{customer.phone}</td>
                        <td className="px-4 py-3 text-center">
                          <span className="text-purple-400 font-medium">{customer.total_orders} ခု</span>
                        </td>
                        <td className="px-4 py-3 text-right">
                          <span className="text-green-400 font-medium">{customer.total_spent.toLocaleString()} ကျပ်</span>
                        </td>
                        <td className="px-4 py-3 text-center text-gray-300">
                          {customer.last_order_date ? new Date(customer.last_order_date).toLocaleDateString('my-MM') : '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Tables Tab */}
        {activeTab === 'tables' && (
          <div>
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0 mb-4">
              <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
                <div className="bg-orange-500/20 rounded-lg p-2 flex-shrink-0">
                  <span className="text-lg sm:text-xl">🪑</span>
                </div>
                <div className="min-w-0 flex-1">
                  <h2 className="text-base sm:text-lg font-bold text-white truncate">စားပွဲများ စီမံခန့်ခွဲမှု</h2>
                  <p className="text-xs text-gray-400 truncate">Tables Management</p>
                </div>
                <div className="bg-orange-500/20 text-orange-300 text-xs px-2 py-1 rounded-full flex-shrink-0">
                  {tables.filter(t => t.is_active).length} ခု
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-2">
                <button
                  onClick={generateQRCodes}
                  className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-3 sm:px-4 py-2 rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-300 font-medium shadow-md text-xs sm:text-sm"
                >
                  📱 QR ကုဒ်များ
                </button>
                <button
                  onClick={() => setShowTableForm(true)}
                  className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 sm:px-4 py-2 rounded-lg hover:from-orange-600 hover:to-red-600 transition-all duration-300 font-medium shadow-md text-xs sm:text-sm"
                >
                  + အသစ်ထည့်ရန်
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 2xl:grid-cols-8 gap-3 sm:gap-4">
              {tables.filter(t => t.is_active).map(table => (
                <div key={table.id} className="bg-gray-800/60 backdrop-blur-sm rounded-lg p-3 sm:p-4 border border-gray-700 hover:bg-gray-700/60 transition-all duration-300 hover:border-orange-500/50">
                  <div className="text-center mb-3">
                    <div className="text-3xl sm:text-4xl mb-2">
                      🪑
                    </div>
                    <h3 className="text-sm sm:text-base font-bold text-white line-clamp-1 p-2">{table.table_name}</h3>
                    <p className="text-xs text-gray-300 line-clamp-1">နံပါတ်: {table.table_number}</p>
                    <p className="text-xs text-gray-500 mt-1">ခုံ: {table.capacity} ယောက်</p>
                  </div>

                  {table.qr_code && (
                    <div className="mb-3 p-2 bg-gray-700/50 rounded text-center">
                      <p className="text-xs text-green-400 font-mono truncate">{table.qr_code}</p>
                    </div>
                  )}

                  <div className="flex justify-center space-x-2">
                    <button
                      onClick={() => {
                        setEditingItem(table);
                        setShowTableForm(true);
                      }}
                      className="bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 p-2 rounded transition-colors"
                      title="ပြင်ဆင်ရန်"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                    <button
                      onClick={() => deleteItem('table', table.id, table.table_name)}
                      className="bg-red-500/20 text-red-400 hover:bg-red-500/30 p-2 rounded transition-colors"
                      title="ဖျက်ရန်"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {tables.filter(t => t.is_active).length === 0 && (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🪑</div>
                <h3 className="text-xl font-bold text-gray-300 mb-2">စားပွဲများ မရှိသေးပါ</h3>
                <p className="text-gray-400 mb-4">စားပွဲများ ထည့်သွင်းပါ</p>
                <button
                  onClick={() => setShowTableForm(true)}
                  className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-6 py-3 rounded-lg hover:from-purple-600 hover:to-indigo-600 transition-all duration-300 font-medium shadow-lg"
                >
                  + ပထမဆုံး စားပွဲ ထည့်ရန်
                </button>
              </div>
            )}
          </div>
        )}

        {/* Categories Tab */}
        {activeTab === 'categories' && (
          <div>
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0 mb-4">
              <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
                <div className="bg-purple-500/20 rounded-lg p-2 flex-shrink-0">
                  <span className="text-lg sm:text-xl">📂</span>
                </div>
                <div className="min-w-0 flex-1">
                  <h2 className="text-base sm:text-lg font-bold text-white truncate">အမျိုးအစားများ စီမံခန့်ခွဲမှု</h2>
                  <p className="text-xs text-gray-400 truncate">Categories Management</p>
                </div>
                <div className="bg-purple-500/20 text-purple-300 text-xs px-2 py-1 rounded-full flex-shrink-0">
                  {categories.filter(c => c.is_active).length} ခု
                </div>
              </div>
              <button
                onClick={() => setShowCategoryForm(true)}
                className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-3 sm:px-4 py-2 rounded-lg hover:from-purple-600 hover:to-indigo-600 transition-all duration-300 font-medium shadow-md text-xs sm:text-sm w-full sm:w-auto"
              >
                + အသစ်ထည့်ရန်
              </button>
            </div>

            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto"></div>
                <p className="text-gray-400 mt-2">Loading categories...</p>
              </div>
            ) : categories.filter(c => c.is_active).length === 0 ? (
              <div className="text-center py-8">
                <div className="text-4xl mb-4">📂</div>
                <p className="text-gray-400 mb-4">အမျိုးအစားများ မရှိသေးပါ</p>
                <button
                  onClick={() => setShowCategoryForm(true)}
                  className="bg-purple-500 text-white px-4 py-2 rounded-lg hover:bg-purple-600 transition-colors"
                >
                  ပထမဆုံး အမျိုးအစား ထည့်ရန်
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 2xl:grid-cols-8 gap-3 sm:gap-4">
                {categories.filter(c => c.is_active).map(category => (
                <div
                  key={category.id}
                  className={`bg-gray-800/60 backdrop-blur-sm rounded-lg p-3 sm:p-4 border border-gray-700 hover:bg-gray-700/60 transition-all duration-300 hover:border-purple-500/50 cursor-move select-none ${
                    draggedCategory?.id === category.id ? 'opacity-50 scale-95' : ''
                  }`}
                  draggable
                  onDragStart={(e) => {
                    setDraggedCategory(category);
                    e.dataTransfer.effectAllowed = 'move';
                    e.dataTransfer.setData('text/plain', '');
                    // Add visual feedback
                    e.currentTarget.style.opacity = '0.5';
                  }}
                  onDragOver={(e) => {
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'move';
                    // Add visual feedback for drop zone
                    e.currentTarget.style.borderColor = '#8b5cf6';
                    e.currentTarget.style.borderWidth = '2px';
                  }}
                  onDragLeave={(e) => {
                    // Remove visual feedback when leaving drop zone
                    e.currentTarget.style.borderColor = '';
                    e.currentTarget.style.borderWidth = '';
                  }}
                  onDrop={(e) => {
                    e.preventDefault();
                    // Remove visual feedback
                    e.currentTarget.style.borderColor = '';
                    e.currentTarget.style.borderWidth = '';

                    if (draggedCategory && draggedCategory.id !== category.id) {
                      handleCategoryReorder(draggedCategory.id, category.id);
                    }
                    setDraggedCategory(null);
                  }}
                  onDragEnd={(e) => {
                    // Reset visual feedback
                    e.currentTarget.style.opacity = '';
                    setDraggedCategory(null);
                  }}
                  onMouseDown={(e) => {
                    // Prevent text selection during drag
                    e.preventDefault();
                  }}
                >
                  <div className="text-center mb-3">
                    {/* Drag Handle */}
                    <div className="flex justify-center mb-2 cursor-grab active:cursor-grabbing">
                      <div className="flex flex-col space-y-1 p-1 rounded hover:bg-gray-600/50 transition-colors">
                        <div className="flex space-x-1">
                          <div className="w-1 h-1 bg-gray-500 rounded-full"></div>
                          <div className="w-1 h-1 bg-gray-500 rounded-full"></div>
                          <div className="w-1 h-1 bg-gray-500 rounded-full"></div>
                        </div>
                        <div className="flex space-x-1">
                          <div className="w-1 h-1 bg-gray-500 rounded-full"></div>
                          <div className="w-1 h-1 bg-gray-500 rounded-full"></div>
                          <div className="w-1 h-1 bg-gray-500 rounded-full"></div>
                        </div>
                      </div>
                    </div>

                    <div className="text-3xl sm:text-4xl lg:text-5xl mb-2">
                      {category.icon || '🍽️'}
                    </div>
                    <h3 className="text-sm sm:text-base font-bold text-white line-clamp-1">{category.name_mm}</h3>
                    <p className="text-xs text-gray-300 line-clamp-1">{category.name}</p>
                    <p className="text-xs text-gray-500 mt-1">#{category.sort_order}</p>
                  </div>

                  {category.description && (
                    <p className="text-xs text-gray-400 mb-3 line-clamp-2 text-center">{category.description}</p>
                  )}

                  <div className="flex justify-center space-x-2">
                    <button
                      onClick={() => {
                        setEditingItem(category);
                        setShowCategoryForm(true);
                      }}
                      className="bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 p-2 rounded transition-colors"
                      title="ပြင်ဆင်ရန်"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                    <button
                      onClick={() => deleteItem('category', category.id, category.name_mm)}
                      className="bg-red-500/20 text-red-400 hover:bg-red-500/30 p-2 rounded transition-colors"
                      title="ဖျက်ရန်"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
                ))}
              </div>
            )}


          </div>
        )}

        {/* Menu Items Tab */}
        {activeTab === 'menu' && (
          <div>
            {/* Header Section */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0 mb-6">
              <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
                <div className="bg-green-500/20 rounded-lg p-2 flex-shrink-0">
                  <span className="text-lg sm:text-xl">🍽️</span>
                </div>
                <div className="min-w-0 flex-1">
                  <h2 className="text-base sm:text-lg font-bold text-white truncate">မီနူးများ စီမံခန့်ခွဲမှု</h2>
                  <p className="text-xs text-gray-400 truncate">Menu Items Management</p>
                </div>
                <div className="bg-green-500/20 text-green-300 text-xs px-2 py-1 rounded-full flex-shrink-0">
                  {menuItems.filter(m => m.is_active).length} ခု
                </div>
              </div>

              <button
                onClick={() => setShowMenuForm(true)}
                className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-3 sm:px-4 py-2 rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 font-medium shadow-lg text-xs sm:text-sm w-full sm:w-auto"
              >
                + မီနူးအသစ်
              </button>
            </div>

            {/* Filters Section */}
            <div className="bg-gray-800/60 backdrop-blur-sm rounded-2xl p-4 sm:p-6 border border-gray-700 mb-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
                {/* Category Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    အမျိုးအစားအလိုက် စစ်ထုတ်ရန်
                  </label>
                  <select
                    value={selectedCategoryFilter || ''}
                    onChange={(e) => setSelectedCategoryFilter(e.target.value ? parseInt(e.target.value) : null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white text-sm"
                  >
                    <option value="">အမျိုးအစားအားလုံး</option>
                    {categories.filter(c => c.is_active).map(category => (
                      <option key={category.id} value={category.id}>
                        {category.icon} {category.name_mm} ({category.name})
                      </option>
                    ))}
                  </select>
                </div>

                {/* Status Filters */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    အခြေအနေအလိုက် စစ်ထုတ်ရန်
                  </label>
                  <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
                    <button
                      onClick={() => setMenuFilter('all')}
                      className={`px-2 sm:px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 ${
                        menuFilter === 'all'
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      }`}
                    >
                      <span className="block">အားလုံး</span>
                      <div className="text-xs opacity-75">({getFilteredMenuItems().length})</div>
                    </button>
                    <button
                      onClick={() => setMenuFilter('today_special')}
                      className={`px-2 sm:px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 ${
                        menuFilter === 'today_special'
                          ? 'bg-yellow-600 text-white'
                          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      }`}
                    >
                      <span className="block">⭐ အထူး</span>
                      <div className="text-xs opacity-75">({menuItems.filter(m => m.is_active && m.is_today_special && (!selectedCategoryFilter || m.category_id === selectedCategoryFilter)).length})</div>
                    </button>
                    <button
                      onClick={() => setMenuFilter('available')}
                      className={`px-2 sm:px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 ${
                        menuFilter === 'available'
                          ? 'bg-green-600 text-white'
                          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      }`}
                    >
                      <span className="block">✅ ရရှိနိုင်</span>
                      <div className="text-xs opacity-75">({menuItems.filter(m => m.is_active && m.is_available && (!selectedCategoryFilter || m.category_id === selectedCategoryFilter)).length})</div>
                    </button>
                    <button
                      onClick={() => setMenuFilter('unavailable')}
                      className={`px-2 sm:px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 ${
                        menuFilter === 'unavailable'
                          ? 'bg-red-600 text-white'
                          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      }`}
                    >
                      <span className="block">❌ မရရှိ</span>
                      <div className="text-xs opacity-75">({menuItems.filter(m => m.is_active && !m.is_available && (!selectedCategoryFilter || m.category_id === selectedCategoryFilter)).length})</div>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {getFilteredMenuItems().length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-3 sm:gap-4">
                {getFilteredMenuItems().map(item => (
                  <div key={item.id} className="bg-gray-800/80 backdrop-blur-sm rounded-lg border border-gray-700 hover:bg-gray-700/80 transition-all duration-300 overflow-hidden group">
                    {/* Image Section */}
                    <div className="relative h-48 sm:h-52 md:h-56 lg:h-60 bg-gray-700/50 overflow-hidden">
                      {item.image_url ? (
                        <img
                          src={item.image_url}
                          alt={item.name_mm}
                          className="w-full h-full object-contain object-center group-hover:scale-105 transition-transform duration-300"
                          style={{ objectFit: 'contain', objectPosition: 'center' }}
                          onError={(e) => {
                            const target = e.currentTarget as HTMLImageElement;
                            target.style.display = 'none';
                            const fallback = target.nextElementSibling as HTMLElement;
                            if (fallback) fallback.style.display = 'flex';
                          }}
                        />
                      ) : null}
                      <div
                        className={`absolute inset-0 bg-gradient-to-br from-gray-700/50 to-gray-800/50 flex items-center justify-center ${item.image_url ? 'hidden' : 'flex'}`}
                      >
                        <span className="text-3xl sm:text-4xl opacity-60">🍽️</span>
                      </div>

                      {/* Status Badges */}
                      <div className="absolute top-2 left-2 flex flex-wrap gap-1">
                        {item.is_today_special && (
                          <div className="bg-yellow-500/90 text-yellow-900 text-xs px-2 py-1 rounded-full font-bold shadow-lg flex items-center gap-1">
                            <span>⭐</span>
                            <span className="hidden sm:inline">ယနေ့အထူး</span>
                          </div>
                        )}
                        {!item.is_available && (
                          <div className="bg-red-500/90 text-white text-xs px-2 py-1 rounded-full font-bold shadow-lg flex items-center gap-1">
                            <span>❌</span>
                            <span className="hidden sm:inline">မရရှိ</span>
                          </div>
                        )}
                      </div>

                      {/* Overlay gradient */}
                      <div className="absolute inset-0 bg-gradient-to-t from-gray-900/40 via-transparent to-transparent"></div>
                    </div>

                    {/* Content Section */}
                    <div className="p-3 sm:p-4">
                      <div className="mb-3">
                        <h3 className="font-bold text-white text-sm sm:text-base mb-1 line-clamp-2">{item.name_mm}</h3>
                        <p className="text-xs text-gray-300 mb-2 line-clamp-1">{item.name}</p>
                        <div className="flex justify-between items-center mb-2">
                          <p className="text-base sm:text-lg font-bold text-green-400">{item.price.toLocaleString()} ကျပ်</p>
                        </div>
                        <p className="text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded text-center truncate">{item.category_name_mm}</p>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex flex-col sm:flex-row justify-between items-stretch sm:items-center gap-2">
                        <div className="flex space-x-2 justify-center sm:justify-start">
                          <button
                            onClick={() => toggleTodaySpecial(item.id, item.is_today_special)}
                            className={`text-xs px-3 py-2 rounded transition-colors flex items-center gap-1 ${
                              item.is_today_special
                                ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
                                : 'bg-gray-600 text-gray-400 hover:bg-gray-500'
                            }`}
                            title="ယနေ့အထူး"
                          >
                            <span>⭐</span>
                            <span className="hidden sm:inline">အထူး</span>
                          </button>
                          <button
                            onClick={() => toggleItemAvailability(item.id, item.is_available)}
                            className={`text-xs px-3 py-2 rounded transition-colors flex items-center gap-1 ${
                              item.is_available
                                ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                                : 'bg-red-500/20 text-red-400 border border-red-500/30'
                            }`}
                            title={item.is_available ? 'ရရှိနိုင်သည်' : 'မရရှိပါ'}
                          >
                            <span>{item.is_available ? '✅' : '❌'}</span>
                            <span className="hidden sm:inline">{item.is_available ? 'ရရှိ' : 'မရရှိ'}</span>
                          </button>
                        </div>

                        <div className="flex space-x-2 justify-center sm:justify-end">
                          <button
                            onClick={() => {
                              setEditingItem(item);
                              setShowMenuForm(true);
                            }}
                            className="bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 p-2 rounded transition-colors"
                            title="ပြင်ဆင်ရန်"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </button>
                          <button
                            onClick={() => deleteItem('menu', item.id, item.name_mm)}
                            className="bg-red-500/20 text-red-400 hover:bg-red-500/30 p-2 rounded transition-colors"
                            title="ဖျက်ရန်"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🍽️</div>
                <h3 className="text-xl font-bold text-gray-300 mb-2">မီနူးများ မရှိသေးပါ</h3>
                <p className="text-gray-400 mb-4">အစားအသောက် မီနူးများ ထည့်သွင်းပါ</p>
                <button
                  onClick={() => setShowMenuForm(true)}
                  className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-6 py-3 rounded-lg hover:from-purple-600 hover:to-indigo-600 transition-all duration-300 font-medium shadow-lg"
                >
                  + ပထမဆုံး မီနူး ထည့်ရန်
                </button>
              </div>
            )}
          </div>
        )}

        {/* Settings Tab */}
        {activeTab === 'settings' && (
          <SettingsTab
            settings={settings}
            onSettingsChange={handleSettingsChange}
            onSave={updateSettings}
            tables={tables}
            categories={categories}
            menuItems={menuItems}
            setTables={setTables}
            setCategories={setCategories}
            setMenuItems={setMenuItems}
            setSettings={setSettings}
            fetchData={fetchData}
          />
        )}
      </div>

      {/* Forms */}
      {showTableForm && (
        <TableForm
          table={editingItem}
          onSubmit={handleTableSubmit}
          onClose={() => {
            setShowTableForm(false);
            setEditingItem(null);
          }}
        />
      )}

      {showCategoryForm && (
        <CategoryForm
          category={editingItem}
          onSubmit={handleCategorySubmit}
          onClose={() => {
            setShowCategoryForm(false);
            setEditingItem(null);
          }}
        />
      )}

      {showMenuForm && (
        <MenuForm
          menuItem={editingItem}
          categories={categories}
          onSubmit={handleMenuSubmit}
          onClose={() => {
            setShowMenuForm(false);
            setEditingItem(null);
          }}
        />
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && deleteItemData && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-2xl p-6 w-full max-w-md border border-gray-700 shadow-2xl">
            <div className="text-center">
              <div className="text-5xl sm:text-6xl mb-4">⚠️</div>
              <h3 className="text-lg sm:text-xl font-bold text-white mb-4">
                ဖျက်ရန် သေချာပါသလား?
              </h3>
              <p className="text-gray-300 mb-6 text-sm sm:text-base">
                <span className="font-medium text-red-400">"{deleteItemData.name}"</span> ကို ဖျက်လိုက်ရင်
                ပြန်လည်ရယူ၍ မရနိုင်ပါ။
              </p>

              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={() => {
                    setShowDeleteConfirm(false);
                    setDeleteItemData(null);
                  }}
                  className="flex-1 bg-gray-600 text-gray-300 py-3 px-4 rounded-lg hover:bg-gray-500 transition-colors font-medium text-sm sm:text-base"
                >
                  ပယ်ဖျက်ရန်
                </button>
                <button
                  onClick={confirmDelete}
                  className="flex-1 bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 transition-colors font-medium text-sm sm:text-base"
                >
                  ဖျက်ရန်
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* QR Codes Modal */}
      {showQRCodes && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 rounded-2xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
            <div className="flex justify-between items-center p-6 border-b border-gray-700">
              <div>
                <h2 className="text-xl font-bold text-white">📱 QR ကုဒ်များ</h2>
                <p className="text-gray-300 text-sm mt-1">
                  Network IP: {networkInfo.network_ip}:{networkInfo.frontend_port}
                </p>
              </div>
              <div className="flex gap-2">
                <button
                  onClick={printQRCodes}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm"
                >
                  🖨️ ပုံနှိပ်ရန်
                </button>
                <button
                  onClick={() => setShowQRCodes(false)}
                  className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
                >
                  ✕
                </button>
              </div>
            </div>

            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {qrCodes.map((qr, index) => (
                  <div key={index} className="bg-gray-700/50 rounded-xl p-4 text-center border border-gray-600">
                    <div className="mb-4">
                      <h3 className="text-lg font-bold text-white mb-1">{qr.table.table_name}</h3>
                      <p className="text-gray-300 text-sm">Table: {qr.table.table_number}</p>
                      <p className="text-gray-400 text-xs">Capacity: {qr.table.capacity} persons</p>
                    </div>

                    <div className="bg-white p-4 rounded-lg mb-4 inline-block">
                      <img
                        src={qr.qr_code_url}
                        alt={`QR Code for ${qr.table.table_name}`}
                        className="w-48 h-48 mx-auto"
                      />
                    </div>

                    <div className="space-y-2">
                      <button
                        onClick={() => downloadQRCode(qr.qr_code_url, qr.table.table_name)}
                        className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-sm"
                      >
                        💾 ဒေါင်းလုဒ်
                      </button>
                      <p className="text-xs text-gray-400 break-all bg-gray-800/50 p-2 rounded">
                        {qr.menu_url}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              {qrCodes.length === 0 && (
                <div className="text-center py-12">
                  <div className="text-6xl mb-4">📱</div>
                  <h3 className="text-xl font-bold text-gray-300 mb-2">QR ကုဒ်များ မရှိပါ</h3>
                  <p className="text-gray-400">စားပွဲများ ထည့်ပြီး QR ကုဒ်များ ထုတ်ပါ</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Add Expense Modal */}
      {showAddExpense && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-2xl p-6 w-full max-w-md border border-gray-700">
            <h3 className="text-lg font-bold text-white mb-4">💸 ကုန်ကျစရိတ် ထည့်သွင်းရန်</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-xs font-medium text-gray-300 mb-1">အမျိုးအစား</label>
                <select
                  value={newExpense.category}
                  onChange={(e) => setNewExpense(prev => ({ ...prev, category: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                >
                  <option value="">ရွေးချယ်ပါ</option>
                  {expenseCategories.map(cat => (
                    <option key={cat.id} value={cat.name}>{cat.icon} {cat.name_mm}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-300 mb-1">ဖော်ပြချက် (မြန်မာ)</label>
                <input
                  type="text"
                  value={newExpense.description_mm}
                  onChange={(e) => setNewExpense(prev => ({ ...prev, description_mm: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                  placeholder="ဥပမာ: ဆန်ဝယ်ခြင်း"
                />
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-300 mb-1">ဖော်ပြချက် (English)</label>
                <input
                  type="text"
                  value={newExpense.description}
                  onChange={(e) => setNewExpense(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                  placeholder="e.g. Rice purchase"
                />
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">ပမာဏ (ကျပ်)</label>
                  <input
                    type="number"
                    value={newExpense.amount}
                    onChange={(e) => setNewExpense(prev => ({ ...prev, amount: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                    placeholder="0"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">ရက်စွဲ</label>
                  <input
                    type="date"
                    value={newExpense.expense_date}
                    onChange={(e) => setNewExpense(prev => ({ ...prev, expense_date: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">ရောင်းချသူ အမည်</label>
                  <input
                    type="text"
                    value={newExpense.vendor_name}
                    onChange={(e) => setNewExpense(prev => ({ ...prev, vendor_name: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                    placeholder="ရွေးချယ်ခွင့်ရှိ"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">ဖုန်းနံပါတ်</label>
                  <input
                    type="text"
                    value={newExpense.vendor_phone}
                    onChange={(e) => setNewExpense(prev => ({ ...prev, vendor_phone: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                    placeholder="ရွေးချယ်ခွင့်ရှိ"
                  />
                </div>
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-300 mb-1">မှတ်ချက်</label>
                <textarea
                  value={newExpense.notes}
                  onChange={(e) => setNewExpense(prev => ({ ...prev, notes: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                  rows={2}
                  placeholder="ရွေးချယ်ခွင့်ရှိ"
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setShowAddExpense(false)}
                className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm transition-colors"
              >
                ပယ်ဖျက်ရန်
              </button>
              <button
                onClick={addExpense}
                disabled={!newExpense.category || !newExpense.amount}
                className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm transition-colors disabled:opacity-50"
              >
                ထည့်သွင်းရန်
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Customer Modal */}
      {showAddCustomer && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-2xl p-6 w-full max-w-md border border-gray-700">
            <h3 className="text-lg font-bold text-white mb-4">👤 ဖောက်သည် အချက်အလက် ထည့်သွင်းရန်</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-xs font-medium text-gray-300 mb-1">အမည် (မြန်မာ)</label>
                <input
                  type="text"
                  value={newCustomer.name_mm}
                  onChange={(e) => setNewCustomer(prev => ({ ...prev, name_mm: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                  placeholder="ဥပမာ: မောင်မောင်"
                />
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-300 mb-1">အမည် (English)</label>
                <input
                  type="text"
                  value={newCustomer.name}
                  onChange={(e) => setNewCustomer(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                  placeholder="e.g. Maung Maung"
                />
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-300 mb-1">ဖုန်းနံပါတ် *</label>
                <input
                  type="text"
                  value={newCustomer.phone}
                  onChange={(e) => setNewCustomer(prev => ({ ...prev, phone: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                  placeholder="09xxxxxxxxx"
                  required
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setShowAddCustomer(false)}
                className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm transition-colors"
              >
                ပယ်ဖျက်ရန်
              </button>
              <button
                onClick={addCustomer}
                disabled={!newCustomer.phone}
                className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors disabled:opacity-50"
              >
                ထည့်သွင်းရန်
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
