const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'restaurant.db');
const db = new sqlite3.Database(dbPath);

console.log('Adding analytics tables to database...');

db.serialize(() => {
  // Daily sales summary table
  db.run(`
    CREATE TABLE IF NOT EXISTS daily_sales (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      date DATE UNIQUE NOT NULL,
      total_orders INTEGER DEFAULT 0,
      total_revenue DECIMAL(10,2) DEFAULT 0,
      total_items_sold INTEGER DEFAULT 0,
      average_order_value DECIMAL(10,2) DEFAULT 0,
      peak_hour TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `, (err) => {
    if (err) {
      console.error('Error creating daily_sales table:', err);
    } else {
      console.log('daily_sales table created successfully');
    }
  });

  // Popular items tracking
  db.run(`
    CREATE TABLE IF NOT EXISTS item_analytics (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      menu_item_id INTEGER NOT NULL,
      date DATE NOT NULL,
      quantity_sold INTEGER DEFAULT 0,
      revenue DECIMAL(10,2) DEFAULT 0,
      order_count INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (menu_item_id) REFERENCES menu_items(id),
      UNIQUE(menu_item_id, date)
    )
  `, (err) => {
    if (err) {
      console.error('Error creating item_analytics table:', err);
    } else {
      console.log('item_analytics table created successfully');
    }
  });

  // Hourly sales tracking
  db.run(`
    CREATE TABLE IF NOT EXISTS hourly_sales (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      date DATE NOT NULL,
      hour INTEGER NOT NULL,
      order_count INTEGER DEFAULT 0,
      revenue DECIMAL(10,2) DEFAULT 0,
      items_sold INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(date, hour)
    )
  `, (err) => {
    if (err) {
      console.error('Error creating hourly_sales table:', err);
    } else {
      console.log('hourly_sales table created successfully');
    }
  });

  // Category performance tracking
  db.run(`
    CREATE TABLE IF NOT EXISTS category_analytics (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      category_id INTEGER NOT NULL,
      date DATE NOT NULL,
      items_sold INTEGER DEFAULT 0,
      revenue DECIMAL(10,2) DEFAULT 0,
      order_count INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (category_id) REFERENCES categories(id),
      UNIQUE(category_id, date)
    )
  `, (err) => {
    if (err) {
      console.error('Error creating category_analytics table:', err);
    } else {
      console.log('category_analytics table created successfully');
    }
  });

  // Table performance tracking
  db.run(`
    CREATE TABLE IF NOT EXISTS table_analytics (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      table_number TEXT NOT NULL,
      date DATE NOT NULL,
      order_count INTEGER DEFAULT 0,
      revenue DECIMAL(10,2) DEFAULT 0,
      average_order_value DECIMAL(10,2) DEFAULT 0,
      total_customers INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(table_number, date)
    )
  `, (err) => {
    if (err) {
      console.error('Error creating table_analytics table:', err);
    } else {
      console.log('table_analytics table created successfully');
    }
  });

  // Profit margin tracking (requires cost data)
  db.run(`
    CREATE TABLE IF NOT EXISTS profit_analytics (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      menu_item_id INTEGER NOT NULL,
      date DATE NOT NULL,
      quantity_sold INTEGER DEFAULT 0,
      revenue DECIMAL(10,2) DEFAULT 0,
      cost DECIMAL(10,2) DEFAULT 0,
      profit DECIMAL(10,2) DEFAULT 0,
      profit_margin DECIMAL(5,2) DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (menu_item_id) REFERENCES menu_items(id),
      UNIQUE(menu_item_id, date)
    )
  `, (err) => {
    if (err) {
      console.error('Error creating profit_analytics table:', err);
    } else {
      console.log('profit_analytics table created successfully');
    }
  });

  console.log('Analytics migration completed successfully!');
});

db.close();
