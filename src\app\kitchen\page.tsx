'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../contexts/AuthContext';
import Navigation from '../../components/Navigation';
import { io } from 'socket.io-client';

interface Order {
  id: number;
  customer_name: string;
  table_number?: string;
  total_amount: number;
  status: string;
  created_at: string;
  items: OrderItem[];
}

interface OrderItem {
  id: number;
  name: string;
  name_mm: string;
  quantity: number;
  special_instructions?: string;
  status: string;
  unit_price: number;
  total_price: number;
  cancellation_reason?: string;
  highlighted?: boolean;
}

export default function KitchenPage() {
  const { isAuthenticated } = useAuth();
  const router = useRouter();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [settings, setSettings] = useState<any>({});

  useEffect(() => {
    // Only redirect if we're sure authentication is loaded and user is not authenticated
    if (isAuthenticated === false) {
      router.push('/login');
      return;
    }

    // Only fetch data if authenticated
    if (isAuthenticated === true) {
      fetchOrders();
      fetchSettings();

      // Set up socket connection for real-time updates
      const socket = io('http://192.168.245.140:5000');

      // Join kitchen room
      socket.emit('join-room', 'kitchen');

      // Listen for real-time updates
      socket.on('order-created', (orderData) => {
        console.log('New order received in kitchen:', orderData);
        fetchOrders();
      });

      socket.on('order-status-updated', (data) => {
        console.log('Order status updated in kitchen:', data);
        fetchOrders();
      });

      socket.on('order-item-updated', (data) => {
        console.log('Order item updated in kitchen:', data);
        fetchOrders();
      });

      socket.on('order-item-updated', () => {
        fetchOrders();
      });

      // Fallback polling for reliability
      const interval = setInterval(fetchOrders, 30000);

      return () => {
        socket.disconnect();
        clearInterval(interval);
      };
    }
  }, [isAuthenticated, router]);

  const fetchOrders = async () => {
    try {
      const response = await fetch('http://192.168.245.140:5000/api/orders?group_by_table=true');
      if (response.ok) {
        const data = await response.json();
        // Filter for kitchen relevant table groups and flatten orders
        const kitchenOrders: Order[] = [];
        data.forEach((tableGroup: any) => {
          const relevantOrders = tableGroup.orders?.filter((order: Order) =>
            ['confirmed', 'preparing'].includes(order.status) && order.payment_status !== 'completed'
          ) || [];
          kitchenOrders.push(...relevantOrders);
        });
        setOrders(kitchenOrders);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchSettings = async () => {
    try {
      const response = await fetch('http://192.168.245.140:5000/api/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
    }
  };

  const updateOrderStatus = async (orderId: number, newStatus: string) => {
    // Check if all items are ready before marking order as ready
    if (newStatus === 'ready') {
      const order = orders.find(o => o.id === orderId);
      if (order) {
        const unfinishedItems = order.items.filter(item =>
          item.status !== 'ready' && item.status !== 'cancelled'
        );

        if (unfinishedItems.length > 0) {
          // Highlight unfinished items with red border
          const itemNames = unfinishedItems.map(item => item.name_mm).join(', ');
          alert(`ဟင်းပွဲအားလုံး ချက်ပြီးမှ အမှာစာတစ်ခုလုံး ပြီးပါပြီ ဟု နှိပ်နိုင်ပါသည်။\n\nမပြီးသေးသော ဟင်းပွဲများ: ${itemNames}`);

          // Temporarily highlight unfinished items
          setOrders(prevOrders =>
            prevOrders.map(o =>
              o.id === orderId
                ? {
                    ...o,
                    items: o.items.map(item => ({
                      ...item,
                      highlighted: item.status !== 'ready' && item.status !== 'cancelled'
                    }))
                  }
                : o
            )
          );

          // Remove highlight after 3 seconds
          setTimeout(() => {
            setOrders(prevOrders =>
              prevOrders.map(o =>
                o.id === orderId
                  ? {
                      ...o,
                      items: o.items.map(item => ({
                        ...item,
                        highlighted: false
                      }))
                    }
                  : o
              )
            );
          }, 3000);

          return;
        }
      }
    }

    try {
      const response = await fetch(`http://192.168.245.140:5000/api/orders/${orderId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        fetchOrders(); // Refresh orders
      }
    } catch (error) {
      console.error('Error updating order status:', error);
    }
  };

  const updateOrderItemStatus = async (orderId: number, itemId: number, newStatus: string) => {
    try {
      const response = await fetch(`http://192.168.245.140:5000/api/orders/${orderId}/items/${itemId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        fetchOrders(); // Refresh orders
      }
    } catch (error) {
      console.error('Error updating order item status:', error);
    }
  };

  const getItemStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-600/20 border-yellow-500/30 text-yellow-300';
      case 'confirmed': return 'bg-blue-600/20 border-blue-500/30 text-blue-300';
      case 'preparing': return 'bg-orange-600/20 border-orange-500/30 text-orange-300';
      case 'ready': return 'bg-green-600/20 border-green-500/30 text-green-300';
      case 'served': return 'bg-purple-600/20 border-purple-500/30 text-purple-300';
      default: return 'bg-gray-600/20 border-gray-500/30 text-gray-300';
    }
  };

  const getItemStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '⏳ စောင့်ဆိုင်း';
      case 'confirmed': return '✅ အတည်ပြု';
      case 'preparing': return '🔥 ချက်နေ';
      case 'ready': return '🍽️ အဆင်သင့်';
      case 'served': return '🚚 ပေးပြီး';
      default: return status;
    }
  };

  const getOrderPriority = (createdAt: string) => {
    const orderTime = new Date(createdAt);
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - orderTime.getTime()) / (1000 * 60));
    
    if (diffMinutes > 30) return { level: 'urgent', color: 'bg-red-500', text: '🚨 အရေးကြီး' };
    if (diffMinutes > 15) return { level: 'high', color: 'bg-orange-500', text: '⚡ မြန်မြန်' };
    return { level: 'normal', color: 'bg-green-500', text: '✅ ပုံမှန်' };
  };

  const getTimeSinceOrder = (createdAt: string) => {
    const orderTime = new Date(createdAt);
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - orderTime.getTime()) / (1000 * 60));
    
    if (diffMinutes < 1) return 'အခုလေး';
    if (diffMinutes < 60) return `${diffMinutes} မိနစ်ကြာပြီ`;
    const hours = Math.floor(diffMinutes / 60);
    const minutes = diffMinutes % 60;
    return `${hours} နာရီ ${minutes} မိနစ်ကြာပြီ`;
  };

  // Show loading while authentication is being checked or data is being fetched
  if (loading || isAuthenticated === null) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center bg-gray-800 rounded-2xl p-8 border border-gray-700">
          <div className="text-4xl mb-4">👨‍🍳</div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-300 font-medium">အမှာများ ရယူနေသည်...</p>
        </div>
      </div>
    );
  }

  // Don't render anything if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-900 relative">
      {/* Background Food Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 text-6xl">👨‍🍳</div>
        <div className="absolute top-20 right-20 text-4xl">🍳</div>
        <div className="absolute top-40 left-1/4 text-5xl">🔥</div>
        <div className="absolute top-60 right-1/3 text-3xl">🥘</div>
        <div className="absolute bottom-40 left-20 text-4xl">🍲</div>
        <div className="absolute bottom-20 right-10 text-5xl">🥗</div>
        <div className="absolute bottom-60 left-1/2 text-3xl">🍛</div>
        <div className="absolute top-80 left-1/3 text-4xl">🍝</div>
        <div className="absolute bottom-80 right-1/4 text-3xl">🥙</div>
        <div className="absolute top-1/4 left-1/2 text-5xl">🍕</div>
      </div>

      {/* Navigation */}
      <Navigation
        settings={settings}
        pageTitle="Kitchen Dashboard"
        pageTitleEn="မီးဖိုချောင် စီမံခန့်ခွဲမှု"
      />

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Kitchen Header */}
        <div className="text-center mb-6">
          <div className="flex items-center justify-center mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center text-white text-2xl font-bold shadow-lg">
              👨‍🍳
            </div>
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">မီးဖိုချောင် ကဏ္ဍ</h1>
          <p className="text-sm" style={{ color: 'rgb(95, 109, 135)' }} className="mb-1">Kitchen Department</p>
          <p className="text-sm" style={{ color: 'rgb(95, 109, 135)' }} className="mb-3">ချက်ရမည့် အမှာများကို စီမံခန့်ခွဲပါ</p>
        </div>

        {/* Kitchen Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-gray-800/60 backdrop-blur-sm rounded-xl p-4 border border-gray-700 text-center">
            <div className="text-3xl mb-2">⏳</div>
            <p className="text-white font-bold text-xl">{orders.filter(o => o.status === 'confirmed').length}</p>
            <p style={{ color: 'rgb(95, 109, 135)' }} className="text-sm">စောင့်ဆိုင်းနေသည်</p>
          </div>
          <div className="bg-gray-800/60 backdrop-blur-sm rounded-xl p-4 border border-gray-700 text-center">
            <div className="text-3xl mb-2">🔥</div>
            <p className="text-white font-bold text-xl">{orders.filter(o => o.status === 'preparing').length}</p>
            <p style={{ color: 'rgb(95, 109, 135)' }} className="text-sm">ချက်နေသည်</p>
          </div>
          <div className="bg-gray-800/60 backdrop-blur-sm rounded-xl p-4 border border-gray-700 text-center">
            <div className="text-3xl mb-2">⚡</div>
            <p className="text-white font-bold text-xl">
              {orders.filter(o => {
                const diffMinutes = Math.floor((new Date().getTime() - new Date(o.created_at).getTime()) / (1000 * 60));
                return diffMinutes > 15;
              }).length}
            </p>
            <p style={{ color: 'rgb(95, 109, 135)' }} className="text-sm">အရေးကြီးအမှာများ</p>
          </div>
        </div>

        {/* Orders Grid */}
        {orders.length === 0 ? (
          <div className="text-center py-12">
            <div className="bg-gray-800/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-700">
              <div className="text-6xl mb-4">🍳</div>
              <h2 className="text-xl font-bold text-white mb-2">ချက်ရမည့်အမှာများ မရှိပါ</h2>
              <p style={{ color: 'rgb(95, 109, 135)' }}>အားလုံး ပြီးပါပြီ! 👏</p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {orders
              .sort((a, b) => {
                // Sort by priority (urgent first) then by time
                const aPriority = getOrderPriority(a.created_at);
                const bPriority = getOrderPriority(b.created_at);

                if (aPriority.level !== bPriority.level) {
                  const priorityOrder = { urgent: 3, high: 2, normal: 1 };
                  return priorityOrder[bPriority.level as keyof typeof priorityOrder] -
                         priorityOrder[aPriority.level as keyof typeof priorityOrder];
                }

                return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
              })
              .map((order) => {
                const priority = getOrderPriority(order.created_at);
                return (
                  <div key={order.id} className="bg-gray-800/90 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-700 overflow-hidden hover:border-gray-600 transition-all duration-300">
                    {/* Order Header */}
                    <div className={`${priority.color} p-4 text-white`}>
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-bold text-lg">#{order.id}</h3>
                          <p className="text-white/90 text-sm">{order.customer_name}</p>
                          {order.table_number && (
                            <p className="text-white/90 text-xs">🪑 စားပွဲ: {order.table_number}</p>
                          )}
                        </div>
                        <div className="text-right">
                          <div className="bg-white/20 px-2 py-1 rounded-full mb-1">
                            <span className="font-bold text-xs">{priority.text}</span>
                          </div>
                          <p className="text-white/90 text-xs">{getTimeSinceOrder(order.created_at)}</p>
                          <p className="text-white/90 text-xs">{order.total_amount?.toLocaleString()} ကျပ်</p>
                        </div>
                      </div>
                    </div>

                    {/* Order Items */}
                    <div className="p-6">
                      <h4 className="font-bold text-white mb-4 text-base">🍽️ ချက်ရမည့်အစားအသောက်များ:</h4>
                      <div className="space-y-3 mb-6">
                        {order.items?.map((item: any) => {
                          const isUnfinished = item.status !== 'ready' && item.status !== 'cancelled';
                          const isCompleted = item.status === 'ready';
                          const isCancelled = item.status === 'cancelled';
                          const isHighlighted = item.highlighted;

                          return (
                          <div key={item.id} className={`backdrop-blur-sm rounded-xl p-4 border transition-all duration-300 ${
                            isHighlighted
                              ? 'bg-red-500/30 border-red-400 border-4 animate-pulse'
                              : isCancelled
                                ? 'bg-red-500/20 border-red-500 border-2'
                                : isCompleted
                                  ? 'bg-green-500/20 border-green-500 border-2'
                                  : isUnfinished
                                    ? 'bg-gray-700/60 border-red-500 border-2'
                                    : 'bg-gray-700/60 border-gray-600'
                          }`}>
                            <div className="flex justify-between items-start mb-3">
                              <div className="flex-1">
                                <h5 className="font-bold text-white text-base mb-1">{item.name_mm}</h5>
                                <p className="text-sm mb-2" style={{ color: 'rgb(95, 109, 135)' }}>{item.name}</p>
                                <div className="flex items-center space-x-3">
                                  <span className="bg-blue-600 text-white px-3 py-1 rounded-full font-bold text-sm">
                                    x{item.quantity}
                                  </span>
                                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getItemStatusColor(item.status)}`}>
                                    {getItemStatusText(item.status)}
                                  </span>
                                  {isCancelled && (
                                    <span className="text-red-400 text-sm font-medium">cancelled</span>
                                  )}
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="text-green-400 font-bold text-lg">
                                  {item.total_price?.toLocaleString()} ကျပ်
                                </div>
                              </div>
                            </div>

                            {item.special_instructions && (
                              <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-3 mb-3">
                                <p className="text-yellow-300 text-sm font-medium">
                                  📝 အပိုတောင်းဆိုချက်: {item.special_instructions}
                                </p>
                              </div>
                            )}

                            {/* Customer Cancellation Request Alert */}
                            {item.cancellation_reason && item.cancellation_reason.includes('Customer requested') && item.status !== 'cancelled' && (
                              <div className="bg-orange-500/20 border border-orange-500/30 rounded-lg p-3 mb-3">
                                <p className="text-orange-300 text-sm font-medium">
                                  🚨 ဖောက်သည် ပယ်ဖျက်တောင်းဆိုထားသည်
                                </p>
                                <p className="text-orange-200 text-xs">Counter မှ အတည်ပြုရန် စောင့်ဆိုင်းနေပါသည်</p>
                              </div>
                            )}

                            {/* Item Action Buttons */}
                            <div className="flex gap-2 mt-3">
                              {/* Show buttons based on item status */}
                              {item.status !== 'ready' && item.status !== 'cancelled' ? (
                                <>
                                  <button
                                    onClick={() => updateOrderItemStatus(order.id, item.id, 'ready')}
                                    className="flex-1 bg-green-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors"
                                  >
                                    ✅ အတည်ပြု
                                  </button>
                                  <button
                                    onClick={() => updateOrderItemStatus(order.id, item.id, 'cancelled')}
                                    className="bg-red-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
                                  >
                                    ❌ ပယ်ဖျက်
                                  </button>
                                </>
                              ) : item.status === 'ready' ? (
                                <div className="flex-1 bg-green-600 text-white py-2 px-3 rounded-lg text-sm font-medium opacity-75 text-center">
                                  ✅ အတည်ပြုပြီး
                                </div>
                              ) : item.status === 'cancelled' ? (
                                <div className="flex-1 bg-red-600 text-white py-2 px-3 rounded-lg text-sm font-medium opacity-75 text-center">
                                  ❌ ပယ်ဖျက်ပြီး
                                </div>
                              ) : (
                                // Fallback: show buttons for any other status
                                <>
                                  <button
                                    onClick={() => updateOrderItemStatus(order.id, item.id, 'ready')}
                                    className="flex-1 bg-green-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors"
                                  >
                                    ✅ အတည်ပြု
                                  </button>
                                  <button
                                    onClick={() => updateOrderItemStatus(order.id, item.id, 'cancelled')}
                                    className="bg-red-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
                                  >
                                    ❌ ပယ်ဖျက်
                                  </button>
                                </>
                              )}
                            </div>
                          </div>
                          );
                        })}
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-4">
                        {order.status === 'confirmed' && (
                          <button
                            onClick={() => updateOrderStatus(order.id, 'preparing')}
                            className="flex-1 bg-orange-600 text-white py-3 px-6 rounded-xl font-bold hover:bg-orange-700 transition-colors text-base"
                          >
                            🔥 အမှာစာတစ်ခုလုံး ချက်စပါမယ်
                          </button>
                        )}
                        {order.status === 'preparing' && (
                          <button
                            onClick={() => {
                              const unfinishedItems = order.items.filter(item =>
                                item.status !== 'ready' && item.status !== 'cancelled'
                              );

                              if (unfinishedItems.length > 0) {
                                updateOrderStatus(order.id, 'ready'); // This will show the error
                              } else {
                                updateOrderStatus(order.id, 'ready');
                              }
                            }}
                            className="flex-1 bg-green-600 text-white py-3 px-6 rounded-xl font-bold hover:bg-green-700 transition-colors text-base"
                          >
                            ✅ အမှာစာတစ်ခုလုံး ချက်ပြီးပါပြီ
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
          </div>
        )}
      </div>
    </div>
  );
}
