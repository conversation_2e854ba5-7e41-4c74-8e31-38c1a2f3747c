'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface CartItem {
  menu_item_id: number;
  name: string;
  name_mm: string;
  price: number;
  quantity: number;
  special_instructions?: string;
}

export default function CheckoutPage() {
  const [cart, setCart] = useState<CartItem[]>([]);
  const [customerName, setCustomerName] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [tableNumber, setTableNumber] = useState('');
  const [notes, setNotes] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('cash');
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const savedCart = localStorage.getItem('cart');
    if (savedCart) {
      setCart(JSON.parse(savedCart));
    } else {
      router.push('/menu');
    }
  }, [router]);

  const getTotalAmount = () => {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const handleSubmitOrder = async () => {
    if (!customerName.trim()) {
      alert('ကျေးဇူးပြု၍ နာမည်ကို ဖြည့်ပါ');
      return;
    }

    setLoading(true);

    try {
      const orderData = {
        customer_name: customerName,
        customer_phone: customerPhone,
        table_number: tableNumber,
        items: cart.map(item => ({
          menu_item_id: item.menu_item_id,
          quantity: item.quantity,
          special_instructions: item.special_instructions
        })),
        payment_method: paymentMethod,
        notes: notes
      };

      const response = await fetch('http://localhost:5000/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      });

      if (response.ok) {
        const result = await response.json();
        localStorage.removeItem('cart');
        alert('အမှာပေးပြီးပါပြီ! ကျေးဇူးတင်ပါတယ်');
        router.push('/');
      } else {
        throw new Error('Order submission failed');
      }
    } catch (error) {
      console.error('Error submitting order:', error);
      alert('အမှာပေးရာတွင် အမှားရှိပါတယ်။ ကျေးဇူးပြု၍ ပြန်လည်ကြိုးစားပါ။');
    } finally {
      setLoading(false);
    }
  };

  if (cart.length === 0) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center bg-gray-50 rounded-2xl p-8 border border-gray-200">
          <div className="text-6xl mb-4">🛒</div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">အမှာများ မရှိပါ</h2>
          <p className="text-gray-600 mb-6">မီနူးမှ အစားအသောက်များ ရွေးချယ်ပါ</p>
          <Link
            href="/menu"
            className="bg-gray-600 text-white px-6 py-3 rounded-2xl font-bold hover:bg-gray-700 transition-colors"
          >
            🍽️ မီနူးသို့ ပြန်သွားရန်
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-gray-50 border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 py-3">
          <div className="flex justify-between items-center">
            <Link href="/menu" className="flex items-center space-x-2">
              <span className="text-xl">🔙</span>
              <span className="font-bold text-blue-800">မီနူးသို့ ပြန်သွားရန်</span>
            </Link>
            <h1 className="text-xl font-bold text-blue-800">🧾 အမှာတင်ရန်</h1>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Order Summary */}
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 border border-blue-200 shadow-lg">
            <h2 className="text-xl font-bold text-blue-800 mb-6 text-center">📋 အမှာအကျဉ်း</h2>
            <div className="space-y-4">
              {cart.map((item) => (
                <div key={item.menu_item_id} className="bg-blue-50 rounded-xl p-4 border border-blue-200">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h3 className="font-bold text-blue-800">{item.name_mm}</h3>
                      <p className="text-blue-600 text-sm">{item.name}</p>
                      <p className="text-blue-700 font-medium">{item.price.toLocaleString()} ကျပ် × {item.quantity}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-blue-800 text-lg">
                        {(item.price * item.quantity).toLocaleString()} ကျပ်
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-6 pt-6 border-t border-blue-200">
              <div className="flex justify-between items-center">
                <span className="text-xl font-bold text-blue-800">စုစုပေါင်း:</span>
                <span className="text-2xl font-bold text-blue-800">
                  {getTotalAmount().toLocaleString()} ကျပ်
                </span>
              </div>
            </div>
          </div>

          {/* Customer Information */}
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 border border-blue-200 shadow-lg">
            <h2 className="text-xl font-bold text-blue-800 mb-6 text-center">👤 ဖောက်သည်အချက်အလက်</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-blue-800 font-medium mb-2">နာမည် *</label>
                <input
                  type="text"
                  value={customerName}
                  onChange={(e) => setCustomerName(e.target.value)}
                  className="w-full px-4 py-3 rounded-xl border border-blue-200 focus:border-blue-500 focus:outline-none bg-white/80"
                  placeholder="သင့်နာမည်ကို ရေးပါ"
                  required
                />
              </div>

              <div>
                <label className="block text-orange-800 font-medium mb-2">ဖုန်းနံပါတ်</label>
                <input
                  type="tel"
                  value={customerPhone}
                  onChange={(e) => setCustomerPhone(e.target.value)}
                  className="w-full px-4 py-3 rounded-xl border border-orange-200 focus:border-orange-500 focus:outline-none bg-white/80"
                  placeholder="09xxxxxxxxx"
                />
              </div>

              <div>
                <label className="block text-orange-800 font-medium mb-2">စားပွဲနံပါတ်</label>
                <input
                  type="text"
                  value={tableNumber}
                  onChange={(e) => setTableNumber(e.target.value)}
                  className="w-full px-4 py-3 rounded-xl border border-orange-200 focus:border-orange-500 focus:outline-none bg-white/80"
                  placeholder="စားပွဲနံပါတ် (ရွေးချယ်ရန်)"
                />
              </div>

              <div>
                <label className="block text-orange-800 font-medium mb-2">💳 ငွေပေးချေမှုနည်းလမ်း</label>
                <select
                  value={paymentMethod}
                  onChange={(e) => setPaymentMethod(e.target.value)}
                  className="w-full px-4 py-3 rounded-xl border border-orange-200 focus:border-orange-500 focus:outline-none bg-white/80"
                >
                  <option value="cash">💵 လက်ငင်းငွေ</option>
                  <option value="card">💳 ကတ်</option>
                  <option value="mobile">📱 မိုဘိုင်းငွေ</option>
                </select>
              </div>

              <div>
                <label className="block text-orange-800 font-medium mb-2">📝 အပိုမှတ်ချက်</label>
                <textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  className="w-full px-4 py-3 rounded-xl border border-orange-200 focus:border-orange-500 focus:outline-none bg-white/80 h-24 resize-none"
                  placeholder="အပိုတောင်းဆိုချက်များ..."
                />
              </div>

              <button
                onClick={handleSubmitOrder}
                disabled={loading}
                className={`w-full py-4 px-6 rounded-2xl font-bold text-white transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 ${
                  loading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700'
                }`}
              >
                {loading ? '⏳ အမှာပေးနေသည်...' : '🚀 အမှာတင်မယ်'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
