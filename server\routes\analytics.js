const express = require('express');
const { getDb } = require('../database/db');
const router = express.Router();

// Get daily sales analytics
router.get('/daily-sales', (req, res) => {
  const { start_date, end_date, limit = 30 } = req.query;
  const db = getDb();

  let query = `
    SELECT 
      DATE(created_at) as date,
      COUNT(*) as total_orders,
      SUM(total_amount) as total_revenue,
      AVG(total_amount) as average_order_value,
      MIN(total_amount) as min_order,
      MAX(total_amount) as max_order
    FROM orders 
    WHERE status != 'cancelled'
  `;

  const params = [];

  if (start_date) {
    query += ` AND DATE(created_at) >= ?`;
    params.push(start_date);
  }

  if (end_date) {
    query += ` AND DATE(created_at) <= ?`;
    params.push(end_date);
  }

  query += ` GROUP BY DATE(created_at) ORDER BY date DESC LIMIT ?`;
  params.push(parseInt(limit));

  db.all(query, params, (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error' });
    }
    res.json(rows);
  });
});

// Get popular items analytics
router.get('/popular-items', (req, res) => {
  const { start_date, end_date, limit = 10 } = req.query;
  const db = getDb();

  let query = `
    SELECT 
      mi.id,
      mi.name,
      mi.name_mm,
      mi.price,
      c.name as category_name,
      c.name_mm as category_name_mm,
      SUM(oi.quantity) as total_sold,
      COUNT(DISTINCT oi.order_id) as order_count,
      SUM(oi.total_price) as total_revenue,
      AVG(oi.total_price) as avg_revenue_per_order,
      (SUM(oi.quantity) * 100.0 / (
        SELECT SUM(quantity) FROM order_items oi2 
        JOIN orders o2 ON oi2.order_id = o2.id 
        WHERE o2.status != 'cancelled'
      )) as popularity_percentage
    FROM order_items oi
    JOIN menu_items mi ON oi.menu_item_id = mi.id
    JOIN categories c ON mi.category_id = c.id
    JOIN orders o ON oi.order_id = o.id
    WHERE o.status != 'cancelled' AND oi.status != 'cancelled'
  `;

  const params = [];

  if (start_date) {
    query += ` AND DATE(o.created_at) >= ?`;
    params.push(start_date);
  }

  if (end_date) {
    query += ` AND DATE(o.created_at) <= ?`;
    params.push(end_date);
  }

  query += ` 
    GROUP BY mi.id, mi.name, mi.name_mm, mi.price, c.name, c.name_mm
    ORDER BY total_sold DESC 
    LIMIT ?
  `;
  params.push(parseInt(limit));

  db.all(query, params, (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error' });
    }
    res.json(rows);
  });
});

// Get hourly sales pattern
router.get('/hourly-pattern', (req, res) => {
  const { date } = req.query;
  const db = getDb();

  let query = `
    SELECT 
      CAST(strftime('%H', created_at) AS INTEGER) as hour,
      COUNT(*) as order_count,
      SUM(total_amount) as revenue,
      AVG(total_amount) as avg_order_value
    FROM orders 
    WHERE status != 'cancelled'
  `;

  const params = [];

  if (date) {
    query += ` AND DATE(created_at) = ?`;
    params.push(date);
  } else {
    // Default to last 7 days
    query += ` AND DATE(created_at) >= DATE('now', '-7 days')`;
  }

  query += ` GROUP BY CAST(strftime('%H', created_at) AS INTEGER) ORDER BY hour`;

  db.all(query, params, (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error' });
    }

    // Fill missing hours with 0 values
    const hourlyData = Array.from({ length: 24 }, (_, hour) => {
      const existingData = rows.find(row => row.hour === hour);
      return existingData || {
        hour,
        order_count: 0,
        revenue: 0,
        avg_order_value: 0
      };
    });

    res.json(hourlyData);
  });
});

// Get category performance
router.get('/category-performance', (req, res) => {
  const { start_date, end_date } = req.query;
  const db = getDb();

  let query = `
    SELECT 
      c.id,
      c.name,
      c.name_mm,
      c.icon,
      COUNT(DISTINCT oi.order_id) as order_count,
      SUM(oi.quantity) as items_sold,
      SUM(oi.total_price) as total_revenue,
      AVG(oi.total_price) as avg_revenue_per_item,
      (SUM(oi.total_price) * 100.0 / (
        SELECT SUM(total_price) FROM order_items oi2 
        JOIN orders o2 ON oi2.order_id = o2.id 
        WHERE o2.status != 'cancelled'
      )) as revenue_percentage
    FROM categories c
    LEFT JOIN menu_items mi ON c.id = mi.category_id
    LEFT JOIN order_items oi ON mi.id = oi.menu_item_id
    LEFT JOIN orders o ON oi.order_id = o.id
    WHERE (o.status != 'cancelled' OR o.status IS NULL) 
      AND (oi.status != 'cancelled' OR oi.status IS NULL)
  `;

  const params = [];

  if (start_date) {
    query += ` AND (DATE(o.created_at) >= ? OR o.created_at IS NULL)`;
    params.push(start_date);
  }

  if (end_date) {
    query += ` AND (DATE(o.created_at) <= ? OR o.created_at IS NULL)`;
    params.push(end_date);
  }

  query += ` GROUP BY c.id, c.name, c.name_mm, c.icon ORDER BY total_revenue DESC`;

  db.all(query, params, (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error' });
    }
    res.json(rows);
  });
});

// Get table performance
router.get('/table-performance', (req, res) => {
  const { start_date, end_date } = req.query;
  const db = getDb();

  let query = `
    SELECT 
      table_number,
      COUNT(*) as order_count,
      SUM(total_amount) as total_revenue,
      AVG(total_amount) as avg_order_value,
      MIN(total_amount) as min_order,
      MAX(total_amount) as max_order,
      (SUM(total_amount) * 100.0 / (
        SELECT SUM(total_amount) FROM orders 
        WHERE status != 'cancelled'
      )) as revenue_percentage
    FROM orders 
    WHERE status != 'cancelled' AND table_number IS NOT NULL
  `;

  const params = [];

  if (start_date) {
    query += ` AND DATE(created_at) >= ?`;
    params.push(start_date);
  }

  if (end_date) {
    query += ` AND DATE(created_at) <= ?`;
    params.push(end_date);
  }

  query += ` GROUP BY table_number ORDER BY total_revenue DESC`;

  db.all(query, params, (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error' });
    }
    res.json(rows);
  });
});

// Get overall statistics
router.get('/overview', (req, res) => {
  const { start_date, end_date } = req.query;
  const db = getDb();

  const queries = {
    totalOrders: `SELECT COUNT(*) as count FROM orders WHERE status != 'cancelled'`,
    totalRevenue: `SELECT SUM(total_amount) as total FROM orders WHERE status != 'cancelled'`,
    avgOrderValue: `SELECT AVG(total_amount) as avg FROM orders WHERE status != 'cancelled'`,
    totalItems: `SELECT SUM(oi.quantity) as total FROM order_items oi JOIN orders o ON oi.order_id = o.id WHERE o.status != 'cancelled' AND oi.status != 'cancelled'`,
    totalCustomers: `SELECT COUNT(DISTINCT customer_phone) as count FROM orders WHERE status != 'cancelled' AND customer_phone IS NOT NULL`,
    activeMenuItems: `SELECT COUNT(*) as count FROM menu_items WHERE is_active = 1 AND is_available = 1`,
    activeTables: `SELECT COUNT(*) as count FROM tables WHERE is_active = 1`
  };

  const params = [];
  let dateFilter = '';

  if (start_date) {
    dateFilter += ` AND DATE(created_at) >= ?`;
    params.push(start_date);
  }

  if (end_date) {
    dateFilter += ` AND DATE(created_at) <= ?`;
    params.push(end_date);
  }

  // Add date filters to relevant queries
  if (dateFilter) {
    queries.totalOrders += dateFilter;
    queries.totalRevenue += dateFilter;
    queries.avgOrderValue += dateFilter;
    queries.totalItems += dateFilter.replace('created_at', 'o.created_at');
    queries.totalCustomers += dateFilter;
  }

  const results = {};
  let completed = 0;
  const totalQueries = Object.keys(queries).length;

  Object.entries(queries).forEach(([key, query]) => {
    db.get(query, params, (err, row) => {
      if (err) {
        console.error(`Error in ${key} query:`, err);
        results[key] = 0;
      } else {
        results[key] = row[Object.keys(row)[0]] || 0;
      }

      completed++;
      if (completed === totalQueries) {
        res.json(results);
      }
    });
  });
});

module.exports = router;
