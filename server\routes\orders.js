const express = require('express');
const { getDb } = require('../database/db');
const { v4: uuidv4 } = require('uuid');

const router = express.Router();

// Create new order
router.post('/', (req, res) => {
  const { 
    customer_name, 
    customer_phone, 
    table_number, 
    items, 
    payment_method, 
    notes 
  } = req.body;
  
  if (!items || items.length === 0) {
    return res.status(400).json({ error: 'Items are required' });
  }
  
  const db = getDb();
  
  // Calculate total amount
  let total_amount = 0;
  const orderItems = [];
  
  // Validate items and calculate total
  let itemsProcessed = 0;
  const totalItems = items.length;
  
  items.forEach(item => {
    db.get(
      'SELECT * FROM menu_items WHERE id = ? AND is_active = 1 AND is_available = 1',
      [item.menu_item_id],
      (err, menuItem) => {
        if (err) {
          return res.status(500).json({ error: 'Database error' });
        }
        
        if (!menuItem) {
          return res.status(400).json({ error: `Menu item ${item.menu_item_id} not found or unavailable` });
        }
        
        const itemTotal = menuItem.price * item.quantity;
        total_amount += itemTotal;
        
        orderItems.push({
          menu_item_id: item.menu_item_id,
          quantity: item.quantity,
          unit_price: menuItem.price,
          total_price: itemTotal,
          special_instructions: item.special_instructions || null
        });
        
        itemsProcessed++;
        
        // When all items are processed, create the order
        if (itemsProcessed === totalItems) {
          createOrder();
        }
      }
    );
  });
  
  function createOrder() {
    db.run(
      `INSERT INTO orders (customer_name, customer_phone, table_number, total_amount, payment_method, notes)
       VALUES (?, ?, ?, ?, ?, ?)`,
      [customer_name || 'Walk-in Customer', customer_phone, table_number, total_amount, payment_method || 'cash', notes],
      function(err) {
        if (err) {
          return res.status(500).json({ error: 'Failed to create order' });
        }
        
        const orderId = this.lastID;
        
        // Insert order items
        let itemsInserted = 0;
        
        orderItems.forEach(orderItem => {
          db.run(
            `INSERT INTO order_items (order_id, menu_item_id, quantity, unit_price, total_price, special_instructions) 
             VALUES (?, ?, ?, ?, ?, ?)`,
            [orderId, orderItem.menu_item_id, orderItem.quantity, orderItem.unit_price, orderItem.total_price, orderItem.special_instructions],
            (err) => {
              if (err) {
                return res.status(500).json({ error: 'Failed to create order items' });
              }
              
              itemsInserted++;
              
              if (itemsInserted === orderItems.length) {
                // Get complete order data
                getOrderById(orderId, (orderData) => {
                  // Emit real-time update to all relevant parties
                  const io = req.app.get('io');
                  if (io) {
                    // Notify kitchen and counter
                    io.to('kitchen').emit('order-created', orderData);
                    io.to('counter').emit('order-created', orderData);

                    // Notify customers at the table
                    if (orderData.table_number) {
                      io.to(`table-${orderData.table_number}`).emit('order-created', orderData);
                    }
                  }

                  res.status(201).json({
                    message: 'Order created successfully',
                    order: orderData
                  });
                });
              }
            }
          );
        });
      }
    );
  }
});

// Get all orders
router.get('/', (req, res) => {
  const { status, table_number, limit = 50, group_by_table } = req.query;
  const db = getDb();

  if (group_by_table === 'true') {
    // Group orders by table for customer view
    let query = `
      SELECT
        table_number,
        COUNT(DISTINCT o.id) as order_count,
        SUM(o.total_amount) as total_amount,
        MAX(o.created_at) as latest_order_time,
        GROUP_CONCAT(DISTINCT o.status) as statuses
      FROM orders o
      WHERE o.status != 'delivered' AND o.status != 'cancelled' AND o.payment_status != 'completed'
    `;

    const params = [];

    if (table_number) {
      query += ' AND o.table_number = ?';
      params.push(table_number);
    }

    query += ' GROUP BY table_number ORDER BY latest_order_time DESC';

    db.all(query, params, (err, tableGroups) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      if (tableGroups.length === 0) {
        return res.json([]);
      }

      // Get detailed orders for each table
      let processedTables = 0;
      const tablesWithOrders = [];

      tableGroups.forEach(tableGroup => {
        // Get all orders for this table
        db.all(
          `SELECT o.* FROM orders o
           WHERE o.table_number = ? AND o.status != 'delivered' AND o.status != 'cancelled' AND o.payment_status != 'completed'
           ORDER BY o.created_at DESC`,
          [tableGroup.table_number],
          (err, orders) => {
            if (err) {
              processedTables++;
              if (processedTables === tableGroups.length) {
                res.json(tablesWithOrders);
              }
              return;
            }

            // Get detailed data for each order
            let processedOrders = 0;
            const detailedOrders = [];

            if (orders.length === 0) {
              processedTables++;
              if (processedTables === tableGroups.length) {
                res.json(tablesWithOrders);
              }
              return;
            }

            orders.forEach(order => {
              getOrderById(order.id, (orderData) => {
                if (orderData) {
                  detailedOrders.push(orderData);
                }
                processedOrders++;

                if (processedOrders === orders.length) {
                  tablesWithOrders.push({
                    table_number: tableGroup.table_number,
                    orders: detailedOrders,
                    total_amount: tableGroup.total_amount,
                    order_count: tableGroup.order_count,
                    latest_order_time: tableGroup.latest_order_time
                  });

                  processedTables++;
                  if (processedTables === tableGroups.length) {
                    res.json(tablesWithOrders);
                  }
                }
              });
            });
          }
        );
      });
    });
  } else {
    // Regular order listing
    let query = `
      SELECT o.*,
             COUNT(oi.id) as item_count,
             GROUP_CONCAT(mi.name || ' x' || oi.quantity) as items_summary
      FROM orders o
      LEFT JOIN order_items oi ON o.id = oi.order_id
      LEFT JOIN menu_items mi ON oi.menu_item_id = mi.id
    `;

    const params = [];
    const conditions = [];

    if (status) {
      conditions.push('o.status = ?');
      params.push(status);
    }

    if (table_number) {
      conditions.push('o.table_number = ?');
      params.push(table_number);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' GROUP BY o.id ORDER BY o.created_at DESC LIMIT ?';
    params.push(parseInt(limit));

    db.all(query, params, (err, orders) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      // Get detailed order data for each order
      if (orders.length === 0) {
        return res.json([]);
      }

      let processedOrders = 0;
      const detailedOrders = [];

      orders.forEach(order => {
        getOrderById(order.id, (orderData) => {
          if (orderData) {
            detailedOrders.push(orderData);
          }
          processedOrders++;

          if (processedOrders === orders.length) {
            // Sort by created_at again
            detailedOrders.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
            res.json(detailedOrders);
          }
        });
      });
    });
  }
});

// Get single order
router.get('/:id', (req, res) => {
  const { id } = req.params;
  
  getOrderById(id, (orderData) => {
    if (!orderData) {
      return res.status(404).json({ error: 'Order not found' });
    }
    res.json(orderData);
  });
});

// Update order status
router.patch('/:id/status', (req, res) => {
  const { id } = req.params;
  const { status } = req.body;
  
  const validStatuses = ['pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled'];
  
  if (!status || !validStatuses.includes(status)) {
    return res.status(400).json({ error: 'Invalid status' });
  }
  
  const db = getDb();
  
  db.run(
    'UPDATE orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    [status, id],
    function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      
      if (this.changes === 0) {
        return res.status(404).json({ error: 'Order not found' });
      }
      
      // Get updated order data
      getOrderById(id, (orderData) => {
        // Emit real-time update to all relevant parties
        const io = req.app.get('io');
        if (io) {
          // Notify kitchen and counter
          io.to('kitchen').emit('order-status-updated', {
            orderId: id,
            status: status,
            order: orderData
          });

          io.to('counter').emit('order-status-updated', {
            orderId: id,
            status: status,
            order: orderData
          });

          // Notify customers at the table
          if (orderData.table_number) {
            io.to(`table-${orderData.table_number}`).emit('order-status-updated', {
              orderId: id,
              status: status,
              order: orderData
            });
          }
        }

        res.json({
          message: 'Order status updated successfully',
          order: orderData
        });
      });
    }
  );
});

// Update order item status
router.patch('/:orderId/items/:itemId/status', (req, res) => {
  const { orderId, itemId } = req.params;
  const { status, kitchen_notes } = req.body;

  const validStatuses = ['pending', 'confirmed', 'preparing', 'ready', 'served', 'cancelled'];

  if (!status || !validStatuses.includes(status)) {
    return res.status(400).json({ error: 'Invalid status' });
  }

  const db = getDb();

  db.run(
    'UPDATE order_items SET status = ?, kitchen_notes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND order_id = ?',
    [status, kitchen_notes || null, itemId, orderId],
    function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      if (this.changes === 0) {
        return res.status(404).json({ error: 'Order item not found' });
      }

      // Get updated order data
      getOrderById(orderId, (orderData) => {
        // Emit real-time update to all connected clients
        const io = req.app.get('io');
        if (io) {
          // Notify kitchen, counter, and customer
          io.to('kitchen').emit('order-item-updated', {
            orderId: orderId,
            itemId: itemId,
            status: status,
            order: orderData
          });

          io.to('counter').emit('order-item-updated', {
            orderId: orderId,
            itemId: itemId,
            status: status,
            order: orderData
          });

          // Notify customers at the table
          if (orderData.table_number) {
            io.to(`table-${orderData.table_number}`).emit('order-item-updated', {
              orderId: orderId,
              itemId: itemId,
              status: status,
              order: orderData
            });
          }
        }

        res.json({
          message: 'Order item status updated successfully',
          order: orderData
        });
      });
    }
  );
});

// Clear cancellation request for order item
router.patch('/:orderId/items/:itemId/clear-cancellation', (req, res) => {
  const { orderId, itemId } = req.params;
  const db = getDb();

  db.run(
    'UPDATE order_items SET cancellation_reason = NULL WHERE id = ? AND order_id = ?',
    [itemId, orderId],
    function(err) {
      if (err) {
        console.error('Error clearing cancellation request:', err);
        return res.status(500).json({ error: 'Failed to clear cancellation request' });
      }

      if (this.changes === 0) {
        return res.status(404).json({ error: 'Order item not found' });
      }

      res.json({ message: 'Cancellation request cleared successfully' });
    }
  );
});

// Cancel order item
router.patch('/:orderId/items/:itemId/cancel', (req, res) => {
  const { orderId, itemId } = req.params;
  const { reason } = req.body;

  const db = getDb();

  db.run(
    'UPDATE order_items SET status = ?, cancellation_reason = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND order_id = ?',
    ['cancelled', reason || 'Customer cancelled', itemId, orderId],
    function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      if (this.changes === 0) {
        return res.status(404).json({ error: 'Order item not found' });
      }

      // Recalculate order total
      db.get(
        'SELECT SUM(total_price) as new_total FROM order_items WHERE order_id = ? AND status != ?',
        [orderId, 'cancelled'],
        (err, result) => {
          if (err) {
            return res.status(500).json({ error: 'Database error' });
          }

          const newTotal = result.new_total || 0;

          db.run(
            'UPDATE orders SET total_amount = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [newTotal, orderId],
            (err) => {
              if (err) {
                return res.status(500).json({ error: 'Database error' });
              }

              // Get updated order data
              getOrderById(orderId, (orderData) => {
                res.json({
                  message: 'Order item cancelled successfully',
                  order: orderData
                });
              });
            }
          );
        }
      );
    }
  );
});

// Request table clearing
router.patch('/:id/request-clear', (req, res) => {
  const { id } = req.params;

  const db = getDb();

  db.run(
    'UPDATE orders SET table_clear_requested = 1, table_clear_time = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    [id],
    function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      if (this.changes === 0) {
        return res.status(404).json({ error: 'Order not found' });
      }

      // Get updated order data
      getOrderById(id, (orderData) => {
        res.json({
          message: 'Table clearing requested successfully',
          order: orderData
        });
      });
    }
  );
});

// Get orders with table clearing requests
router.get('/table-clear/requests', (req, res) => {
  const db = getDb();

  db.all(
    `SELECT o.*,
            COUNT(oi.id) as item_count,
            GROUP_CONCAT(mi.name_mm || ' x' || oi.quantity) as items_summary
     FROM orders o
     LEFT JOIN order_items oi ON o.id = oi.order_id AND oi.status != 'cancelled'
     LEFT JOIN menu_items mi ON oi.menu_item_id = mi.id
     WHERE o.table_clear_requested = 1 AND o.status != 'delivered'
     GROUP BY o.id
     ORDER BY o.table_clear_time DESC`,
    [],
    (err, orders) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      res.json(orders);
    }
  );
});

// Delete order item completely
router.delete('/:orderId/items/:itemId', (req, res) => {
  const { orderId, itemId } = req.params;
  const db = getDb();

  // First check if the order is still pending
  db.get('SELECT status FROM orders WHERE id = ?', [orderId], (err, order) => {
    if (err) {
      return res.status(500).json({ error: 'Database error' });
    }

    if (!order) {
      return res.status(404).json({ error: 'Order not found' });
    }

    if (order.status !== 'pending') {
      return res.status(400).json({ error: 'Cannot delete items from non-pending orders' });
    }

    // Delete the order item
    db.run('DELETE FROM order_items WHERE id = ? AND order_id = ?', [itemId, orderId], function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      if (this.changes === 0) {
        return res.status(404).json({ error: 'Order item not found' });
      }

      // Recalculate order total
      db.get(
        `SELECT SUM(oi.quantity * oi.price) as total
         FROM order_items oi
         WHERE oi.order_id = ?`,
        [orderId],
        (err, result) => {
          if (err) {
            return res.status(500).json({ error: 'Database error calculating total' });
          }

          const newTotal = result.total || 0;

          if (newTotal === 0) {
            // If no items left, delete the entire order
            db.run('DELETE FROM orders WHERE id = ?', [orderId], (err) => {
              if (err) {
                return res.status(500).json({ error: 'Database error deleting empty order' });
              }
              res.json({ message: 'Order item deleted and empty order removed' });
            });
          } else {
            // Update order total
            db.run(
              'UPDATE orders SET total_amount = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
              [newTotal, orderId],
              (err) => {
                if (err) {
                  return res.status(500).json({ error: 'Database error updating total' });
                }
                res.json({ message: 'Order item deleted successfully' });
              }
            );
          }
        }
      );
    });
  });
});

// Update order status (for cancelling whole orders)
router.patch('/:id', (req, res) => {
  const { id } = req.params;
  const { status } = req.body;
  const db = getDb();

  db.run(
    'UPDATE orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    [status, id],
    function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      if (this.changes === 0) {
        return res.status(404).json({ error: 'Order not found' });
      }

      res.json({ message: 'Order status updated successfully' });
    }
  );
});

// Mark payment as completed
router.patch('/:id/payment-complete', (req, res) => {
  const { id } = req.params;

  const db = getDb();

  db.run(
    'UPDATE orders SET payment_status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    ['paid', id],
    function(err) {
      if (err) {
        console.error('Error updating payment status:', err);
        return res.status(500).json({ error: 'Failed to update payment status' });
      }

      if (this.changes === 0) {
        return res.status(404).json({ error: 'Order not found' });
      }

      // Get updated order data
      getOrderById(id, (orderData) => {
        res.json({
          message: 'Payment status updated successfully',
          order: orderData
        });
      });
    }
  );
});

// Helper function to get complete order data
function getOrderById(orderId, callback) {
  const db = getDb();

  db.get(
    'SELECT * FROM orders WHERE id = ?',
    [orderId],
    (err, order) => {
      if (err || !order) {
        return callback(null);
      }

      db.all(
        `SELECT oi.*, mi.name, mi.name_mm, mi.image_url
         FROM order_items oi
         JOIN menu_items mi ON oi.menu_item_id = mi.id
         WHERE oi.order_id = ?
         ORDER BY oi.created_at ASC`,
        [orderId],
        (err, items) => {
          if (err) {
            return callback(null);
          }

          callback({
            ...order,
            items: items
          });
        }
      );
    }
  );
}

module.exports = router;
