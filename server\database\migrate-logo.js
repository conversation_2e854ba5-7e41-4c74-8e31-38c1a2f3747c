const { getDb } = require('./db');

const addLogoSetting = async () => {
  const db = getDb();
  
  console.log('Adding logo setting to database...');
  
  try {
    // Check if logo setting already exists
    const existingSetting = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM settings WHERE key = ?',
        ['restaurant_logo'],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!existingSetting) {
      // Insert logo setting
      await new Promise((resolve, reject) => {
        db.run(
          'INSERT INTO settings (key, value, description) VALUES (?, ?, ?)',
          ['restaurant_logo', '', 'Restaurant logo image URL'],
          function(err) {
            if (err) {
              console.error('Error inserting logo setting:', err);
              reject(err);
            } else {
              console.log('Logo setting added successfully');
              resolve();
            }
          }
        );
      });
    } else {
      console.log('Logo setting already exists');
    }

    // Add admin user settings and navigation settings
    const adminSettings = [
      { key: 'admin_username', value: 'admin', description: 'Admin username' },
      { key: 'admin_password', value: 'admin123', description: 'Admin password (hashed)' },
      { key: 'admin_email', value: '<EMAIL>', description: 'Admin email' },
      { key: 'restaurant_name_mm', value: 'အရှင်စားသောက်ဆိုင်', description: 'Restaurant name in Myanmar' },
      { key: 'restaurant_name', value: 'A Shin Restaurant', description: 'Restaurant name in English' },
      { key: 'restaurant_icon', value: '🍜', description: 'Restaurant icon emoji' },
      { key: 'restaurant_title_mm', value: 'မြန်မာ့အရသာ', description: 'Restaurant title in Myanmar' },
      { key: 'restaurant_title_en', value: 'Myanmar Traditional Cuisine', description: 'Restaurant title in English' },
      { key: 'opening_hours_mm', value: '၂၄ နာရီ ဖွင့်ထားသည်', description: 'Opening hours in Myanmar' },
      { key: 'opening_hours_en', value: 'Always Open for You', description: 'Opening hours in English' },
      { key: 'address', value: '', description: 'Restaurant address' },
      { key: 'phone', value: '', description: 'Restaurant phone number' },
      { key: 'email', value: '', description: 'Restaurant email' }
    ];

    for (const setting of adminSettings) {
      const existing = await new Promise((resolve, reject) => {
        db.get(
          'SELECT * FROM settings WHERE key = ?',
          [setting.key],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });

      if (!existing) {
        await new Promise((resolve, reject) => {
          db.run(
            'INSERT INTO settings (key, value, description) VALUES (?, ?, ?)',
            [setting.key, setting.value, setting.description],
            function(err) {
              if (err) {
                console.error(`Error inserting ${setting.key}:`, err);
                reject(err);
              } else {
                console.log(`${setting.key} setting added successfully`);
                resolve();
              }
            }
          );
        });
      } else {
        console.log(`${setting.key} setting already exists`);
      }
    }

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
  }
};

// Run migration if this file is executed directly
if (require.main === module) {
  addLogoSetting().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
}

module.exports = { addLogoSetting };
