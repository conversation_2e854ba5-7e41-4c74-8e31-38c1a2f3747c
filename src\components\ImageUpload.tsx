'use client';

import React, { useState, useRef, useEffect } from 'react';

interface ImageUploadProps {
  currentImage?: string;
  onImageChange: (imageUrl: string) => void;
  label?: string;
  className?: string;
}

export default function ImageUpload({ 
  currentImage, 
  onImageChange, 
  label = "ပုံထည့်ရန်",
  className = ""
}: ImageUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [preview, setPreview] = useState<string>(typeof currentImage === 'string' ? currentImage : '');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Update preview when currentImage prop changes
  useEffect(() => {
    setPreview(typeof currentImage === 'string' ? currentImage : '');
  }, [currentImage]);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('ပုံဖိုင်များသာ ရွေးချယ်နိုင်ပါသည်။');
      return;
    }

    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('ပုံဖိုင်သည် 5MB ထက်မကျော်လွန်ရပါ။');
      return;
    }

    setUploading(true);

    try {
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // Upload to server
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('http://localhost:5000/api/upload/image', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        const imageUrl = `http://localhost:5000${result.imageUrl}`;
        onImageChange(imageUrl);
        setPreview(imageUrl);
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      alert('ပုံတင်ရာတွင် အမှားရှိပါသည်။ ထပ်မံကြိုးစားပါ။');
      setPreview(typeof currentImage === 'string' ? currentImage : '');
    } finally {
      setUploading(false);
    }
  };

  const handleRemoveImage = () => {
    setPreview('');
    onImageChange('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <label className="block text-sm font-medium text-gray-300 mb-1">
        {label}
      </label>
      
      <div className="relative">
        {/* Preview Area */}
        <div 
          onClick={handleClick}
          className="w-full h-32 border-2 border-dashed border-gray-600 rounded-lg flex items-center justify-center cursor-pointer hover:border-purple-500 transition-colors bg-gray-700/50"
        >
          {preview && typeof preview === 'string' ? (
            <div className="relative w-full h-full">
              <img 
                src={preview} 
                alt="Preview" 
                className="w-full h-full object-cover rounded-lg"
              />
              <div className="absolute inset-0 bg-black/50 opacity-0 hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                <span className="text-white text-sm">ပုံပြောင်းရန် နှိပ်ပါ</span>
              </div>
            </div>
          ) : (
            <div className="text-center">
              {uploading ? (
                <div className="flex flex-col items-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mb-2"></div>
                  <span className="text-gray-400 text-sm">တင်နေသည်...</span>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <svg className="w-8 h-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <span className="text-gray-400 text-sm">ပုံထည့်ရန် နှိပ်ပါ</span>
                  <span className="text-gray-500 text-xs mt-1">PNG, JPG, GIF (Max 5MB)</span>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Remove Button */}
        {preview && typeof preview === 'string' && !uploading && (
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              handleRemoveImage();
            }}
            className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600 transition-colors"
          >
            ×
          </button>
        )}

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>

      {/* URL Input Alternative */}
      <div className="text-center text-gray-400 text-xs">
        သို့မဟုတ်
      </div>
      
      <input
        type="url"
        placeholder="ပုံ URL ထည့်ပါ"
        value={typeof preview === 'string' && preview.startsWith('http') ? preview : ''}
        onChange={(e) => {
          const url = e.target.value;
          setPreview(url);
          onImageChange(url);
        }}
        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 text-sm"
      />
    </div>
  );
}
