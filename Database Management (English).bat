@echo off
title Step 3 - Restaurant Management System Database Manager
color 0B

echo.
echo ================================================================
echo                Step 3 - Restaurant Management System
echo                      Database Manager
echo ================================================================
echo.

:MENU
echo Step 3 - Database Management Options:
echo.
echo [1] Check Database Status
echo [2] View Menu Items
echo [3] View Categories
echo [4] View Users
echo [5] View Orders
echo [6] View Tables
echo [7] View Settings
echo [8] Reset Database (Clear All)
echo [9] Backup Database
echo [0] Back to Main Menu
echo.
set /p choice="Select option (0-9): "

if "%choice%"=="1" goto CHECK_STATUS
if "%choice%"=="2" goto VIEW_MENU
if "%choice%"=="3" goto VIEW_CATEGORIES
if "%choice%"=="4" goto VIEW_USERS
if "%choice%"=="5" goto VIEW_ORDERS
if "%choice%"=="6" goto VIEW_TABLES
if "%choice%"=="7" goto VIEW_SETTINGS
if "%choice%"=="8" goto RESET_DATABASE
if "%choice%"=="9" goto BACKUP_DATABASE
if "%choice%"=="0" goto EXIT
goto INVALID

:CHECK_STATUS
echo.
echo Step 3.1 - Database Status Check...
echo =======================================
echo Checking database tables and row counts
cd /d "%~dp0\server"
node -e "const{getDb}=require('./database/db');const db=getDb();console.log('DATABASE STATUS:');console.log('=======================================');const tables=['menu_items','categories','users','orders','tables','settings'];let completed=0;tables.forEach(tableName=>{db.get('SELECT COUNT(*) as count FROM '+tableName,[],(err,result)=>{if(err)console.error('Error:',err);else console.log(tableName+': '+result.count+' rows');completed++;if(completed===tables.length){console.log('\nDatabase status check completed!');process.exit(0);}});});"
pause
goto MENU

:VIEW_MENU
echo.
echo Step 3.2 - Menu Items...
echo =======================================
echo Viewing menu items (latest 20)
cd /d "%~dp0\server"
node -e "const{getDb}=require('./database/db');const db=getDb();db.all('SELECT name,name_mm,price,is_available FROM menu_items ORDER BY id DESC LIMIT 20',[],(err,items)=>{if(err)console.error('Error:',err);else{console.log('MENU ITEMS (Latest 20):');console.log('=======================================');if(items.length===0){console.log('No menu items found');}else{items.forEach((item,index)=>{const status=item.is_available?'Available':'Not Available';console.log((index+1)+'. '+item.name_mm+' ('+item.name+')');console.log('   Price: '+item.price+' MMK - '+status);console.log('');});}}console.log('Menu items display completed!');process.exit(0);});"
pause
goto MENU

:VIEW_CATEGORIES
echo.
echo Step 3.3 - Categories...
echo =======================================
echo Viewing categories with single icons
cd /d "%~dp0\server"
node -e "const{getDb}=require('./database/db');const db=getDb();db.all('SELECT id,name,name_mm,icon,sort_order FROM categories ORDER BY sort_order',[],(err,cats)=>{if(err){console.error('Error:',err);}else{console.log('CATEGORIES:');console.log('=======================================');if(cats.length===0){console.log('No categories found');}else{cats.forEach((cat,index)=>{console.log((index+1)+'. [ID:'+cat.id+'] '+cat.icon+' '+cat.name_mm);console.log('   English: '+cat.name+' | Order: '+cat.sort_order);console.log('');});}}console.log('Categories display completed!');process.exit(0);});"
pause
goto MENU

:VIEW_USERS
echo.
echo Step 3.4 - Users...
echo =======================================
echo Viewing users
cd /d "%~dp0\server"
node -e "const{getDb}=require('./database/db');const db=getDb();db.all('SELECT username,name,role,created_at FROM users ORDER BY created_at DESC',[],(err,users)=>{if(err)console.error('Error:',err);else{console.log('USERS:');console.log('=======================================');if(users.length===0){console.log('No users found');}else{users.forEach((user,index)=>{const date=user.created_at?new Date(user.created_at).toLocaleDateString():'Unknown';console.log((index+1)+'. '+user.name+' (@'+user.username+')');console.log('   Role: '+user.role+' - Created: '+date);console.log('');});}}console.log('Users display completed!');process.exit(0);});"
pause
goto MENU

:VIEW_ORDERS
echo.
echo Step 3.5 - Orders...
echo =======================================
echo Viewing orders (latest 10)
cd /d "%~dp0\server"
node -e "const{getDb}=require('./database/db');const db=getDb();db.all('SELECT id,table_number,customer_name,total_amount,status,created_at FROM orders ORDER BY id DESC LIMIT 10',[],(err,orders)=>{if(err)console.error('Error:',err);else{console.log('ORDERS (Latest 10):');console.log('=======================================');if(orders.length===0){console.log('No orders found');}else{orders.forEach((order,index)=>{const date=new Date(order.created_at).toLocaleString();console.log((index+1)+'. Order #'+order.id+' - '+order.customer_name);console.log('   Table: '+order.table_number+' - Amount: '+order.total_amount+' MMK');console.log('   Status: '+order.status+' - Date: '+date);console.log('');});}}console.log('Orders display completed!');process.exit(0);});"
pause
goto MENU

:VIEW_TABLES
echo.
echo Step 3.6 - Tables...
echo =======================================
echo Viewing tables
cd /d "%~dp0\server"
node -e "const{getDb}=require('./database/db');const db=getDb();db.all('SELECT table_number,table_name,capacity,is_active FROM tables ORDER BY table_number',[],(err,tables)=>{if(err)console.error('Error:',err);else{console.log('TABLES:');console.log('=======================================');if(tables.length===0){console.log('No tables found');}else{tables.forEach((table,index)=>{const status=table.is_active?'Active':'Inactive';console.log((index+1)+'. '+table.table_number+' - '+table.table_name);console.log('   Capacity: '+table.capacity+' seats - '+status);console.log('');});}}console.log('Tables display completed!');process.exit(0);});"
pause
goto MENU

:VIEW_SETTINGS
echo.
echo Step 3.7 - Settings...
echo =======================================
echo Viewing settings
cd /d "%~dp0\server"
node -e "const{getDb}=require('./database/db');const db=getDb();db.all('SELECT key,value FROM settings ORDER BY key',[],(err,settings)=>{if(err)console.error('Error:',err);else{console.log('SETTINGS:');console.log('=======================================');if(settings.length===0){console.log('No settings found');}else{settings.forEach((setting,index)=>{console.log((index+1)+'. '+setting.key+': '+setting.value);});}}console.log('\nSettings display completed!');process.exit(0);});"
pause
goto MENU

:RESET_DATABASE
echo.
echo Step 3.8 - WARNING: Database Reset!
echo =======================================
echo This will DELETE ALL DATA permanently!
echo.
echo What will be deleted:
echo    - All menu items
echo    - All categories
echo    - All users
echo    - All orders
echo    - All tables
echo    - All settings
echo.
echo This action cannot be undone!
echo.
set /p confirm="Type 'YES' to confirm reset: "
if not "%confirm%"=="YES" (
    echo Reset cancelled. Database is safe.
    pause
    goto MENU
)

echo.
echo Resetting database... Please wait...
curl -s -X POST http://localhost:5000/api/admin/reset-database
if %errorlevel%==0 (
    echo Database reset completed successfully!
    echo Database is now completely empty.
) else (
    echo Database reset failed. Make sure server is running.
)
pause
goto MENU

:BACKUP_DATABASE
echo.
echo Step 3.9 - Creating Database Backup...
echo =======================================
echo Creating database backup...
set timestamp=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set timestamp=%timestamp: =0%
set backupfile=database_backup_%timestamp%.db

if exist "server\database\restaurant.db" (
    echo Creating backup folder if not exists...
    if not exist "backups" mkdir backups
    echo Copying database file...
    copy "server\database\restaurant.db" "backups\%backupfile%" >nul 2>&1
    if %errorlevel%==0 (
        echo Database backup created successfully!
        echo Backup file: backups\%backupfile%
    ) else (
        echo Backup failed! Check permissions.
    )
) else (
    echo Database file not found!
    echo Expected location: server\database\restaurant.db
)
pause
goto MENU

:INVALID
echo.
echo Invalid option! Please select 0-9.
pause
goto MENU

:EXIT
echo.
echo Step 3 Complete! Returning to Main Menu...
echo Database Management completed
pause
goto :EOF
