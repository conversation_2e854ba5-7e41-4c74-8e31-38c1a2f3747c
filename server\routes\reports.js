const express = require('express');
const router = express.Router();
const { getDb } = require('../database/db');
const ExcelJS = require('exceljs');
const PDFDocument = require('pdfkit');

// Get sales report data
router.get('/sales', (req, res) => {
  const db = getDb();
  
  const today = new Date().toISOString().split('T')[0];
  const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format
  
  // Get today's sales
  db.get(`
    SELECT 
      COUNT(*) as today_orders,
      COALESCE(SUM(total_amount), 0) as today_total
    FROM orders 
    WHERE DATE(created_at) = ? AND status = 'completed'
  `, [today], (err, todayData) => {
    if (err) {
      console.error('Error fetching today sales:', err);
      return res.status(500).json({ error: 'Database error' });
    }
    
    // Get this month's sales
    db.get(`
      SELECT 
        COUNT(*) as month_orders,
        COALESCE(SUM(total_amount), 0) as month_total
      FROM orders 
      WHERE strftime('%Y-%m', created_at) = ? AND status = 'completed'
    `, [currentMonth], (err, monthData) => {
      if (err) {
        console.error('Error fetching month sales:', err);
        return res.status(500).json({ error: 'Database error' });
      }
      
      // Get top selling items
      db.all(`
        SELECT 
          mi.name_mm,
          mi.name,
          mi.price,
          SUM(oi.quantity) as total_quantity,
          SUM(oi.total_price) as total_revenue
        FROM order_items oi
        JOIN menu_items mi ON oi.menu_item_id = mi.id
        JOIN orders o ON oi.order_id = o.id
        WHERE DATE(o.created_at) = ? AND o.status = 'completed'
        GROUP BY mi.id
        ORDER BY total_quantity DESC
        LIMIT 10
      `, [today], (err, topItems) => {
        if (err) {
          console.error('Error fetching top items:', err);
          return res.status(500).json({ error: 'Database error' });
        }
        
        res.json({
          today_orders: todayData.today_orders || 0,
          today_total: todayData.today_total || 0,
          month_orders: monthData.month_orders || 0,
          month_total: monthData.month_total || 0,
          top_items: topItems || []
        });
      });
    });
  });
});

// Download sales report
router.get('/sales/download', async (req, res) => {
  const { format } = req.query;
  const db = getDb();
  
  const today = new Date().toISOString().split('T')[0];
  const currentMonth = new Date().toISOString().slice(0, 7);
  
  try {
    // Get detailed sales data
    const salesData = await new Promise((resolve, reject) => {
      db.all(`
        SELECT 
          o.id,
          o.table_number,
          o.total_amount,
          o.created_at,
          o.status,
          GROUP_CONCAT(
            mi.name_mm || ' x' || oi.quantity || ' (' || oi.total_price || ' ကျပ်)',
            ', '
          ) as items
        FROM orders o
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN menu_items mi ON oi.menu_item_id = mi.id
        WHERE DATE(o.created_at) = ? AND o.status = 'completed'
        GROUP BY o.id
        ORDER BY o.created_at DESC
      `, [today], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    if (format === 'excel') {
      // Generate Excel file
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Sales Report');
      
      // Add headers
      worksheet.columns = [
        { header: 'အမှာစာ ID', key: 'id', width: 15 },
        { header: 'စားပွဲ', key: 'table_number', width: 15 },
        { header: 'စုစုပေါင်း', key: 'total_amount', width: 20 },
        { header: 'ရက်စွဲ/အချိန်', key: 'created_at', width: 25 },
        { header: 'အခြေအနေ', key: 'status', width: 15 },
        { header: 'မီနူးများ', key: 'items', width: 50 }
      ];
      
      // Add data
      salesData.forEach(row => {
        worksheet.addRow({
          id: row.id,
          table_number: row.table_number,
          total_amount: `${row.total_amount} ကျပ်`,
          created_at: new Date(row.created_at).toLocaleString('my-MM'),
          status: row.status === 'completed' ? 'ပြီးပါပြီ' : row.status,
          items: row.items || ''
        });
      });
      
      // Style the header
      worksheet.getRow(1).font = { bold: true };
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };
      
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename=sales_report_${today}.xlsx`);
      
      await workbook.xlsx.write(res);
      res.end();
      
    } else if (format === 'pdf') {
      // Generate PDF file
      const doc = new PDFDocument({ margin: 50 });
      
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename=sales_report_${today}.pdf`);
      
      doc.pipe(res);
      
      // Add title
      doc.fontSize(20).text('ရောင်းအား အစီရင်ခံစာ', { align: 'center' });
      doc.fontSize(14).text(`ရက်စွဲ: ${new Date(today).toLocaleDateString('my-MM')}`, { align: 'center' });
      doc.moveDown();
      
      // Add summary
      const totalSales = salesData.reduce((sum, row) => sum + row.total_amount, 0);
      doc.fontSize(12);
      doc.text(`စုစုပေါင်း အမှာစာ: ${salesData.length} ခု`);
      doc.text(`စုစုပေါင်း ရောင်းအား: ${totalSales.toLocaleString()} ကျပ်`);
      doc.moveDown();
      
      // Add table header
      doc.text('အမှာစာများ အသေးစိတ်:', { underline: true });
      doc.moveDown();
      
      // Add data
      salesData.forEach((row, index) => {
        doc.text(`${index + 1}. အမှာစာ #${row.id} - စားပွဲ ${row.table_number}`);
        doc.text(`   ပမာණ: ${row.total_amount.toLocaleString()} ကျပ်`);
        doc.text(`   အချိန်: ${new Date(row.created_at).toLocaleString('my-MM')}`);
        if (row.items) {
          doc.text(`   မီနူးများ: ${row.items}`);
        }
        doc.moveDown(0.5);
      });
      
      doc.end();
      
    } else {
      res.status(400).json({ error: 'Invalid format. Use excel or pdf.' });
    }
    
  } catch (error) {
    console.error('Error generating report:', error);
    res.status(500).json({ error: 'Error generating report' });
  }
});

module.exports = router;
