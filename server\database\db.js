const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const bcrypt = require('bcryptjs');

const dbPath = path.join(__dirname, 'restaurant.db');
const db = new sqlite3.Database(dbPath);

// Set UTF-8 encoding for proper Myanmar text support
db.run("PRAGMA encoding = 'UTF-8'");
db.run("PRAGMA journal_mode = WAL");
db.run("PRAGMA synchronous = NORMAL");

// Initialize database with tables
const init = () => {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Users table
      db.run(`
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username TEXT UNIQUE NOT NULL,
          password TEXT NOT NULL,
          role TEXT NOT NULL CHECK(role IN ('admin', 'counter', 'kitchen', 'customer')),
          name TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Categories table
      db.run(`
        CREATE TABLE IF NOT EXISTS categories (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          name_mm TEXT NOT NULL,
          description TEXT,
          image_url TEXT,
          icon TEXT DEFAULT '🍽️',
          sort_order INTEGER DEFAULT 0,
          is_active BOOLEAN DEFAULT 1,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Menu items table
      db.run(`
        CREATE TABLE IF NOT EXISTS menu_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          category_id INTEGER NOT NULL,
          name TEXT NOT NULL,
          name_mm TEXT NOT NULL,
          description TEXT,
          description_mm TEXT,
          price DECIMAL(10,2) NOT NULL,
          image_url TEXT,
          is_available BOOLEAN DEFAULT 1,
          is_active BOOLEAN DEFAULT 1,
          is_today_special BOOLEAN DEFAULT 0,
          sort_order INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (category_id) REFERENCES categories (id)
        )
      `);

      // Tables table
      db.run(`
        CREATE TABLE IF NOT EXISTS tables (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          table_number TEXT UNIQUE NOT NULL,
          table_name TEXT,
          capacity INTEGER DEFAULT 4,
          qr_code TEXT,
          is_active BOOLEAN DEFAULT 1,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Orders table
      db.run(`
        CREATE TABLE IF NOT EXISTS orders (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          customer_name TEXT NOT NULL,
          customer_phone TEXT,
          table_id INTEGER,
          table_number TEXT,
          total_amount DECIMAL(10,2) NOT NULL,
          status TEXT NOT NULL DEFAULT 'pending' CHECK(status IN ('pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled')),
          payment_status TEXT NOT NULL DEFAULT 'pending' CHECK(payment_status IN ('pending', 'paid', 'completed', 'refunded')),
          payment_method TEXT CHECK(payment_method IN ('cash', 'card', 'mobile')),
          table_clear_requested BOOLEAN DEFAULT 0,
          table_clear_time DATETIME,
          notes TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (table_id) REFERENCES tables (id)
        )
      `);

      // Order items table
      db.run(`
        CREATE TABLE IF NOT EXISTS order_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          order_id INTEGER NOT NULL,
          menu_item_id INTEGER NOT NULL,
          quantity INTEGER NOT NULL,
          unit_price DECIMAL(10,2) NOT NULL,
          total_price DECIMAL(10,2) NOT NULL,
          special_instructions TEXT,
          status TEXT NOT NULL DEFAULT 'pending' CHECK(status IN ('pending', 'confirmed', 'preparing', 'ready', 'served', 'cancelled')),
          kitchen_notes TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (order_id) REFERENCES orders (id),
          FOREIGN KEY (menu_item_id) REFERENCES menu_items (id)
        )
      `);

      // Settings table
      db.run(`
        CREATE TABLE IF NOT EXISTS settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          key TEXT UNIQUE NOT NULL,
          value TEXT NOT NULL,
          description TEXT,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Payment requests table
      db.run(`
        CREATE TABLE IF NOT EXISTS payment_requests (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          table_number TEXT NOT NULL,
          order_id INTEGER,
          message TEXT,
          amount DECIMAL(10,2),
          status TEXT DEFAULT 'pending',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Bell alerts table
      db.run(`
        CREATE TABLE IF NOT EXISTS bell_alerts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          table_number TEXT NOT NULL,
          message TEXT,
          status TEXT DEFAULT 'pending',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Customer notifications table
      db.run(`
        CREATE TABLE IF NOT EXISTS customer_notifications (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          table_number TEXT NOT NULL,
          message TEXT NOT NULL,
          type TEXT DEFAULT 'general',
          read BOOLEAN DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `, (err) => {
        if (err) {
          reject(err);
        } else {
          // Insert default data
          insertDefaultData().then(resolve).catch(reject);
        }
      });
    });
  });
};

// Insert default data
const insertDefaultData = async () => {
  return new Promise((resolve, reject) => {
    // Create default admin user
    const adminPassword = bcrypt.hashSync('admin123', 10);

    db.run(`
      INSERT OR IGNORE INTO users (username, password, role, name)
      VALUES ('admin', ?, 'admin', 'Administrator')
    `, [adminPassword]);

    // Create default kitchen user
    const kitchenPassword = bcrypt.hashSync('kitchen123', 10);

    db.run(`
      INSERT OR IGNORE INTO users (username, password, role, name)
      VALUES ('kitchen', ?, 'kitchen', 'Kitchen Staff')
    `, [kitchenPassword]);

    // Create default counter user
    const counterPassword = bcrypt.hashSync('counter123', 10);

    db.run(`
      INSERT OR IGNORE INTO users (username, password, role, name)
      VALUES ('counter', ?, 'counter', 'Counter Staff')
    `, [counterPassword]);

    // Insert default settings
    const defaultSettings = [
      ['restaurant_name', 'အရှင်စားသောက်ဆိုင်', 'Restaurant name'],
      ['restaurant_name_en', 'A Shin Restaurant', 'Restaurant name in English'],
      ['restaurant_title_mm', 'မြန်မာ့အရသာ', 'Restaurant title in Myanmar'],
      ['restaurant_title_en', 'Myanmar Traditional Cuisine', 'Restaurant title in English'],
      ['restaurant_logo', '🍜', 'Restaurant logo emoji'],
      ['opening_hours_mm', '၂၄ နာရီ ဖွင့်ထားသည်', 'Opening hours in Myanmar'],
      ['opening_hours_en', 'Always Open for You', 'Opening hours in English'],
      ['phone_number', '09-123-456-789', 'Restaurant phone number'],
      ['address_mm', 'ရန်ကုန်မြို့', 'Restaurant address in Myanmar'],
      ['address_en', 'Yangon, Myanmar', 'Restaurant address in English'],
      ['welcome_message_mm', 'မင်္ဂလာပါ! ကြိုဆိုပါတယ်', 'Welcome message in Myanmar'],
      ['welcome_message_en', 'Welcome to our restaurant', 'Welcome message in English'],
      ['description_mm', 'မြန်မာ့ရိုးရာအစားအသောက်များကို အွန်လိုင်းမှ အမှာပေးနိုင်ပါတယ်', 'Restaurant description in Myanmar'],
      ['description_en', 'Order traditional Myanmar food online', 'Restaurant description in English'],
      ['currency', 'MMK', 'Currency symbol'],
      ['tax_rate', '0', 'Tax rate percentage'],
      ['service_charge', '0', 'Service charge percentage']
    ];

    defaultSettings.forEach(([key, value, description]) => {
      db.run(`
        INSERT OR IGNORE INTO settings (key, value, description) 
        VALUES (?, ?, ?)
      `, [key, value, description]);
    });

    // Insert default categories (unique only)
    const defaultCategories = [
      // Food Categories
      ['ထမင်းများ', 'Rice Dishes', '🍚'],
      ['ခေါက်ဆွဲများ', 'Noodle Dishes', '🍜'],
      ['ဟင်းများ', 'Curry Dishes', '🍛'],
      ['သုပ်များ', 'Salad Dishes', '🥗'],
      ['အချိုများ', 'Desserts', '🍰'],
      ['ယမကာများ', 'Beverages', '🥤'],
      ['မုန့်များ', 'Snacks', '🥖'],
      ['အသားများ', 'Meat Dishes', '🍖'],
      ['ကြက်သားများ', 'Chicken Dishes', '🍗'],

      // Utensils & Accessories Categories
      ['ပန်းကန်များ', 'Plates & Dishes', '🍽️'],
      ['ဇွန်းများ', 'Spoons', '🥄'],
      ['ခက်ရင်းများ', 'Forks & Knives', '🍴'],
      ['ဖန်ခွက်များ', 'Glasses & Cups', '🥃'],
      ['အရက်ခွက်များ', 'Beer Mugs', '🍻'],
      ['တစ်ရှူးများ', 'Tissues & Napkins', '🧻'],
      ['ထုပ်ပိုးများ', 'Takeaway Items', '🥡'],
      ['သန့်ရှင်းရေးပစ္စည်းများ', 'Cleaning Items', '🧽']
    ];

    // Insert default categories (only if none exist)
    db.get('SELECT COUNT(*) as count FROM categories', [], (err, result) => {
      if (err) {
        console.error('Error checking categories:', err);
        return;
      }

      if (result.count > 0) {
        console.log('Categories already exist, skipping default insertion');
        return;
      }

      console.log('Inserting default categories...');

      // Only insert essential food categories
      const essentialCategories = [
        ['ထမင်းများ', 'Rice Dishes', '🍚'],
        ['ခေါက်ဆွဲများ', 'Noodle Dishes', '🍜'],
        ['ဟင်းများ', 'Curry Dishes', '🍛'],
        ['သုပ်များ', 'Salad Dishes', '🥗'],
        ['အချိုများ', 'Desserts', '🍰'],
        ['ယမကာများ', 'Beverages', '🥤']
      ];

      let completed = 0;
      essentialCategories.forEach(([name_mm, name_en, icon], index) => {
        db.run(`
          INSERT INTO categories (name, name_mm, icon, sort_order, is_active, created_at)
          VALUES (?, ?, ?, ?, 1, datetime('now'))
        `, [name_en, name_mm, icon, index + 1], function(err) {
          if (err) {
            console.error('Error inserting category:', err);
          } else {
            console.log(`✅ Created category: ${icon} ${name_mm} (${name_en})`);
          }

          completed++;
          if (completed === essentialCategories.length) {
            console.log('✅ Default categories inserted successfully!');
          }
        });
      });
    });

    // Insert default menu items (only if none exist)
    setTimeout(() => {
      db.get('SELECT COUNT(*) as count FROM menu_items', [], (err, result) => {
        if (err) {
          console.error('Error checking menu items:', err);
          return;
        }

        if (result.count > 0) {
          console.log('Menu items already exist, skipping default insertion');
          return;
        }

        console.log('Inserting sample menu items...');

        // Only insert a few sample items
        const sampleMenuItems = [
        // Rice Dishes (Category 1)
        [1, 'Fried Rice', 'ထမင်းကြော်', 'Delicious fried rice', 'အရသာရှိသော ထမင်းကြော်', 3000, true, false],
        [1, 'Plain Rice', 'ထမင်းဖြူ', 'Steamed white rice', 'ပြုတ်ထမင်းဖြူ', 1500, true, false],

        // Noodle Dishes (Category 2)
        [2, 'Mohinga', 'မုန့်ဟင်းခါး', 'Traditional Myanmar fish noodle soup', 'မြန်မာ့ရိုးရာ ငါးခေါက်ဆွဲ', 2500, true, true],

        // Curry Dishes (Category 3)
        [3, 'Chicken Curry', 'ကြက်သားဟင်း', 'Spicy chicken curry', 'စပ်သော ကြက်သားဟင်း', 4000, true, true],

        // Salad Dishes (Category 4)
        [4, 'Tea Leaf Salad', 'လပက်သုပ်', 'Traditional tea leaf salad', 'ရိုးရာ လပက်သုပ်', 3000, true, true],

        // Desserts (Category 5)
        [5, 'Shwe Yin Aye', 'ရွှေရင်အေး', 'Traditional Myanmar dessert', 'မြန်မာ့ရိုးရာ အချိုပွဲ', 2000, true, false],

        // Beverages (Category 6)
        [6, 'Myanmar Tea', 'လက်ဖက်ရည်', 'Traditional Myanmar tea', 'မြန်မာ့ရိုးရာ လက်ဖက်ရည်', 800, true, false]
      ];

      let menuCompleted = 0;
      sampleMenuItems.forEach(([category_id, name, name_mm, description, description_mm, price, is_available, is_today_special]) => {
        db.run(`
          INSERT INTO menu_items (category_id, name, name_mm, description, description_mm, price, is_available, is_today_special, is_active, created_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, datetime('now'))
        `, [category_id, name, name_mm, description, description_mm, price, is_available, is_today_special], function(err) {
          if (err) {
            console.error('Error inserting menu item:', err);
          } else {
            console.log(`✅ Created menu item: ${name_mm} (${name}) - ${price} MMK`);
          }

          menuCompleted++;
          if (menuCompleted === sampleMenuItems.length) {
            console.log('✅ Sample menu items inserted successfully!');
          }
        });
      });
      });
    }, 100);

    // Insert default tables
    const defaultTables = [
      ['T001', 'စားပွဲ ၁', 4],
      ['T002', 'စားပွဲ ၂', 4],
      ['T003', 'စားပွဲ ၃', 6],
      ['T004', 'စားပွဲ ၄', 4],
      ['T005', 'စားပွဲ ၅', 2],
      ['T006', 'စားပွဲ ၆', 4],
      ['T007', 'စားပွဲ ၇', 8],
      ['T008', 'စားပွဲ ၈', 4],
      ['T009', 'စားပွဲ ၉', 4],
      ['T010', 'စားပွဲ ၁၀', 6]
    ];

    defaultTables.forEach(([table_number, table_name, capacity]) => {
      const qr_code = `${table_number}-${Date.now()}`;
      db.run(`
        INSERT OR IGNORE INTO tables (table_number, table_name, capacity, qr_code)
        VALUES (?, ?, ?, ?)
      `, [table_number, table_name, capacity, qr_code]);
    });

    resolve();
  });
};

// Get database instance
const getDb = () => db;

// Close database connection
const close = () => {
  return new Promise((resolve) => {
    db.close((err) => {
      if (err) {
        console.error('Error closing database:', err);
      }
      resolve();
    });
  });
};

module.exports = {
  init,
  getDb,
  close
};
