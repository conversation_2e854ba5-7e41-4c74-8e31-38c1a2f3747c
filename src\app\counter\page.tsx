'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../contexts/AuthContext';
import Navigation from '../../components/Navigation';
import { io } from 'socket.io-client';

interface Order {
  id: number;
  customer_name: string;
  customer_phone?: string;
  table_number?: string;
  total_amount: number;
  status: string;
  payment_status: string;
  payment_method?: string;
  notes?: string;
  table_clear_requested?: boolean;
  table_clear_time?: string;
  created_at: string;
  items: OrderItem[];
}

interface OrderItem {
  id: number;
  name: string;
  name_mm: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  special_instructions?: string;
  status: string;
  kitchen_notes?: string;
  created_at: string;
  updated_at: string;
}

export default function CounterPage() {
  const { isAuthenticated } = useAuth();
  const router = useRouter();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [settings, setSettings] = useState<any>({});
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [paymentRequests, setPaymentRequests] = useState<any[]>([]);
  const [bellAlerts, setBellAlerts] = useState<any[]>([]);
  const [showTableClearConfirm, setShowTableClearConfirm] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [notifications, setNotifications] = useState<any[]>([]);
  const [showNotifications, setShowNotifications] = useState(false);
  const [marqueeText, setMarqueeText] = useState('');

  // Update time every second for LCD display
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Update marquee text based on current data
  useEffect(() => {
    const updateMarqueeText = () => {
      const messages = [];

      // System status
      messages.push('🍽️ ကောင်တာစနစ် လုပ်ဆောင်နေပါသည်');

      // Orders info
      if (orders.length > 0) {
        const activeOrders = orders.filter(o => o.status !== 'delivered' && o.payment_status !== 'paid').length;
        if (activeOrders > 0) {
          messages.push(`📋 ${activeOrders} အမှာများ လုပ်ဆောင်နေပါသည်`);
        }
      }

      // Payment requests
      if (paymentRequests.length > 0) {
        messages.push(`💰 ${paymentRequests.length} ငွေရှင်းရန် စောင့်ဆိုင်းနေပါသည်`);
      }

      // Bell alerts
      if (bellAlerts.length > 0) {
        messages.push(`🔔 ${bellAlerts.length} အရေးပေါ် အသိပေးချက်များ ရှိပါသည်`);
      }

      // Time info
      messages.push(`⏰ ${currentTime.toLocaleTimeString('my-MM')}`);

      // System ready
      messages.push('✅ စနစ် အဆင်သင့်ဖြစ်ပါသည်');

      // Auto refresh info
      messages.push('🔄 အချက်အလက်များ အလိုအလျောက် ပြန်လည်ရယူနေပါသည်');

      setMarqueeText(messages.join(' • ') + ' • ');
    };

    updateMarqueeText();
  }, [orders, paymentRequests, bellAlerts, currentTime]);

  useEffect(() => {
    // Only redirect if we're sure authentication is loaded and user is not authenticated
    if (isAuthenticated === false) {
      router.push('/login');
      return;
    }

    // Only fetch data if authenticated
    if (isAuthenticated === true) {
      fetchOrders();
      fetchSettings();
      fetchPaymentRequests();
      fetchBellAlerts();

      // Set up socket connection for real-time updates
      const socket = io('http://localhost:5000');

      // Join counter room
      socket.emit('join-room', 'counter');

      // Listen for real-time updates
      socket.on('order-created', (orderData) => {
        console.log('New order received:', orderData);
        fetchOrders();
      });

      socket.on('order-status-updated', (data) => {
        console.log('Order status updated:', data);
        fetchOrders();
      });

      socket.on('order-item-updated', (data) => {
        console.log('Order item updated:', data);
        fetchOrders();
      });

      socket.on('payment-request-created', (requestData) => {
        console.log('New payment request:', requestData);
        fetchPaymentRequests();
      });

      socket.on('payment-request-updated', (data) => {
        console.log('Payment request updated:', data);
        fetchPaymentRequests();
      });

      socket.on('bell-alert-created', (alertData) => {
        console.log('New bell alert:', alertData);
        fetchBellAlerts();
      });

      socket.on('bell-alert-updated', (data) => {
        console.log('Bell alert updated:', data);
        fetchBellAlerts();
      });

      // Listen for new notifications
      socket.on('notification', (notification) => {
        setNotifications(prev => [notification, ...prev.slice(0, 4)]); // Keep only 5 latest
        setTimeout(() => {
          setNotifications(prev => prev.filter(n => n.id !== notification.id));
        }, 10000); // Remove after 10 seconds
      });

      // Fallback polling for reliability
      const interval = setInterval(() => {
        fetchOrders();
        fetchPaymentRequests();
        fetchBellAlerts();
      }, 30000);

      return () => {
        socket.disconnect();
        clearInterval(interval);
      };
    }
  }, [isAuthenticated, router]);

  const fetchOrders = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/orders');
      if (response.ok) {
        const data = await response.json();
        // Filter out orders with payment_status 'paid'
        const activeOrders = data.filter((order: any) => order.payment_status !== 'paid');
        setOrders(activeOrders);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchSettings = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
    }
  };

  const fetchPaymentRequests = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/payment-requests');
      if (response.ok) {
        const data = await response.json();
        setPaymentRequests(data);
      }
    } catch (error) {
      console.error('Error fetching payment requests:', error);
    }
  };

  const fetchBellAlerts = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/bell-alerts');
      if (response.ok) {
        const data = await response.json();
        setBellAlerts(data);
      }
    } catch (error) {
      console.error('Error fetching bell alerts:', error);
    }
  };

  const markPaymentRequestCompleted = async (id: number, tableNumber: string) => {
    try {
      const response = await fetch(`http://localhost:5000/api/payment-requests/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'completed' }),
      });

      if (response.ok) {
        fetchPaymentRequests();

        // Send notification to customer
        try {
          const notificationResponse = await fetch('http://localhost:5000/api/customer-notifications', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              table_number: tableNumber,
              message: `လာပါ့ပြီရှင်! ဝန်ထမ်းများ စားပွဲ ${tableNumber} သို့ ငွေရှင်းရန် လာနေပါပြီ။`,
              type: 'payment_coming'
            }),
          });

          if (notificationResponse.ok) {
            alert(`စားပွဲ ${tableNumber} ကို "လာပါ့ပြီရှင်" ဆိုတဲ့ message ပေးပို့ပြီးပါပြီ`);
          } else {
            alert(`Payment request ကို completed လုပ်ပြီးပါပြီ`);
          }
        } catch (notificationError) {
          console.error('Error sending customer notification:', notificationError);
          alert(`Payment request ကို completed လုပ်ပြီးပါပြီ`);
        }
      }
    } catch (error) {
      console.error('Error marking payment request as completed:', error);
    }
  };

  const markBellAlertCompleted = async (id: number, tableNumber: string) => {
    try {
      const response = await fetch(`http://localhost:5000/api/bell-alerts/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'completed' }),
      });

      if (response.ok) {
        fetchBellAlerts();

        // Send notification to customer
        try {
          const notificationResponse = await fetch('http://localhost:5000/api/customer-notifications', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              table_number: tableNumber,
              message: `လာပါ့ပြီရှင်! ဝန်ထမ်းများ စားပွဲ ${tableNumber} သို့ လာနေပါပြီ။`,
              type: 'waiter_coming'
            }),
          });

          if (notificationResponse.ok) {
            alert(`စားပွဲ ${tableNumber} ကို "လာပါ့ပြီရှင်" ဆိုတဲ့ message ပေးပို့ပြီးပါပြီ`);
          } else {
            alert(`Bell alert ကို completed လုပ်ပြီးပါပြီ`);
          }
        } catch (notificationError) {
          console.error('Error sending customer notification:', notificationError);
          alert(`Bell alert ကို completed လုပ်ပြီးပါပြီ`);
        }
      }
    } catch (error) {
      console.error('Error marking bell alert as completed:', error);
    }
  };

  const updateOrderStatus = async (orderId: number, newStatus: string) => {
    try {
      const response = await fetch(`http://localhost:5000/api/orders/${orderId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        fetchOrders(); // Refresh orders
      }
    } catch (error) {
      console.error('Error updating order status:', error);
    }
  };

  const updateOrderItemStatus = async (orderId: number, itemId: number, newStatus: string, kitchenNotes?: string) => {
    try {
      const response = await fetch(`http://localhost:5000/api/orders/${orderId}/items/${itemId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus, kitchen_notes: kitchenNotes }),
      });

      if (response.ok) {
        fetchOrders(); // Refresh orders
      }
    } catch (error) {
      console.error('Error updating order item status:', error);
    }
  };

  const clearCancellationRequest = async (orderId: number, itemId: number) => {
    try {
      const response = await fetch(`http://localhost:5000/api/orders/${orderId}/items/${itemId}/clear-cancellation`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        fetchOrders();
      }
    } catch (error) {
      console.error('Error clearing cancellation request:', error);
    }
  };

  const cancelOrderItem = async (orderId: number, itemId: number, reason: string) => {
    try {
      const response = await fetch(`http://localhost:5000/api/orders/${orderId}/items/${itemId}/cancel`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason }),
      });

      if (response.ok) {
        fetchOrders(); // Refresh orders
      }
    } catch (error) {
      console.error('Error cancelling order item:', error);
    }
  };

  const markPaymentCompleted = async (orderId: number) => {
    try {
      const response = await fetch(`http://localhost:5000/api/orders/${orderId}/payment-complete`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        fetchOrders(); // Refresh orders
        alert('ငွေရှင်းပြီးပါပြီ! စားပွဲသစ်အတွက် အဆင်သင့်ဖြစ်ပါပြီ။');
      }
    } catch (error) {
      console.error('Error marking payment as completed:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
      case 'confirmed': return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
      case 'preparing': return 'bg-orange-500/20 text-orange-300 border-orange-500/30';
      case 'ready': return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'served': return 'bg-purple-500/20 text-purple-300 border-purple-500/30';
      case 'delivered': return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
      case 'cancelled': return 'bg-red-500/20 text-red-300 border-red-500/30';
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  const getItemStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
      case 'confirmed': return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
      case 'preparing': return 'bg-orange-500/20 text-orange-300 border-orange-500/30';
      case 'ready': return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'served': return 'bg-purple-500/20 text-purple-300 border-purple-500/30';
      case 'cancelled': return 'bg-red-500/20 text-red-300 border-red-500/30';
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '⏳ စောင့်ဆိုင်းနေသည်';
      case 'confirmed': return '✅ အတည်ပြုပြီး';
      case 'preparing': return '👨‍🍳 ချက်နေသည်';
      case 'ready': return '🍽️ အဆင်သင့်';
      case 'served': return '🚚 ပေးပြီး';
      case 'delivered': return '✅ ပြီးဆုံး';
      case 'cancelled': return '❌ ပယ်ဖျက်ပြီး';
      default: return status;
    }
  };

  const getItemStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '⏳ စောင့်ဆိုင်း';
      case 'confirmed': return '✅ အတည်ပြု';
      case 'preparing': return '👨‍🍳 ချက်နေ';
      case 'ready': return '🍽️ အဆင်သင့်';
      case 'served': return '🚚 ပေးပြီး';
      case 'cancelled': return '❌ ပယ်ဖျက်';
      default: return status;
    }
  };

  const filteredOrders = selectedStatus === 'all' 
    ? orders 
    : orders.filter(order => order.status === selectedStatus);

  // Show loading while authentication is being checked or data is being fetched
  if (loading || isAuthenticated === null) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center bg-gray-800 rounded-2xl p-8 border border-gray-700">
          <div className="text-4xl mb-4">🧾</div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-300 font-medium">အမှာများ ရယူနေသည်...</p>
        </div>
      </div>
    );
  }

  // Don't render anything if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-900 relative">
      {/* Background Food Pattern */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-10 left-10 text-6xl">🧾</div>
        <div className="absolute top-20 right-20 text-4xl">💰</div>
        <div className="absolute top-40 left-1/4 text-5xl">📋</div>
        <div className="absolute top-60 right-1/3 text-3xl">✅</div>
        <div className="absolute bottom-40 left-20 text-4xl">🍽️</div>
        <div className="absolute bottom-20 right-10 text-5xl">👨‍🍳</div>
        <div className="absolute bottom-60 left-1/2 text-3xl">⏰</div>
        <div className="absolute top-1/2 left-10 text-4xl">🚚</div>
        <div className="absolute top-1/3 right-10 text-3xl">📱</div>
        <div className="absolute top-80 left-1/3 text-4xl">🪑</div>
        <div className="absolute bottom-80 right-1/4 text-3xl">💳</div>
        <div className="absolute top-1/4 left-1/2 text-5xl">🔔</div>
      </div>

      {/* Navigation */}
      <Navigation
        settings={settings}
        pageTitle="Counter Dashboard"
        pageTitleEn="အမှာများ စီမံခန့်ခွဲမှု"
      />

      {/* Floating Notifications */}
      {notifications.length > 0 && (
        <div className="fixed top-20 right-4 z-50 space-y-2">
          {notifications.map((notification, index) => (
            <div
              key={notification.id || index}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-3 rounded-lg shadow-lg animate-pulse border border-blue-400"
            >
              <div className="flex items-center gap-2">
                <span className="text-lg">🔔</span>
                <span className="font-medium">{notification.message}</span>
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="max-w-6xl mx-auto px-4 py-6 relative z-10">
        {/* Compact LCD Digital Display */}
        <div className="mb-4">
          <div className="bg-black rounded-xl p-3 border-2 border-gray-600 shadow-xl">
            {/* LCD Display */}
            <div className="bg-gradient-to-r from-green-900 to-green-800 rounded-lg p-3 border border-green-600 relative overflow-hidden">
              {/* Top Row - Time and Stats */}
              <div className="grid grid-cols-4 gap-3 text-green-300 font-mono text-center mb-2">
                {/* Time Display */}
                <div>
                  <div className="text-lg font-bold tracking-wider">
                    {currentTime.toLocaleTimeString('en-US', {
                      hour12: false,
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit'
                    })}
                  </div>
                  <div className="text-xs opacity-70">
                    {currentTime.toLocaleDateString('my-MM', { month: 'short', day: 'numeric' })}
                  </div>
                </div>

                {/* Order Stats */}
                <div>
                  <div className="text-lg font-bold">
                    {orders.filter(o => o.status !== 'delivered' && o.payment_status !== 'paid').length}
                  </div>
                  <div className="text-xs opacity-70">အမှာများ</div>
                </div>

                {/* Payment Requests */}
                <div>
                  <div className="text-lg font-bold text-yellow-300">
                    {paymentRequests.length}
                  </div>
                  <div className="text-xs opacity-70">ငွေရှင်း</div>
                </div>

                {/* Bell Alerts */}
                <div>
                  <div className="text-lg font-bold text-red-300">
                    {bellAlerts.length}
                  </div>
                  <div className="text-xs opacity-70">အရေးပေါ်</div>
                </div>
              </div>

              {/* Marquee Text Display */}
              <div className="bg-green-950/50 rounded border border-green-700 p-2 overflow-hidden">
                <div className="whitespace-nowrap">
                  <div className="inline-block animate-marquee text-green-300 text-sm font-mono">
                    {marqueeText}
                  </div>
                </div>
              </div>
            </div>

            {/* Compact Control Panel */}
            <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 border border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-lg font-bold text-white">
                    💰 ငွေကောင်တာ
                  </h1>
                  <p className="text-gray-400 text-xs">
                    အမှာများ၊ ငွေရှင်းခြင်း နှင့် စားပွဲများ
                  </p>
                </div>

                {/* Quick Actions */}
                <div className="flex gap-2">
                  <button
                    onClick={() => setShowNotifications(!showNotifications)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs transition-colors"
                  >
                    🔔
                  </button>
                  <button
                    onClick={() => window.location.reload()}
                    className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-xs transition-colors"
                  >
                    🔄
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Notifications Section */}
        {(paymentRequests.length > 0 || bellAlerts.length > 0) && (
          <div className="mb-6">
            <div className="bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4 border border-gray-700">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                <span className="text-2xl">🔔</span>
                အသိပေးချက်များ
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Payment Requests */}
                {paymentRequests.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-green-400 font-medium text-sm flex items-center gap-2">
                      <span>💰</span>
                      ငွေရှင်းရန် တောင်းဆိုမှုများ ({paymentRequests.length})
                    </h4>
                    {paymentRequests.map((request) => (
                      <div key={request.id} className="bg-green-500/20 border border-green-500/30 rounded-lg p-3">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="text-green-300 font-medium text-sm">
                              🪑 {request.message}
                            </p>
                            <p className="text-green-200 text-xs">
                              {new Date(request.created_at).toLocaleString('my-MM')}
                            </p>
                          </div>
                          <button
                            onClick={() => markPaymentRequestCompleted(request.id, request.table_number)}
                            className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs transition-colors"
                          >
                            ✅ လာပါ့ပြီရှင်
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Bell Alerts */}
                {bellAlerts.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-orange-400 font-medium text-sm flex items-center gap-2">
                      <span>🔔</span>
                      လှန်းခေါ်မှုများ ({bellAlerts.length})
                    </h4>
                    {bellAlerts.map((alert) => (
                      <div key={alert.id} className="bg-orange-500/20 border border-orange-500/30 rounded-lg p-3">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="text-orange-300 font-medium text-sm">
                              🪑 {alert.message}
                            </p>
                            <p className="text-orange-200 text-xs">
                              {new Date(alert.created_at).toLocaleString('my-MM')}
                            </p>
                          </div>
                          <button
                            onClick={() => markBellAlertCompleted(alert.id, alert.table_number)}
                            className="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1 rounded text-xs transition-colors"
                          >
                            ✅ လာပါ့ပြီ
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Status Filter */}
        <div className="mb-6">
          <div className="bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4 border border-gray-700">
            <div className="flex flex-wrap gap-2">
              {[
                { value: 'all', label: '🔍 အားလုံး', count: orders.length },
                { value: 'pending', label: '⏳ စောင့်ဆိုင်းနေသည်', count: orders.filter(o => o.status === 'pending').length },
                { value: 'confirmed', label: '✅ အတည်ပြုပြီး', count: orders.filter(o => o.status === 'confirmed').length },
                { value: 'preparing', label: '👨‍🍳 ချက်နေသည်', count: orders.filter(o => o.status === 'preparing').length },
                { value: 'ready', label: '🍽️ အဆင်သင့်', count: orders.filter(o => o.status === 'ready').length },
              ].map((filter) => (
                <button
                  key={filter.value}
                  onClick={() => setSelectedStatus(filter.value)}
                  className={`px-4 py-2 rounded-xl font-medium transition-all duration-300 text-sm ${
                    selectedStatus === filter.value
                      ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg scale-105'
                      : 'bg-gray-700/80 text-gray-300 hover:bg-gray-600/80 border border-gray-600 hover:border-gray-500'
                  }`}
                >
                  {filter.label} ({filter.count})
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Table-Based Orders View */}
        {filteredOrders.length === 0 ? (
          <div className="text-center py-12">
            <div className="bg-gray-800/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-700">
              <div className="text-6xl mb-4">📋</div>
              <h2 className="text-xl font-bold text-white mb-2">အမှာများ မရှိပါ</h2>
              <p className="text-gray-300">ဖောက်သည်များ အမှာပေးလာသည်ကို စောင့်ဆိုင်းပါ</p>
            </div>
          </div>
        ) : (
          <div className="bg-gray-800/90 backdrop-blur-sm rounded-2xl border border-gray-700 overflow-hidden">
            {/* Table Header */}
            <div className="bg-gradient-to-r from-gray-700 to-gray-600 p-4">
              <div className="grid grid-cols-12 gap-4 text-white font-medium text-sm">
                <div className="col-span-1">အမှာ#</div>
                <div className="col-span-2">ဖောက်သည်</div>
                <div className="col-span-1">စားပွဲ</div>
                <div className="col-span-2">အခြေအနေ</div>
                <div className="col-span-1">ငွေ</div>
                <div className="col-span-2">အချိန်</div>
                <div className="col-span-3">လုပ်ဆောင်ချက်များ</div>
              </div>
            </div>

            {/* Table Body */}
            <div className="divide-y divide-gray-700">
              {filteredOrders.map((order) => (
                <div key={order.id} className="p-4 hover:bg-gray-700/50 transition-all duration-300">
                  <div className="grid grid-cols-12 gap-4 items-center text-sm">
                    {/* Order ID */}
                    <div className="col-span-1">
                      <span className="font-bold text-blue-400">#{order.id}</span>
                    </div>

                    {/* Customer */}
                    <div className="col-span-2">
                      <div className="text-white font-medium">{order.customer_name}</div>
                      {order.customer_phone && (
                        <div className="text-gray-400 text-xs">{order.customer_phone}</div>
                      )}
                    </div>

                    {/* Table */}
                    <div className="col-span-1">
                      {order.table_number ? (
                        <span className="bg-purple-600 text-white px-2 py-1 rounded text-xs font-medium">
                          🪑 {order.table_number}
                        </span>
                      ) : (
                        <span className="text-gray-500 text-xs">-</span>
                      )}
                    </div>

                    {/* Status */}
                    <div className="col-span-2">
                      <div className="space-y-1">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          order.status === 'pending' ? 'bg-yellow-500/20 text-yellow-300' :
                          order.status === 'confirmed' ? 'bg-blue-500/20 text-blue-300' :
                          order.status === 'preparing' ? 'bg-orange-500/20 text-orange-300' :
                          order.status === 'ready' ? 'bg-green-500/20 text-green-300' :
                          order.status === 'served' ? 'bg-purple-500/20 text-purple-300' :
                          'bg-gray-500/20 text-gray-300'
                        }`}>
                          {order.status === 'pending' ? '⏳ စောင့်ဆိုင်း' :
                           order.status === 'confirmed' ? '✅ အတည်ပြု' :
                           order.status === 'preparing' ? '👨‍🍳 ချက်နေ' :
                           order.status === 'ready' ? '🍽️ အဆင်သင့်' :
                           order.status === 'served' ? '🚚 ပေးပို့်ပြီး' :
                           order.status}
                        </span>
                        <div>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            order.payment_status === 'paid' ? 'bg-green-500/20 text-green-300' :
                            order.payment_status === 'pending' ? 'bg-yellow-500/20 text-yellow-300' :
                            'bg-red-500/20 text-red-300'
                          }`}>
                            {order.payment_status === 'paid' ? '💰 ရှင်းပြီး' :
                             order.payment_status === 'pending' ? '💳 စောင့်ဆိုင်း' :
                             '❌ မရှင်းသေး'}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Amount */}
                    <div className="col-span-1">
                      <span className="text-green-400 font-bold">
                        {order.total_amount?.toLocaleString()} ကျပ်
                      </span>
                    </div>

                    {/* Time */}
                    <div className="col-span-2">
                      <div className="text-gray-300 text-xs">
                        {new Date(order.created_at).toLocaleString('my-MM')}
                      </div>
                      {order.table_clear_requested && (
                        <div className="text-purple-400 text-xs mt-1">
                          🧹 ရှင်းရန် တောင်းဆို
                        </div>
                      )}
                    </div>

                    {/* Actions */}
                    <div className="col-span-3">
                      <div className="flex gap-1 flex-wrap">
                        {order.status === 'pending' && (
                          <button
                            onClick={() => updateOrderStatus(order.id, 'confirmed')}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs transition-colors"
                          >
                            ✅ အတည်ပြု
                          </button>
                        )}
                        {(order.status === 'ready' || order.status === 'served') && !order.table_clear_requested && (
                          <button
                            onClick={() => updateOrderStatus(order.id, 'delivered')}
                            className="bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded text-xs transition-colors"
                          >
                            ✅ ပြီးဆုံး
                          </button>
                        )}
                        {order.table_clear_requested && order.status !== 'delivered' && (
                          <button
                            onClick={() => {
                              setSelectedOrder(order);
                              setShowTableClearConfirm(true);
                            }}
                            className="bg-purple-600 hover:bg-purple-700 text-white px-2 py-1 rounded text-xs transition-colors"
                          >
                            🧹 ရှင်းပြီး
                          </button>
                        )}
                        {(order.status === 'delivered' || order.status === 'served') && order.payment_status !== 'paid' && (
                          <button
                            onClick={() => markPaymentCompleted(order.id)}
                            className="bg-emerald-600 hover:bg-emerald-700 text-white px-2 py-1 rounded text-xs transition-colors"
                          >
                            💰 ငွေရှင်း
                          </button>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Expandable Order Details */}
                  <div className="mt-2 border-t border-gray-600 pt-2">
                    <details className="group">
                      <summary className="cursor-pointer text-gray-400 text-xs hover:text-white transition-colors">
                        📋 အမှာအသေးစိတ် ({order.items?.length || 0} မျိုး)
                      </summary>
                      <div className="mt-2 space-y-1">
                        {order.items?.map((item) => (
                          <div key={item.id} className="bg-gray-700/30 rounded p-2 text-xs">
                            <div className="flex justify-between items-center">
                              <span className="text-white">{item.name_mm} x{item.quantity}</span>
                              <span className="text-green-400">{item.total_price.toLocaleString()} ကျပ်</span>
                            </div>
                            {item.special_instructions && (
                              <div className="text-yellow-300 mt-1">📝 {item.special_instructions}</div>
                            )}
                          </div>
                        ))}
                        {order.notes && (
                          <div className="bg-blue-600/20 rounded p-2 text-xs">
                            <span className="text-blue-300">📝 မှတ်ချက်: {order.notes}</span>
                          </div>
                        )}
                      </div>
                    </details>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Table Clear Confirmation Modal */}
        {showTableClearConfirm && selectedOrder && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-gray-800 rounded-2xl max-w-md w-full border border-gray-700">
              {/* Modal Header */}
              <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-4 text-white rounded-t-2xl">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-bold">🧹 စားပွဲရှင်းရန် အတည်ပြုချက်</h3>
                  <button
                    onClick={() => setShowTableClearConfirm(false)}
                    className="text-white hover:text-gray-300 text-xl"
                  >
                    ✕
                  </button>
                </div>
                <p className="text-purple-100 text-sm mt-1">🪑 စားပွဲ: {selectedOrder.table_number}</p>
              </div>

              {/* Modal Content */}
              <div className="p-6">
                <div className="text-center mb-6">
                  <div className="text-4xl mb-3">🧹</div>
                  <h4 className="text-white font-bold mb-2">စားပွဲရှင်းပြီးပါပြီလား?</h4>
                  <p className="text-gray-300 text-sm">
                    အမှာ #{selectedOrder.id} အတွက် စားပွဲ {selectedOrder.table_number} ကို ရှင်းပြီးပါပြီလား?
                  </p>
                </div>

                {/* Order Summary */}
                <div className="bg-gray-700 rounded-lg p-4 mb-6">
                  <h5 className="text-white font-medium mb-2">📋 အမှာအကျဉ်း</h5>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-300">ဖောက်သည်:</span>
                      <span className="text-white">{selectedOrder.customer_name}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-300">စုစုပေါင်း:</span>
                      <span className="text-green-400 font-bold">{selectedOrder.total_amount.toLocaleString()} ကျပ်</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-300">အချိန်:</span>
                      <span className="text-white">{new Date(selectedOrder.created_at).toLocaleString('my-MM')}</span>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <button
                    onClick={() => setShowTableClearConfirm(false)}
                    className="flex-1 bg-gray-600 text-white py-3 rounded-xl font-medium hover:bg-gray-700 transition-colors"
                  >
                    မရှင်းသေးပါ
                  </button>
                  <button
                    onClick={() => {
                      updateOrderStatus(selectedOrder.id, 'delivered');
                      setShowTableClearConfirm(false);
                      setSelectedOrder(null);
                    }}
                    className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 rounded-xl font-medium hover:from-purple-700 hover:to-blue-700 transition-all"
                  >
                    ✅ ရှင်းပြီးပါပြီ
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
