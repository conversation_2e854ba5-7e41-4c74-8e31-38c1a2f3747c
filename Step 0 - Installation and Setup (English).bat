@echo off
title Step 0 - Restaurant Management System Installation and Setup
color 0E

echo.
echo ================================================================
echo                Step 0 - Restaurant Management System
echo                Installation and Setup
echo ================================================================
echo.

:MENU
echo Step 0 - Installation and Setup Options:
echo.
echo [1] First Time System Setup
echo     Install Node.js, dependencies and setup database
echo.
echo [2] Reinstall Dependencies
echo     Reinstall npm packages
echo.
echo [3] Database Reset and Setup
echo     Reset database and insert sample data
echo.
echo [4] System Requirements Check
echo     Check Node.js, npm and required tools
echo.
echo [5] External Tools Installation Guide
echo     Install Git, VS Code and other development tools
echo.
echo [6] Verify Installation
echo     Verify that installation is working correctly
echo.
echo [7] Start Servers and Open Web Interface
echo     Start both servers and open web interface
echo.
echo [8] Go to Main Menu
echo.
echo [9] Exit
echo.
set /p choice="Choose option (1-9): "

if "%choice%"=="1" goto FIRST_TIME_SETUP
if "%choice%"=="2" goto REINSTALL_DEPS
if "%choice%"=="3" goto DATABASE_SETUP
if "%choice%"=="4" goto SYSTEM_CHECK
if "%choice%"=="5" goto EXTERNAL_TOOLS
if "%choice%"=="6" goto VERIFY_INSTALLATION
if "%choice%"=="7" goto START_SERVERS
if "%choice%"=="8" goto MAIN_MENU
if "%choice%"=="9" goto EXIT
goto INVALID

:FIRST_TIME_SETUP
echo.
echo ================================================================
echo               Step 0.1 - First Time System Setup
echo ================================================================
echo Setting up the system for the first time...
echo.

echo [1/5] Checking Node.js...
node --version >nul 2>&1
if %errorlevel%==0 (
    echo ✓ Node.js found
    for /f "tokens=*" %%i in ('node --version 2^>nul') do echo   Version: %%i
) else (
    echo ✗ Node.js not found
    echo.
    echo Please install Node.js first:
    echo 1. Go to https://nodejs.org
    echo 2. Download LTS version
    echo 3. Install and restart this script
    echo.
    echo Returning to main menu...
    pause
    goto MENU
)

echo.
echo [2/5] Checking npm...
where npm >nul 2>&1
if %errorlevel%==0 (
    echo ✓ npm found
    for /f "tokens=*" %%i in ('npm --version 2^>nul') do echo   Version: %%i
) else (
    echo ✗ npm not found (should come with Node.js)
    echo Please reinstall Node.js
    echo.
    echo Returning to main menu...
    pause
    goto MENU
)

echo.
echo [3/5] Installing dependencies...
echo This may take 2-5 minutes depending on your internet speed...
echo.
if exist "node_modules" (
    echo Removing old node_modules...
    rmdir /s /q "node_modules" 2>nul
)
echo Installing packages...
npm install --silent --no-audit --no-fund
if %errorlevel%==0 (
    echo ✓ Dependencies installed successfully
) else (
    echo ✗ Installation failed
    echo.
    echo Try these solutions:
    echo 1. Check internet connection
    echo 2. Run as administrator
    echo 3. Use option [2] to reinstall
    echo.
    echo Returning to main menu...
    pause
    goto MENU
)

echo.
echo [4/5] Setting up directories...
if not exist "server" mkdir server
if not exist "server\database" mkdir server\database
if not exist "client" mkdir client
echo ✓ Directory structure ready

echo.
echo [5/5] Database setup...
echo Checking database requirements...
if exist "server\database\db.js" (
    echo ✓ Database module found
    echo ⚠ Database will be initialized when you run option [3]
) else (
    echo ⚠ Database module not found
    echo   This is normal - database will be set up later
)

echo.
echo ================================================================
echo                    Setup Complete!
echo ================================================================
echo.
echo ✓ Node.js and npm verified
echo ✓ Dependencies installed
echo ✓ Directory structure created
echo ✓ Ready for database setup
echo.
echo Next Steps:
echo • Use option [3] to set up database
echo • Use option [4] to check system requirements
echo • Use option [6] to verify everything works
echo.
echo Default Admin Credentials:
echo Username: admin
echo Password: admin123
echo.
echo Returning to main menu...
pause
goto MENU

:REINSTALL_DEPS
echo.
echo ================================================================
echo              Step 0.2 - Reinstall Dependencies
echo ================================================================
echo Cleaning and reinstalling all project dependencies...
echo.

echo [1/4] Removing old files...
if exist "node_modules" (
    echo Removing node_modules folder...
    rmdir /s /q "node_modules" 2>nul
    echo ✓ node_modules removed
) else (
    echo ✓ node_modules not found (already clean)
)

if exist "package-lock.json" (
    del "package-lock.json" 2>nul
    echo ✓ package-lock.json removed
) else (
    echo ✓ package-lock.json not found
)

echo.
echo [2/4] Cleaning npm cache...
echo This may take a moment...
npm cache clean --force >nul 2>&1
echo ✓ npm cache cleaned

echo.
echo [3/4] Verifying npm...
npm --version >nul 2>&1
if %errorlevel%==0 (
    echo ✓ npm is working
) else (
    echo ✗ npm has issues
    echo Please restart command prompt and try again
    echo.
    echo Returning to main menu...
    pause
    goto MENU
)

echo.
echo [4/4] Installing dependencies...
echo This may take 2-5 minutes...
npm install --silent --no-audit --no-fund
if %errorlevel%==0 (
    echo ✓ Dependencies installed successfully
) else (
    echo ✗ Installation failed
    echo.
    echo Possible solutions:
    echo 1. Check internet connection
    echo 2. Run as administrator
    echo 3. Try again later
    echo.
    echo Returning to main menu...
    pause
    goto MENU
)

echo.
echo ================================================================
echo                Dependencies Reinstalled!
echo ================================================================
echo.
echo ✓ Old files cleaned
echo ✓ npm cache cleared
echo ✓ Fresh dependencies installed
echo.
echo Returning to main menu...
pause
goto MENU

:DATABASE_SETUP
echo.
echo ================================================================
echo               Step 0.3 - Database Reset and Setup
echo ================================================================
echo Resetting database and inserting sample data...
echo.

echo ⚠ WARNING: This will delete all current database data!
echo Press Ctrl+C within 5 seconds to cancel...
timeout /t 5 /nobreak >nul

echo.
echo [1/4] Stopping servers...
echo Stopping any running Node.js processes...
taskkill /F /IM node.exe >nul 2>&1
echo ✓ Servers stopped

echo.
echo [2/4] Cleaning database files...
if exist "server\database\restaurant.db" (
    del "server\database\restaurant.db" 2>nul
    echo ✓ Old database removed
) else (
    echo ✓ No existing database found
)

echo.
echo [3/4] Setting up directories...
if not exist "server" mkdir server
if not exist "server\database" mkdir server\database
if not exist "server\uploads" mkdir server\uploads
echo ✓ Directory structure ready

echo.
echo [4/4] Initializing database with complete sample data...
echo Checking for database module...
if exist "server\database\db.js" (
    echo ✓ Database module found
    cd /d "%~dp0\server"
    echo Creating new database...
    node -e "try{const{init}=require('./database/db');init().then(()=>{console.log('✓ Database initialized successfully!');process.exit(0);}).catch(err=>{console.error('✗ Error:',err.message);process.exit(1);});}catch(e){console.error('✗ Module error:',e.message);process.exit(1);}"
    if %errorlevel%==0 (
        echo ✓ Database tables created
        echo.
        echo 📊 Step 1/6: Inserting Categories...
        node -e "const{getDb}=require('./database/db');const db=getDb();const categories=[[1,'Rice Dishes','ထမင်းများ','🍚🥢🍙🍘',1],[2,'Noodles','ခေါက်ဆွဲများ','🍜🥄🍝🍲',2],[3,'Curries','ဟင်းများ','🍛🌶️🍖🔥',3],[4,'Salads','သုပ်များ','🥗🥒🥬🍅',4],[5,'Desserts','အချိုများ','🍰🧁🍮🍯',5],[6,'Beverages','ယမကာများ','🥤☕🧃🍹',6],[7,'Snacks','မုန့်များ','🥖🥨🥯🍞',7],[8,'Meat Dishes','အသားများ','🍖🥩🍗🥓',8],[9,'Seafood','ပင်လယ်စာများ','🐟🦐🦀🐙',9],[10,'Vegetables','ဟင်းသီးဟင်းရွက်များ','🥬🥕🌽🥦',10]];let completed=0;categories.forEach(cat=>{db.run('INSERT OR REPLACE INTO categories(id,name,name_mm,icon,sort_order,is_active)VALUES(?,?,?,?,?,1)',cat,(err)=>{completed++;if(completed===categories.length){console.log('✅ 10 Categories inserted successfully!');process.exit(0);}});});"
        echo ✅ Categories inserted successfully!
        echo.
        echo 📊 Step 2/6: Inserting Menu Items...
        node -e "const{getDb}=require('./database/db');const db=getDb();const items=[[1,'Fried Rice','ထမင်းကြော်','Delicious fried rice','အရသာရှိသော ထမင်းကြော်',3000,1,0],[1,'Plain Rice','ထမင်းဖြူ','Steamed white rice','ပြုတ်ထမင်းဖြူ',1500,1,0],[1,'Chicken Fried Rice','ကြက်သားထမင်းကြော်','Delicious chicken fried rice','အရသာရှိသော ကြက်သားထမင်းကြော်',3500,1,1],[1,'Pork Fried Rice','ဝက်သားထမင်းကြော်','Fried rice with pork','ဝက်သားနှင့် ထမင်းကြော်',3200,1,0],[1,'Shrimp Fried Rice','ပုစွန်ထမင်းကြော်','Fried rice with shrimp','ပုစွန်နှင့် ထမင်းကြော်',4000,1,1],[2,'Mohinga','မုန့်ဟင်းခါး','Traditional Myanmar fish noodle soup','မြန်မာ့ရိုးရာ ငါးခေါက်ဆွဲ',2500,1,1],[2,'Shan Noodles','ရှမ်းခေါက်ဆွဲ','Traditional Shan style noodles','ရှမ်းပြည်နယ်စတိုင် ခေါက်ဆွဲ',2800,1,1],[2,'Coconut Noodles','အုန်းခေါက်ဆွဲ','Noodles in coconut milk','အုန်းရည်နှင့် ခေါက်ဆွဲ',3000,1,0],[2,'Dry Noodles','ခေါက်ဆွဲခြောက်','Dry mixed noodles','ရောစပ်ခေါက်ဆွဲ',2500,1,0],[2,'Rakhine Noodles','ရခိုင်ခေါက်ဆွဲ','Rakhine style fish noodles','ရခိုင်စတိုင် ငါးခေါက်ဆွဲ',3200,1,1],[2,'Nan Gyi Thoke','နန်းကြီးသုပ်','Thick rice noodle salad','ခေါက်ဆွဲထူသုပ်',2800,1,0],[2,'Khauk Swe Thoke','ခေါက်ဆွဲသုပ်','Noodle salad','ခေါက်ဆွဲသုပ်ရောစပ်',2800,1,0],[3,'Chicken Curry','ကြက်သားဟင်း','Spicy chicken curry','စပ်သော ကြက်သားဟင်း',4000,1,1],[3,'Pork Curry','ဝက်သားဟင်း','Spicy pork curry','စပ်သော ဝက်သားဟင်း',4200,1,1],[3,'Fish Curry','ငါးဟင်း','Traditional fish curry','ရိုးရာ ငါးဟင်း',3800,1,0],[3,'Beef Curry','နွားသားဟင်း','Rich beef curry','နွားသားဟင်းချို',4500,1,1],[3,'Mutton Curry','သိုးသားဟင်း','Tender mutton curry','သိုးသားဟင်းချို',5000,1,0],[3,'Prawn Curry','ပုစွန်ဟင်း','Spicy prawn curry','စပ်သော ပုစွန်ဟင်း',4800,1,1],[4,'Tea Leaf Salad','လပက်သုပ်','Traditional tea leaf salad','မြန်မာ့ရိုးရာ လပက်သုပ်',3000,1,1],[4,'Ginger Salad','ချင်းသုပ်','Fresh ginger salad','ချင်းလတ်သုပ်',2500,1,0],[4,'Tomato Salad','ခရမ်းချဉ်သုပ်','Fresh tomato salad','ခရမ်းချဉ်လတ်သုပ်',2200,1,0],[4,'Cucumber Salad','သခွားသီးသုပ်','Refreshing cucumber salad','သခွားသီးလတ်သုပ်',2000,1,0],[4,'Pennywort Salad','မြက်ပုပ်သုပ်','Healthy pennywort salad','မြက်ပုပ်လတ်သုပ်',2800,1,1],[5,'Shwe Yin Aye','ရွှေရင်အေး','Traditional Myanmar dessert','မြန်မာ့ရိုးရာ အချိုပွဲ',2000,1,0],[5,'Mont Lone Yay Paw','မုန့်လုံးရေပေါ်','Glutinous rice balls in coconut milk','အုန်းရည်နှင့် မုန့်လုံး',1800,1,0],[5,'Thagu','သာကူ','Sago pudding','သာကူပေါင်း',1500,1,0],[5,'Sanwin Makin','ဆန်ဝင်မကင်း','Semolina cake','ဆန်ဝင်မုန့်',2200,1,1],[5,'Htamanay','ထမနဲ','Sticky rice cake','ကောက်ညှင်းမုန့်',2000,1,0],[5,'Ice Cream','ရေခဲမုန့်','Homemade ice cream','အိမ်လုပ်ရေခဲမုန့်',2500,1,0],[6,'Myanmar Tea','မြန်မာလက်ဖက်ရည်','Traditional Myanmar tea','မြန်မာ့ရိုးရာ လက်ဖက်ရည်',800,1,0],[6,'Lime Juice','သံပုရာရည်','Fresh lime juice','သံပုရာရည်လတ်',1200,1,0],[6,'Sugarcane Juice','ကြံရည်','Fresh sugarcane juice','ကြံရည်လတ်',1500,1,0],[6,'Coconut Water','အုန်းရည်','Fresh coconut water','အုန်းရည်လတ်',1800,1,0],[7,'Samosa','ဆမူဆာ','Crispy samosa','ဆမူဆာကြွပ်',1500,1,0],[7,'Spring Roll','ကော်ပြန့်','Fresh spring roll','ကော်ပြန့်လတ်',2000,1,0],[7,'Htamin Jin','ထမင်းချဉ်','Fermented rice snack','ထမင်းချဉ်မုန့်',1200,1,0],[8,'Grilled Chicken','ကြက်သားကင်','BBQ grilled chicken','ကြက်သားကင်ပေါင်း',5500,1,0],[8,'Grilled Pork','ဝက်သားကင်','BBQ grilled pork','ဝက်သားကင်ပေါင်း',6000,1,0],[8,'Beef Steak','နွားသားစတိတ်','Tender beef steak','နွားသားစတိတ်ပြား',7500,1,0],[8,'Lamb Chops','သိုးသားကင်','Grilled lamb chops','သိုးသားကင်ပေါင်း',8000,1,0]];let completed=0;items.forEach(item=>{db.run('INSERT INTO menu_items(category_id,name,name_mm,description,description_mm,price,is_available,is_today_special,is_active)VALUES(?,?,?,?,?,?,?,?,1)',item,(err)=>{completed++;if(completed===items.length){console.log('✅ 40 Menu items inserted successfully!');process.exit(0);}});});"
        echo ✅ Menu items inserted successfully!
        echo.
        echo 📊 Step 3/6: Inserting Users...
        node -e "const{getDb}=require('./database/db');const bcrypt=require('bcryptjs');const db=getDb();const users=[['admin','admin123','admin','Administrator'],['waiter1','waiter123','staff','Waiter 1'],['waiter2','waiter123','staff','Waiter 2'],['kitchen1','kitchen123','kitchen','Kitchen Staff'],['cashier1','cashier123','cashier','Cashier']];let completed=0;users.forEach(user=>{const hashedPassword=bcrypt.hashSync(user[1],10);db.run('INSERT OR REPLACE INTO users(username,password,role,name)VALUES(?,?,?,?)',[user[0],hashedPassword,user[2],user[3]],(err)=>{completed++;if(completed===users.length){console.log('✅ 5 Users inserted successfully!');process.exit(0);}});});"
        echo ✅ Users inserted successfully!
        echo.
        echo 📊 Step 4/6: Inserting Tables...
        node -e "const{getDb}=require('./database/db');const db=getDb();const tables=[['T001','Table 1',4],['T002','Table 2',4],['T003','Table 3',6],['T004','Table 4',4],['T005','Table 5',2],['T006','Table 6',4],['T007','Table 7',8],['T008','Table 8',4],['T009','Table 9',4],['T010','Table 10',6]];let completed=0;tables.forEach(table=>{db.run('INSERT OR REPLACE INTO tables(table_number,table_name,capacity,is_active)VALUES(?,?,?,1)',table,(err)=>{completed++;if(completed===tables.length){console.log('✅ 10 Tables inserted successfully!');process.exit(0);}});});"
        echo ✅ Tables inserted successfully!
        echo.
        echo 📊 Step 5/6: Inserting Settings...
        node -e "const{getDb}=require('./database/db');const db=getDb();const settings=[['restaurant_name','A Shin Restaurant'],['restaurant_name_mm','အရှင်စားသောက်ဆိုင်'],['restaurant_title_en','Myanmar Traditional Cuisine'],['restaurant_title_mm','မြန်မာ့ရိုးရာ အစားအသောက်များ'],['address','Yangon, Myanmar'],['phone','09-***********'],['email','<EMAIL>'],['opening_hours_en','Always Open for You'],['opening_hours_mm','အမြဲဖွင့်ထားပါသည်'],['currency','MMK'],['tax_rate','5'],['service_charge','10']];let completed=0;settings.forEach(setting=>{db.run('INSERT OR REPLACE INTO settings(key,value)VALUES(?,?)',setting,(err)=>{completed++;if(completed===settings.length){console.log('✅ 12 Settings inserted successfully!');process.exit(0);}});});"
        echo ✅ Settings inserted successfully!
        echo.
        echo 📊 Step 6/6: Inserting Sample Orders...
        node -e "const{getDb}=require('./database/db');const db=getDb();const orders=[['T001','John Doe',7500,'completed'],['T002','Jane Smith',4500,'pending'],['T003','Bob Johnson',6200,'preparing'],['T004','Alice Brown',3800,'completed'],['T005','Charlie Wilson',5900,'pending']];let completed=0;orders.forEach(order=>{db.run('INSERT INTO orders(table_number,customer_name,total_amount,status,created_at)VALUES(?,?,?,?,datetime(\"now\"))',order,(err)=>{completed++;if(completed===orders.length){console.log('✅ 5 Sample orders inserted successfully!');process.exit(0);}});});"
        echo ✅ Sample orders inserted successfully!
        echo.
        echo Default Login Credentials:
        echo ================================
        echo Admin User:
        echo   Username: admin
        echo   Password: admin123
        echo.
        echo Kitchen User:
        echo   Username: kitchen1
        echo   Password: kitchen123
        echo.
        echo Waiter Users:
        echo   Username: waiter1 / waiter2
        echo   Password: waiter123
        echo.
        echo Cashier User:
        echo   Username: cashier1
        echo   Password: cashier123
        echo ================================
        echo.
        echo ✓ Database setup completed with complete sample data
    ) else (
        echo ✗ Database setup failed
        echo This might be due to missing dependencies
        echo Try running option [1] first for complete setup
    )
    cd /d "%~dp0"
) else (
    echo ⚠ Database module not found
    echo Please run option [1] for complete setup first
)

echo.
echo ================================================================
echo                Database Setup Complete!
echo ================================================================
echo.
echo ✓ Database files cleaned
echo ✓ Directory structure ready
echo ✓ Database initialized with complete sample data
echo ✓ All users created (5 users)
echo ✓ All menu items added (40 items)
echo ✓ All categories and tables set up (10 each)
echo ✓ Restaurant settings configured
echo ✓ Sample orders inserted
echo.
echo 🎉 Ready to use! Complete sample data includes:
echo    • 5 Users (admin, kitchen, waiters, cashier)
echo    • 10 Categories (Rice, Noodles, Curry, Salads, Desserts, etc.)
echo    • 40 Menu Items (10 special items included)
echo    • 10 Tables (T001-T010)
echo    • 12 Restaurant settings
echo    • 5 Sample orders
echo.
echo Next Steps:
echo • Use option [6] to verify installation
echo • Use option [7] to go to Main Menu and start servers
echo • Use Step 2 - Server Management to start both servers
echo • Use Step 6 - Open Web Interface to access the system
echo.
echo Returning to main menu...
pause
goto MENU

:SYSTEM_CHECK
echo.
echo ================================================================
echo              Step 0.4 - System Requirements Check
echo ================================================================
echo Checking system requirements and project status...
echo.

echo [1/5] Node.js Check...
node --version >nul 2>&1
if %errorlevel%==0 (
    echo ✓ Node.js installed
    for /f "tokens=*" %%i in ('node --version 2^>nul') do echo   Version: %%i
    echo   Status: OK (minimum v14.0.0 required)
) else (
    echo ✗ Node.js not found
    echo   Download from: https://nodejs.org
    echo   Status: REQUIRED
)

echo.
echo [2/5] npm Check...
where npm >nul 2>&1
if %errorlevel%==0 (
    echo ✓ npm available
    for /f "tokens=*" %%i in ('npm --version 2^>nul') do echo   Version: %%i
    echo   Status: OK (minimum v6.0.0 required)
) else (
    echo ✗ npm not found
    echo   Should be included with Node.js
    echo   Status: REQUIRED
)

echo.
echo [3/5] Project Files Check...
if exist "package.json" (
    echo ✓ package.json found
) else (
    echo ✗ package.json missing
)

if exist "node_modules" (
    echo ✓ node_modules found
    echo   Dependencies: INSTALLED
) else (
    echo ✗ node_modules missing
    echo   Dependencies: NOT INSTALLED (run option [1] or [2])
)

echo.
echo [4/5] Directory Structure...
if exist "server" (
    echo ✓ server directory found
) else (
    echo ✗ server directory missing
)

if exist "server\database" (
    echo ✓ database directory found
) else (
    echo ✗ database directory missing
)

echo.
echo [5/5] Database Status...
if exist "server\database\restaurant.db" (
    echo ✓ Database file exists
    echo   Status: READY
) else (
    echo ⚠ Database file not found
    echo   Status: NEEDS SETUP (run option [3])
)

echo.
echo ================================================================
echo                  System Check Complete!
echo ================================================================
echo.
echo Summary:
echo • Node.js and npm status checked
echo • Project files verified
echo • Directory structure examined
echo • Database status reviewed
echo.
echo Returning to main menu...
pause
goto MENU

:EXTERNAL_TOOLS
echo.
echo Step 0.5 - External Tools Installation Guide...
echo =======================================
echo Guide for installing external development tools
echo.

echo [1] Git (Version Control)
echo     Download: https://git-scm.com/download/win
echo     Purpose: Source code management
echo.
echo [2] Visual Studio Code (Code Editor)
echo     Download: https://code.visualstudio.com/
echo     Purpose: Code editing
echo.
echo [3] Postman (API Testing)
echo     Download: https://www.postman.com/downloads/
echo     Purpose: API testing
echo.
echo [4] Chrome/Firefox (Web Browser)
echo     Purpose: Web interface testing
echo.
echo [5] Windows Terminal (Better Command Line)
echo     Download: "Windows Terminal" from Microsoft Store
echo     Purpose: Better command line experience
echo.

echo Note: These tools are optional and require manual installation
echo.
echo Returning to main menu...
pause
goto MENU

:VERIFY_INSTALLATION
echo.
echo ================================================================
echo               Step 0.6 - Verify Installation
echo ================================================================
echo Testing all components to ensure everything works correctly...
echo.

echo [1/4] Dependencies Check...
if exist "node_modules" (
    echo ✓ node_modules found
    npm list --depth=0 >nul 2>&1
    if %errorlevel%==0 (
        echo ✓ Dependencies are properly installed
    ) else (
        echo ⚠ Dependencies have some issues but may still work
    )
) else (
    echo ✗ node_modules missing
    echo   Run option [1] or [2] to install dependencies
)

echo.
echo [2/4] Project Structure Check...
if exist "package.json" (
    echo ✓ package.json found
) else (
    echo ✗ package.json missing
)

if exist "server" (
    echo ✓ server directory found
) else (
    echo ✗ server directory missing
)

echo.
echo [3/4] Database Check...
if exist "server\database" (
    echo ✓ database directory found
    if exist "server\database\restaurant.db" (
        echo ✓ Database file exists
        echo   Testing database connection...
        cd /d "%~dp0\server"
        node -e "try{require('./database/db');console.log('✓ Database module accessible');}catch(e){console.log('⚠ Database module has issues');}" 2>nul
        cd /d "%~dp0"
    ) else (
        echo ⚠ Database file not found
        echo   Run option [3] to set up database
    )
) else (
    echo ✗ database directory missing
    echo   Run option [1] for complete setup
)

echo.
echo [4/4] Configuration Check...
if exist "package.json" (
    findstr "scripts" package.json >nul 2>&1
    if %errorlevel%==0 (
        echo ✓ Scripts section found in package.json
    ) else (
        echo ⚠ Scripts section missing in package.json
    )
) else (
    echo ✗ Cannot check package.json scripts
)

echo.
echo ================================================================
echo                 Verification Complete!
echo ================================================================
echo.

if exist "node_modules" if exist "server" if exist "package.json" (
    echo ✅ VERIFICATION PASSED!
    echo.
    echo ✓ All essential components found
    echo ✓ Project structure is correct
    echo ✓ Dependencies are installed
    echo ✓ Ready to run the application
    echo.
    echo � Next Steps:
    echo   1. Use option [7] to go to Main Menu
    echo   2. Start servers with Server Management
    echo   3. Insert sample data with Data Management
    echo   4. Access web interface
    echo.
    echo 🌐 Application URLs:
    echo   Frontend: http://localhost:3002
    echo   Backend API: http://localhost:5000
    echo   Admin Panel: http://localhost:3002/admin
    echo.
    echo 👤 Default Admin Login:
    echo   Username: admin
    echo   Password: admin123
) else (
    echo ⚠ VERIFICATION ISSUES FOUND
    echo.
    echo � Recommended Actions:
    if not exist "node_modules" (
        echo   • Run option [1] or [2] to install dependencies
    )
    if not exist "server" (
        echo   • Run option [1] for complete setup
    )
    if not exist "package.json" (
        echo   • Check if you're in the correct directory
    )
    if not exist "server\database" (
        echo   • Run option [3] to set up database
    )
    echo.
    echo   After fixing issues, run this verification again
)

echo.
echo ================================================================
echo Returning to main menu...
pause
goto MENU

:START_SERVERS
echo.
echo ================================================================
echo               Step 0.7 - Start Servers and Open Web Interface
echo ================================================================
echo Starting both Backend and Frontend servers and opening web interface...
echo.

echo [1/4] Checking if servers are already running...
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:5000/api/health' -TimeoutSec 2 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel%==0 (
    echo ⚠ Backend server already running on port 5000
    echo Stopping existing servers first...
    taskkill /f /im node.exe >nul 2>&1
    timeout /t 2 /nobreak >nul
)

echo [2/4] Starting Backend Server (Port 5000)...
start "Backend Server - Port 5000" cmd /k "cd /d \"%~dp0\" && npm run server"
echo ⏳ Waiting for backend server to start (5 seconds)...
timeout /t 5 /nobreak >nul

echo [3/4] Starting Frontend Server (Port 3002)...
start "Frontend Server - Port 3002" cmd /k "cd /d \"%~dp0\" && npm run dev"
echo ⏳ Waiting for frontend server to start (8 seconds)...
timeout /t 8 /nobreak >nul

echo [4/4] Opening Web Interface...
echo Checking if servers are ready...
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:5000/api/health' -TimeoutSec 3 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel%==0 (
    echo ✅ Backend server is ready
    powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:3002' -TimeoutSec 3 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
    if %errorlevel%==0 (
        echo ✅ Frontend server is ready
        echo 🌐 Opening web interface in browser...
        start http://localhost:3002
        echo.
        echo ================================================================
        echo                    🎉 Setup Complete!
        echo ================================================================
        echo.
        echo ✅ Both servers are running successfully!
        echo.
        echo 📋 Server Information:
        echo   🔧 Backend API: http://localhost:5000
        echo   🌐 Frontend Web: http://localhost:3002
        echo   👨‍💼 Admin Panel: http://localhost:3002/admin
        echo   📱 Counter Page: http://localhost:3002/counter
        echo   🍽️ Menu Page: http://localhost:3002/menu
        echo.
        echo 👤 Default Login Credentials:
        echo   Admin: admin / admin123
        echo   Kitchen: kitchen1 / kitchen123
        echo   Waiter: waiter1 / waiter123
        echo   Cashier: cashier1 / cashier123
        echo.
        echo 📊 Sample Data Ready:
        echo   • 10 Categories, 40 Menu Items (10 special)
        echo   • 5 Users, 10 Tables, 5 Sample Orders
        echo   • Complete restaurant settings
        echo.
        echo ⚠️ Note: Keep the server windows open while using the system
        echo    Use Ctrl+C in server windows to stop servers
        echo.
    ) else (
        echo ⚠ Frontend server not responding yet
        echo Please wait a moment and try accessing http://localhost:3002 manually
    )
) else (
    echo ⚠ Backend server not responding yet
    echo Please check the Backend Server window for any errors
)

echo.
echo Returning to main menu...
pause
goto MENU

:MAIN_MENU
echo.
echo ================================================================
echo                 Going to Main Menu
echo ================================================================
echo.
echo Checking for Main Launcher...
if exist "Step 1 - Main Launcher (English).bat" (
    echo ✓ Main Launcher found
    echo.
    echo Opening Main Menu...
    echo Please wait...
    timeout /t 2 /nobreak >nul
    call "Step 1 - Main Launcher (English).bat"
) else (
    echo ✗ Main Launcher not found!
    echo.
    echo Expected file: "Step 1 - Main Launcher (English).bat"
    echo Current directory: %CD%
    echo.
    echo Please ensure the Main Launcher file exists in the same directory.
    echo.
    echo Returning to setup menu...
    pause
    goto MENU
)
goto EXIT

:INVALID
echo.
echo Invalid choice! Please choose 1-9.
echo Returning to main menu...
pause
goto MENU

:EXIT
echo.
echo ================================================================
echo                    Thank You!
echo.
echo              Restaurant Management System
echo.
echo                    Have a great day!
echo ================================================================
echo.
echo Exiting...
exit
