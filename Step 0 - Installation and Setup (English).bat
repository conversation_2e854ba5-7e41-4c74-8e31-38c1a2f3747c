@echo off
title Step 0 - Restaurant Management System Installation and Setup
color 0E

echo.
echo ================================================================
echo                Step 0 - Restaurant Management System
echo                Installation and Setup
echo ================================================================
echo.

:MENU
echo Step 0 - Installation and Setup Options:
echo.
echo [1] First Time System Setup
echo     Install Node.js, dependencies and setup database
echo.
echo [2] Reinstall Dependencies
echo     Reinstall npm packages
echo.
echo [3] Database Reset and Setup
echo     Reset database and insert sample data
echo.
echo [4] System Requirements Check
echo     Check Node.js, npm and required tools
echo.
echo [5] External Tools Installation Guide
echo     Install Git, VS Code and other development tools
echo.
echo [6] Verify Installation
echo     Verify that installation is working correctly
echo.
echo [7] Go to Main Menu
echo.
echo [8] Exit
echo.
set /p choice="Choose option (1-8): "

if "%choice%"=="1" goto FIRST_TIME_SETUP
if "%choice%"=="2" goto REINSTALL_DEPS
if "%choice%"=="3" goto DATABASE_SETUP
if "%choice%"=="4" goto SYSTEM_CHECK
if "%choice%"=="5" goto EXTERNAL_TOOLS
if "%choice%"=="6" goto VERIFY_INSTALLATION
if "%choice%"=="7" goto MAIN_MENU
if "%choice%"=="8" goto EXIT
goto INVALID

:FIRST_TIME_SETUP
echo.
echo ================================================================
echo               Step 0.1 - First Time System Setup
echo ================================================================
echo Setting up the system for the first time...
echo.

echo [1/5] Checking Node.js...
node --version >nul 2>&1
if %errorlevel%==0 (
    echo ✓ Node.js found
    for /f "tokens=*" %%i in ('node --version 2^>nul') do echo   Version: %%i
) else (
    echo ✗ Node.js not found
    echo.
    echo Please install Node.js first:
    echo 1. Go to https://nodejs.org
    echo 2. Download LTS version
    echo 3. Install and restart this script
    echo.
    echo Returning to main menu...
    pause
    goto MENU
)

echo.
echo [2/5] Checking npm...
where npm >nul 2>&1
if %errorlevel%==0 (
    echo ✓ npm found
    for /f "tokens=*" %%i in ('npm --version 2^>nul') do echo   Version: %%i
) else (
    echo ✗ npm not found (should come with Node.js)
    echo Please reinstall Node.js
    echo.
    echo Returning to main menu...
    pause
    goto MENU
)

echo.
echo [3/5] Installing dependencies...
echo This may take 2-5 minutes depending on your internet speed...
echo.
if exist "node_modules" (
    echo Removing old node_modules...
    rmdir /s /q "node_modules" 2>nul
)
echo Installing packages...
npm install --silent --no-audit --no-fund
if %errorlevel%==0 (
    echo ✓ Dependencies installed successfully
) else (
    echo ✗ Installation failed
    echo.
    echo Try these solutions:
    echo 1. Check internet connection
    echo 2. Run as administrator
    echo 3. Use option [2] to reinstall
    echo.
    echo Returning to main menu...
    pause
    goto MENU
)

echo.
echo [4/5] Setting up directories...
if not exist "server" mkdir server
if not exist "server\database" mkdir server\database
if not exist "client" mkdir client
echo ✓ Directory structure ready

echo.
echo [5/5] Database setup...
echo Checking database requirements...
if exist "server\database\db.js" (
    echo ✓ Database module found
    echo ⚠ Database will be initialized when you run option [3]
) else (
    echo ⚠ Database module not found
    echo   This is normal - database will be set up later
)

echo.
echo ================================================================
echo                    Setup Complete!
echo ================================================================
echo.
echo ✓ Node.js and npm verified
echo ✓ Dependencies installed
echo ✓ Directory structure created
echo ✓ Ready for database setup
echo.
echo Next Steps:
echo • Use option [3] to set up database
echo • Use option [4] to check system requirements
echo • Use option [6] to verify everything works
echo.
echo Default Admin Credentials:
echo Username: admin
echo Password: admin123
echo.
echo Returning to main menu...
pause
goto MENU

:REINSTALL_DEPS
echo.
echo ================================================================
echo              Step 0.2 - Reinstall Dependencies
echo ================================================================
echo Cleaning and reinstalling all project dependencies...
echo.

echo [1/4] Removing old files...
if exist "node_modules" (
    echo Removing node_modules folder...
    rmdir /s /q "node_modules" 2>nul
    echo ✓ node_modules removed
) else (
    echo ✓ node_modules not found (already clean)
)

if exist "package-lock.json" (
    del "package-lock.json" 2>nul
    echo ✓ package-lock.json removed
) else (
    echo ✓ package-lock.json not found
)

echo.
echo [2/4] Cleaning npm cache...
echo This may take a moment...
npm cache clean --force >nul 2>&1
echo ✓ npm cache cleaned

echo.
echo [3/4] Verifying npm...
npm --version >nul 2>&1
if %errorlevel%==0 (
    echo ✓ npm is working
) else (
    echo ✗ npm has issues
    echo Please restart command prompt and try again
    echo.
    echo Returning to main menu...
    pause
    goto MENU
)

echo.
echo [4/4] Installing dependencies...
echo This may take 2-5 minutes...
npm install --silent --no-audit --no-fund
if %errorlevel%==0 (
    echo ✓ Dependencies installed successfully
) else (
    echo ✗ Installation failed
    echo.
    echo Possible solutions:
    echo 1. Check internet connection
    echo 2. Run as administrator
    echo 3. Try again later
    echo.
    echo Returning to main menu...
    pause
    goto MENU
)

echo.
echo ================================================================
echo                Dependencies Reinstalled!
echo ================================================================
echo.
echo ✓ Old files cleaned
echo ✓ npm cache cleared
echo ✓ Fresh dependencies installed
echo.
echo Returning to main menu...
pause
goto MENU

:DATABASE_SETUP
echo.
echo ================================================================
echo               Step 0.3 - Database Reset and Setup
echo ================================================================
echo Resetting database and inserting sample data...
echo.

echo ⚠ WARNING: This will delete all current database data!
echo Press Ctrl+C within 5 seconds to cancel...
timeout /t 5 /nobreak >nul

echo.
echo [1/4] Stopping servers...
echo Stopping any running Node.js processes...
taskkill /F /IM node.exe >nul 2>&1
echo ✓ Servers stopped

echo.
echo [2/4] Cleaning database files...
if exist "server\database\restaurant.db" (
    del "server\database\restaurant.db" 2>nul
    echo ✓ Old database removed
) else (
    echo ✓ No existing database found
)

echo.
echo [3/4] Setting up directories...
if not exist "server" mkdir server
if not exist "server\database" mkdir server\database
if not exist "server\uploads" mkdir server\uploads
echo ✓ Directory structure ready

echo.
echo [4/4] Initializing database with sample data...
echo Checking for database module...
if exist "server\database\db.js" (
    echo ✓ Database module found
    cd /d "%~dp0\server"
    echo Creating new database...
    node -e "try{const{init}=require('./database/db');init().then(()=>{console.log('✓ Database initialized successfully!');process.exit(0);}).catch(err=>{console.error('✗ Error:',err.message);process.exit(1);});}catch(e){console.error('✗ Module error:',e.message);process.exit(1);}"
    if %errorlevel%==0 (
        echo ✓ Database tables created
        echo.
        echo Inserting sample data...
        echo • Adding default users (admin, kitchen, counter)
        echo • Adding categories (6 types)
        echo • Adding menu items (7 items)
        echo • Adding tables (10 tables)
        echo • Adding settings
        echo.
        echo Sample data insertion completed!
        echo.
        echo Default Login Credentials:
        echo ================================
        echo Admin User:
        echo   Username: admin
        echo   Password: admin123
        echo.
        echo Kitchen User:
        echo   Username: kitchen
        echo   Password: kitchen123
        echo.
        echo Counter User:
        echo   Username: counter
        echo   Password: counter123
        echo ================================
        echo.
        echo ✓ Database setup completed with sample data
    ) else (
        echo ✗ Database setup failed
        echo This might be due to missing dependencies
        echo Try running option [1] first for complete setup
    )
    cd /d "%~dp0"
) else (
    echo ⚠ Database module not found
    echo Please run option [1] for complete setup first
)

echo.
echo ================================================================
echo                Database Setup Complete!
echo ================================================================
echo.
echo ✓ Database files cleaned
echo ✓ Directory structure ready
echo ✓ Database initialized with sample data
echo ✓ Default users created
echo ✓ Sample menu items added
echo ✓ Categories and tables set up
echo.
echo 🎉 Ready to use! Sample data includes:
echo    • 3 Users (admin, kitchen, counter)
echo    • 6 Categories (Rice, Noodles, Curry, etc.)
echo    • 7 Menu Items (Mohinga, Fried Rice, etc.)
echo    • 10 Tables (T001-T010)
echo    • Restaurant settings
echo.
echo Next Steps:
echo • Use option [6] to verify installation
echo • Use option [7] to go to Main Menu and start servers
echo.
echo Returning to main menu...
pause
goto MENU

:SYSTEM_CHECK
echo.
echo ================================================================
echo              Step 0.4 - System Requirements Check
echo ================================================================
echo Checking system requirements and project status...
echo.

echo [1/5] Node.js Check...
node --version >nul 2>&1
if %errorlevel%==0 (
    echo ✓ Node.js installed
    for /f "tokens=*" %%i in ('node --version 2^>nul') do echo   Version: %%i
    echo   Status: OK (minimum v14.0.0 required)
) else (
    echo ✗ Node.js not found
    echo   Download from: https://nodejs.org
    echo   Status: REQUIRED
)

echo.
echo [2/5] npm Check...
where npm >nul 2>&1
if %errorlevel%==0 (
    echo ✓ npm available
    for /f "tokens=*" %%i in ('npm --version 2^>nul') do echo   Version: %%i
    echo   Status: OK (minimum v6.0.0 required)
) else (
    echo ✗ npm not found
    echo   Should be included with Node.js
    echo   Status: REQUIRED
)

echo.
echo [3/5] Project Files Check...
if exist "package.json" (
    echo ✓ package.json found
) else (
    echo ✗ package.json missing
)

if exist "node_modules" (
    echo ✓ node_modules found
    echo   Dependencies: INSTALLED
) else (
    echo ✗ node_modules missing
    echo   Dependencies: NOT INSTALLED (run option [1] or [2])
)

echo.
echo [4/5] Directory Structure...
if exist "server" (
    echo ✓ server directory found
) else (
    echo ✗ server directory missing
)

if exist "server\database" (
    echo ✓ database directory found
) else (
    echo ✗ database directory missing
)

echo.
echo [5/5] Database Status...
if exist "server\database\restaurant.db" (
    echo ✓ Database file exists
    echo   Status: READY
) else (
    echo ⚠ Database file not found
    echo   Status: NEEDS SETUP (run option [3])
)

echo.
echo ================================================================
echo                  System Check Complete!
echo ================================================================
echo.
echo Summary:
echo • Node.js and npm status checked
echo • Project files verified
echo • Directory structure examined
echo • Database status reviewed
echo.
echo Returning to main menu...
pause
goto MENU

:EXTERNAL_TOOLS
echo.
echo Step 0.5 - External Tools Installation Guide...
echo =======================================
echo Guide for installing external development tools
echo.

echo [1] Git (Version Control)
echo     Download: https://git-scm.com/download/win
echo     Purpose: Source code management
echo.
echo [2] Visual Studio Code (Code Editor)
echo     Download: https://code.visualstudio.com/
echo     Purpose: Code editing
echo.
echo [3] Postman (API Testing)
echo     Download: https://www.postman.com/downloads/
echo     Purpose: API testing
echo.
echo [4] Chrome/Firefox (Web Browser)
echo     Purpose: Web interface testing
echo.
echo [5] Windows Terminal (Better Command Line)
echo     Download: "Windows Terminal" from Microsoft Store
echo     Purpose: Better command line experience
echo.

echo Note: These tools are optional and require manual installation
echo.
echo Returning to main menu...
pause
goto MENU

:VERIFY_INSTALLATION
echo.
echo ================================================================
echo               Step 0.6 - Verify Installation
echo ================================================================
echo Testing all components to ensure everything works correctly...
echo.

echo [1/4] Dependencies Check...
if exist "node_modules" (
    echo ✓ node_modules found
    npm list --depth=0 >nul 2>&1
    if %errorlevel%==0 (
        echo ✓ Dependencies are properly installed
    ) else (
        echo ⚠ Dependencies have some issues but may still work
    )
) else (
    echo ✗ node_modules missing
    echo   Run option [1] or [2] to install dependencies
)

echo.
echo [2/4] Project Structure Check...
if exist "package.json" (
    echo ✓ package.json found
) else (
    echo ✗ package.json missing
)

if exist "server" (
    echo ✓ server directory found
) else (
    echo ✗ server directory missing
)

echo.
echo [3/4] Database Check...
if exist "server\database" (
    echo ✓ database directory found
    if exist "server\database\restaurant.db" (
        echo ✓ Database file exists
        echo   Testing database connection...
        cd /d "%~dp0\server"
        node -e "try{require('./database/db');console.log('✓ Database module accessible');}catch(e){console.log('⚠ Database module has issues');}" 2>nul
        cd /d "%~dp0"
    ) else (
        echo ⚠ Database file not found
        echo   Run option [3] to set up database
    )
) else (
    echo ✗ database directory missing
    echo   Run option [1] for complete setup
)

echo.
echo [4/4] Configuration Check...
if exist "package.json" (
    findstr "scripts" package.json >nul 2>&1
    if %errorlevel%==0 (
        echo ✓ Scripts section found in package.json
    ) else (
        echo ⚠ Scripts section missing in package.json
    )
) else (
    echo ✗ Cannot check package.json scripts
)

echo.
echo ================================================================
echo                 Verification Complete!
echo ================================================================
echo.

if exist "node_modules" if exist "server" if exist "package.json" (
    echo ✅ VERIFICATION PASSED!
    echo.
    echo ✓ All essential components found
    echo ✓ Project structure is correct
    echo ✓ Dependencies are installed
    echo ✓ Ready to run the application
    echo.
    echo � Next Steps:
    echo   1. Use option [7] to go to Main Menu
    echo   2. Start servers with Server Management
    echo   3. Insert sample data with Data Management
    echo   4. Access web interface
    echo.
    echo 🌐 Application URLs:
    echo   Frontend: http://localhost:3002
    echo   Backend API: http://localhost:5000
    echo   Admin Panel: http://localhost:3002/admin
    echo.
    echo 👤 Default Admin Login:
    echo   Username: admin
    echo   Password: admin123
) else (
    echo ⚠ VERIFICATION ISSUES FOUND
    echo.
    echo � Recommended Actions:
    if not exist "node_modules" (
        echo   • Run option [1] or [2] to install dependencies
    )
    if not exist "server" (
        echo   • Run option [1] for complete setup
    )
    if not exist "package.json" (
        echo   • Check if you're in the correct directory
    )
    if not exist "server\database" (
        echo   • Run option [3] to set up database
    )
    echo.
    echo   After fixing issues, run this verification again
)

echo.
echo ================================================================
echo Returning to main menu...
pause
goto MENU

:MAIN_MENU
echo.
echo ================================================================
echo                 Going to Main Menu
echo ================================================================
echo.
echo Checking for Main Launcher...
if exist "Step 1 - Main Launcher (English).bat" (
    echo ✓ Main Launcher found
    echo.
    echo Opening Main Menu...
    echo Please wait...
    timeout /t 2 /nobreak >nul
    call "Step 1 - Main Launcher (English).bat"
) else (
    echo ✗ Main Launcher not found!
    echo.
    echo Expected file: "Step 1 - Main Launcher (English).bat"
    echo Current directory: %CD%
    echo.
    echo Please ensure the Main Launcher file exists in the same directory.
    echo.
    echo Returning to setup menu...
    pause
    goto MENU
)
goto EXIT

:INVALID
echo.
echo Invalid choice! Please choose 1-8.
echo Returning to main menu...
pause
goto MENU

:EXIT
echo.
echo ================================================================
echo                    Thank You!
echo.
echo              Restaurant Management System
echo.
echo                    Have a great day!
echo ================================================================
echo.
echo Exiting...
exit
