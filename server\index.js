const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

// Import routes
const authRoutes = require('./routes/auth');
const menuRoutes = require('./routes/menu');
const orderRoutes = require('./routes/orders');
const adminRoutes = require('./routes/admin');
const settingsRoutes = require('./routes/settings');
const uploadRoutes = require('./routes/upload');
const notificationsRoutes = require('./routes/notifications');
const reportsRoutes = require('./routes/reports');
const analyticsRoutes = require('./routes/analytics');
const financialRoutes = require('./routes/financial');

// Import database
const db = require('./database/db');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: ['http://localhost:3002', 'http://***************:3002', 'http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files (uploaded images)
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Make io available to routes
app.set('io', io);

// Routes
const tableRoutes = require('./routes/tables');

app.use('/api/auth', authRoutes);
app.use('/api/menu', menuRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/tables', tableRoutes);
app.use('/api/settings', settingsRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api', notificationsRoutes);
app.use('/api/bell-alerts', require('./routes/bell-alerts'));
app.use('/api/payment-requests', require('./routes/payment-requests'));
app.use('/api/reports', reportsRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/financial', financialRoutes);

// Socket.io for real-time communication
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  // Join room based on user role
  socket.on('join-room', (room) => {
    socket.join(room);
    console.log(`User ${socket.id} joined room: ${room}`);
  });

  // Join table room for cart synchronization
  socket.on('join-table', (tableNumber) => {
    const tableRoom = `table-${tableNumber}`;
    socket.join(tableRoom);
    console.log(`User ${socket.id} joined table room: ${tableRoom}`);

    // Send current cart state to new user
    socket.emit('cart-sync-request', { tableNumber });
  });

  // Handle cart updates
  socket.on('cart-update', (data) => {
    const { tableNumber, cart, action, item } = data;
    const tableRoom = `table-${tableNumber}`;

    console.log(`Cart update for table ${tableNumber}:`, action);

    // Broadcast cart update to all users at the same table
    socket.to(tableRoom).emit('cart-updated', {
      cart,
      action,
      item,
      updatedBy: socket.id
    });
  });

  // Handle cart sync response
  socket.on('cart-sync-response', (data) => {
    const { tableNumber, cart } = data;
    const tableRoom = `table-${tableNumber}`;

    // Send cart state to requesting user
    socket.to(tableRoom).emit('cart-synced', { cart });
  });

  // Handle new order
  socket.on('new-order', (orderData) => {
    // Broadcast to counter and kitchen
    io.to('counter').emit('order-received', orderData);
    io.to('kitchen').emit('order-received', orderData);

    // Clear cart for all users at the table
    if (orderData.table_number) {
      const tableRoom = `table-${orderData.table_number}`;
      io.to(tableRoom).emit('cart-cleared', {
        message: 'အမှာပေးပြီးပါပြီ! Cart ကို ရှင်းလင်းပြီးပါပြီ။'
      });
    }
  });

  // Handle order status updates
  socket.on('order-status-update', (data) => {
    // Broadcast to all relevant parties
    io.to('counter').emit('order-updated', data);
    io.to('kitchen').emit('order-updated', data);
    io.to(`customer-${data.customerId}`).emit('order-updated', data);
  });

  // Handle customer notifications
  socket.on('customer-notification', (data) => {
    const { tableNumber, message, type } = data;
    const tableRoom = `table-${tableNumber}`;

    // Send notification to all users at the table
    io.to(tableRoom).emit('notification-received', {
      message,
      type,
      timestamp: new Date().toISOString()
    });
  });

  // Handle payment request updates
  socket.on('payment-request-update', (data) => {
    // Broadcast to counter staff
    io.to('counter').emit('payment-request-updated', data);
  });

  // Handle bell alert updates
  socket.on('bell-alert-update', (data) => {
    // Broadcast to counter staff
    io.to('counter').emit('bell-alert-updated', data);
  });

  // Handle menu updates
  socket.on('menu-update', (data) => {
    // Broadcast to all users
    io.emit('menu-updated', data);
  });

  // Handle settings updates
  socket.on('settings-update', (data) => {
    // Broadcast to all users
    io.emit('settings-updated', data);
  });

  // Handle inventory updates
  socket.on('inventory-update', (data) => {
    // Broadcast to all users
    io.emit('inventory-updated', data);
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

// Initialize database
db.init().then(() => {
  console.log('Database initialized successfully');
  
  server.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
    console.log(`Local Network: http://localhost:${PORT}`);
    
    // Get local IP address
    const os = require('os');
    const networkInterfaces = os.networkInterfaces();
    Object.keys(networkInterfaces).forEach(interfaceName => {
      networkInterfaces[interfaceName].forEach(interface => {
        if (interface.family === 'IPv4' && !interface.internal) {
          console.log(`Network Access: http://${interface.address}:${PORT}`);
        }
      });
    });
  });
}).catch(err => {
  console.error('Failed to initialize database:', err);
});

module.exports = { app, io };
