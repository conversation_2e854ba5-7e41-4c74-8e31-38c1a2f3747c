const express = require('express');
const { getDb } = require('../database/db');

const router = express.Router();

// Payment requests endpoint
router.post('/payment-requests', (req, res) => {
  const { table_number, message } = req.body;
  
  if (!table_number) {
    return res.status(400).json({ error: 'Table number is required' });
  }

  const db = getDb();
  const timestamp = new Date().toISOString();
  
  db.run(
    `INSERT INTO payment_requests (table_number, message, status, created_at) 
     VALUES (?, ?, 'pending', ?)`,
    [table_number, message || `Table ${table_number} ငွေရှင်းပါမယ်ခင်ဗျာ`, timestamp],
    function(err) {
      if (err) {
        console.error('Error creating payment request:', err);
        return res.status(500).json({ error: 'Database error' });
      }
      
      console.log(`💰 Payment request from Table ${table_number}`);
      
      res.json({
        id: this.lastID,
        table_number,
        message: message || `Table ${table_number} ငွေရှင်းပါမယ်ခင်ဗျာ`,
        status: 'pending',
        created_at: timestamp
      });
    }
  );
});

// Bell alerts endpoint
router.post('/bell-alerts', (req, res) => {
  const { table_number, message } = req.body;
  
  if (!table_number) {
    return res.status(400).json({ error: 'Table number is required' });
  }

  const db = getDb();
  const timestamp = new Date().toISOString();
  
  db.run(
    `INSERT INTO bell_alerts (table_number, message, status, created_at) 
     VALUES (?, ?, 'pending', ?)`,
    [table_number, message || `Table ${table_number} မှ လှန်းခေါ်နေပါသည်`, timestamp],
    function(err) {
      if (err) {
        console.error('Error creating bell alert:', err);
        return res.status(500).json({ error: 'Database error' });
      }
      
      console.log(`🔔 Bell alert from Table ${table_number}`);
      
      res.json({
        id: this.lastID,
        table_number,
        message: message || `Table ${table_number} မှ လှန်းခေါ်နေပါသည်`,
        status: 'pending',
        created_at: timestamp
      });
    }
  );
});

// Get payment requests
router.get('/payment-requests', (req, res) => {
  const db = getDb();
  
  db.all(
    `SELECT * FROM payment_requests 
     WHERE status = 'pending' 
     ORDER BY created_at DESC`,
    [],
    (err, requests) => {
      if (err) {
        console.error('Error fetching payment requests:', err);
        return res.status(500).json({ error: 'Database error' });
      }
      res.json(requests);
    }
  );
});

// Get bell alerts
router.get('/bell-alerts', (req, res) => {
  const db = getDb();
  
  db.all(
    `SELECT * FROM bell_alerts 
     WHERE status = 'pending' 
     ORDER BY created_at DESC`,
    [],
    (err, alerts) => {
      if (err) {
        console.error('Error fetching bell alerts:', err);
        return res.status(500).json({ error: 'Database error' });
      }
      res.json(alerts);
    }
  );
});

// Mark payment request as completed
router.patch('/payment-requests/:id', (req, res) => {
  const { id } = req.params;
  const { status } = req.body;
  
  const db = getDb();
  
  db.run(
    `UPDATE payment_requests SET status = ? WHERE id = ?`,
    [status, id],
    function(err) {
      if (err) {
        console.error('Error updating payment request:', err);
        return res.status(500).json({ error: 'Database error' });
      }
      
      if (this.changes === 0) {
        return res.status(404).json({ error: 'Payment request not found' });
      }
      
      res.json({ message: 'Payment request updated successfully' });
    }
  );
});

// Mark bell alert as completed
router.patch('/bell-alerts/:id', (req, res) => {
  const { id } = req.params;
  const { status } = req.body;

  const db = getDb();

  db.run(
    `UPDATE bell_alerts SET status = ? WHERE id = ?`,
    [status, id],
    function(err) {
      if (err) {
        console.error('Error updating bell alert:', err);
        return res.status(500).json({ error: 'Database error' });
      }

      if (this.changes === 0) {
        return res.status(404).json({ error: 'Bell alert not found' });
      }

      res.json({ message: 'Bell alert updated successfully' });
    }
  );
});

// Customer notifications endpoints
router.post('/customer-notifications', (req, res) => {
  const { table_number, message, type } = req.body;

  if (!table_number || !message) {
    return res.status(400).json({ error: 'Table number and message are required' });
  }

  const db = getDb();
  const timestamp = new Date().toISOString();

  db.run(
    `INSERT INTO customer_notifications (table_number, message, type, created_at)
     VALUES (?, ?, ?, ?)`,
    [table_number, message, type || 'general', timestamp],
    function(err) {
      if (err) {
        console.error('Error creating customer notification:', err);
        return res.status(500).json({ error: 'Database error' });
      }

      console.log(`📢 Customer notification sent to Table ${table_number}: ${message}`);

      // Send real-time notification via Socket.IO
      const io = req.app.get('io');
      if (io) {
        io.emit('customer-notification', {
          tableNumber: table_number,
          message,
          type: type || 'general'
        });
      }

      res.json({
        id: this.lastID,
        table_number,
        message,
        type: type || 'general',
        read: false,
        created_at: timestamp
      });
    }
  );
});

router.get('/customer-notifications', (req, res) => {
  const { table_number } = req.query;
  const db = getDb();

  let query = 'SELECT * FROM customer_notifications';
  const params = [];

  if (table_number) {
    query += ' WHERE table_number = ?';
    params.push(table_number);
  }

  query += ' ORDER BY created_at DESC';

  db.all(query, params, (err, notifications) => {
    if (err) {
      console.error('Error fetching customer notifications:', err);
      return res.status(500).json({ error: 'Database error' });
    }
    res.json(notifications);
  });
});

router.patch('/customer-notifications/:id', (req, res) => {
  const { id } = req.params;
  const { read } = req.body;

  const db = getDb();

  db.run(
    `UPDATE customer_notifications SET read = ? WHERE id = ?`,
    [read, id],
    function(err) {
      if (err) {
        console.error('Error updating customer notification:', err);
        return res.status(500).json({ error: 'Database error' });
      }

      if (this.changes === 0) {
        return res.status(404).json({ error: 'Customer notification not found' });
      }

      res.json({ message: 'Customer notification updated successfully' });
    }
  );
});

module.exports = router;
