const express = require('express');
const { getDb } = require('../database/db');
const QRCode = require('qrcode');
const os = require('os');

const router = express.Router();

// Get local network IP
function getLocalIP() {
  const networkInterfaces = os.networkInterfaces();
  for (const interfaceName of Object.keys(networkInterfaces)) {
    for (const interface of networkInterfaces[interfaceName]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return 'localhost';
}

// Get all tables
router.get('/', (req, res) => {
  const db = getDb();
  
  db.all(
    'SELECT * FROM tables WHERE is_active = 1 ORDER BY table_number',
    [],
    (err, tables) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      res.json(tables);
    }
  );
});

// Get table by ID
router.get('/:id', (req, res) => {
  const db = getDb();
  const { id } = req.params;
  
  db.get(
    'SELECT * FROM tables WHERE id = ? AND is_active = 1',
    [id],
    (err, table) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      if (!table) {
        return res.status(404).json({ error: 'Table not found' });
      }
      res.json(table);
    }
  );
});

// Generate QR codes for all tables (must be before /qr/:qrCode route)
router.get('/qr/all', async (req, res) => {
  console.log('🔍 QR Code generation requested for all tables');
  const db = getDb();

  db.all('SELECT * FROM tables WHERE is_active = 1 ORDER BY table_number', [], async (err, tables) => {
    if (err) {
      console.error('❌ Database error:', err);
      return res.status(500).json({ error: 'Database error' });
    }

    console.log(`📊 Found ${tables.length} active tables`);

    if (tables.length === 0) {
      console.log('⚠️ No active tables found');
      return res.status(404).json({ error: 'No active tables found' });
    }

    try {
      const localIP = getLocalIP();
      const frontendPort = 3002;
      const qrCodes = [];

      console.log(`🌐 Network IP: ${localIP}, Frontend Port: ${frontendPort}`);

      for (const table of tables) {
        const menuUrl = `http://${localIP}:${frontendPort}/menu?table=${table.table_number}`;

        console.log(`🔗 Generating QR for ${table.table_name}: ${menuUrl}`);

        const qrCodeDataURL = await QRCode.toDataURL(menuUrl, {
          width: 300,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        });

        qrCodes.push({
          table: table,
          qr_code_url: qrCodeDataURL,
          menu_url: menuUrl
        });
      }

      console.log(`✅ Generated ${qrCodes.length} QR codes successfully`);

      res.json({
        network_ip: localIP,
        frontend_port: frontendPort,
        qr_codes: qrCodes
      });
    } catch (error) {
      console.error('❌ Error generating QR codes:', error);
      res.status(500).json({ error: 'Failed to generate QR codes' });
    }
  });
});

// Get table by QR code
router.get('/qr/:qrCode', (req, res) => {
  const db = getDb();
  const { qrCode } = req.params;

  db.get(
    'SELECT * FROM tables WHERE qr_code = ? AND is_active = 1',
    [qrCode],
    (err, table) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      if (!table) {
        return res.status(404).json({ error: 'Table not found' });
      }
      res.json(table);
    }
  );
});

// Create new table
router.post('/', (req, res) => {
  const db = getDb();
  const { table_number, table_name, capacity } = req.body;
  
  if (!table_number) {
    return res.status(400).json({ error: 'Table number is required' });
  }
  
  const qr_code = `${table_number}-${Date.now()}`;
  
  db.run(
    `INSERT INTO tables (table_number, table_name, capacity, qr_code) 
     VALUES (?, ?, ?, ?)`,
    [table_number, table_name || `စားပွဲ ${table_number}`, capacity || 4, qr_code],
    function(err) {
      if (err) {
        if (err.code === 'SQLITE_CONSTRAINT') {
          return res.status(400).json({ error: 'Table number already exists' });
        }
        return res.status(500).json({ error: 'Database error' });
      }
      
      // Return the created table
      db.get(
        'SELECT * FROM tables WHERE id = ?',
        [this.lastID],
        (err, table) => {
          if (err) {
            return res.status(500).json({ error: 'Database error' });
          }
          res.status(201).json(table);
        }
      );
    }
  );
});

// Update table
router.put('/:id', (req, res) => {
  const db = getDb();
  const { id } = req.params;
  const { table_number, table_name, capacity, is_active } = req.body;
  
  db.run(
    `UPDATE tables 
     SET table_number = ?, table_name = ?, capacity = ?, is_active = ?
     WHERE id = ?`,
    [table_number, table_name, capacity, is_active !== undefined ? is_active : 1, id],
    function(err) {
      if (err) {
        if (err.code === 'SQLITE_CONSTRAINT') {
          return res.status(400).json({ error: 'Table number already exists' });
        }
        return res.status(500).json({ error: 'Database error' });
      }
      
      if (this.changes === 0) {
        return res.status(404).json({ error: 'Table not found' });
      }
      
      // Return the updated table
      db.get(
        'SELECT * FROM tables WHERE id = ?',
        [id],
        (err, table) => {
          if (err) {
            return res.status(500).json({ error: 'Database error' });
          }
          res.json(table);
        }
      );
    }
  );
});

// Delete table (soft delete)
router.delete('/:id', (req, res) => {
  const db = getDb();
  const { id } = req.params;
  
  db.run(
    'UPDATE tables SET is_active = 0 WHERE id = ?',
    [id],
    function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      
      if (this.changes === 0) {
        return res.status(404).json({ error: 'Table not found' });
      }
      
      res.json({ message: 'Table deleted successfully' });
    }
  );
});



// Generate QR Code for table
router.get('/:id/qr', async (req, res) => {
  const { id } = req.params;
  const db = getDb();

  db.get('SELECT * FROM tables WHERE id = ?', [id], async (err, table) => {
    if (err) {
      return res.status(500).json({ error: 'Database error' });
    }

    if (!table) {
      return res.status(404).json({ error: 'Table not found' });
    }

    try {
      const localIP = getLocalIP();
      const port = process.env.PORT || 5000;
      const frontendPort = 3002; // Next.js port

      // Create URL for menu page with table parameter
      const menuUrl = `http://${localIP}:${frontendPort}/menu?table=${table.table_number}`;

      // Generate QR code as data URL
      const qrCodeDataURL = await QRCode.toDataURL(menuUrl, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });

      res.json({
        table: table,
        qr_code_url: qrCodeDataURL,
        menu_url: menuUrl,
        network_ip: localIP
      });
    } catch (error) {
      console.error('Error generating QR code:', error);
      res.status(500).json({ error: 'Failed to generate QR code' });
    }
  });
});

// Generate new QR code for table
router.post('/:id/regenerate-qr', (req, res) => {
  const db = getDb();
  const { id } = req.params;

  // Get table info first
  db.get(
    'SELECT table_number FROM tables WHERE id = ? AND is_active = 1',
    [id],
    (err, table) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      if (!table) {
        return res.status(404).json({ error: 'Table not found' });
      }

      const new_qr_code = `${table.table_number}-${Date.now()}`;

      db.run(
        'UPDATE tables SET qr_code = ? WHERE id = ?',
        [new_qr_code, id],
        function(err) {
          if (err) {
            return res.status(500).json({ error: 'Database error' });
          }

          // Return the updated table
          db.get(
            'SELECT * FROM tables WHERE id = ?',
            [id],
            (err, updatedTable) => {
              if (err) {
                return res.status(500).json({ error: 'Database error' });
              }
              res.json(updatedTable);
            }
          );
        }
      );
    }
  );
});

module.exports = router;
