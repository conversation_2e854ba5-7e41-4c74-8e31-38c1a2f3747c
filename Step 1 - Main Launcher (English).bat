@echo off
title Step 1 - Restaurant System Main Launcher
color 0F

echo.
echo ================================================================
echo                Step 1 - Restaurant System
echo                      Main Launcher
echo.
echo                   Welcome to the
echo                Restaurant Management System
echo ================================================================
echo.

:MENU
echo Step 1 - Main System Options:
echo.
echo [1] Step 2 - Server Management (Start/Stop Servers)
echo      Manage backend and frontend servers
echo      Recommended: Start here for daily use
echo.
echo [2] Step 3 - Database Management (View/Reset Database)
echo      View, reset, and backup database
echo.
echo [3] Step 4 - Data Management (Insert Sample Data)
echo      Insert complete sample data (40 menu items, 10 categories)
echo      Alternative to Step 0 option [3] for data insertion
echo.
echo [4] Step 5 - API Testing (Test All APIs)
echo      Test and verify all APIs
echo.
echo [5] Step 6 - Open Web Interface
echo      Open web interface in browser
echo      Main application interface
echo.
echo [6] Step 7 - System Status Check
echo      Check complete system status
echo.
echo [7] Step 8 - Help and Documentation
echo     Usage instructions and documentation
echo.
echo [8] Exit
echo.
set /p choice="Select option (1-8): "

if "%choice%"=="1" goto SERVER_MANAGEMENT
if "%choice%"=="2" goto DATABASE_MANAGEMENT
if "%choice%"=="3" goto DATA_MANAGEMENT
if "%choice%"=="4" goto API_TESTING
if "%choice%"=="5" goto OPEN_WEB
if "%choice%"=="6" goto SYSTEM_STATUS
if "%choice%"=="7" goto HELP
if "%choice%"=="8" goto EXIT
goto INVALID

:SERVER_MANAGEMENT
echo.
echo Step 2 - Launching Server Manager...
echo Opening Server Management tool...
call "Step 2 - Server Management (English).bat"
goto MENU

:DATABASE_MANAGEMENT
echo.
echo Step 3 - Launching Database Manager...
echo Opening Database Management tool...
call "Step 3 - Database Management (English).bat"
goto MENU

:DATA_MANAGEMENT
echo.
echo Step 4 - Launching Data Inserter...
echo Opening Data Management tool...
call "Step 4 - Data Management (English).bat"
goto MENU

:API_TESTING
echo.
echo Step 5 - Launching API Tester...
echo Opening API Testing tool...
call "Step 5 - API Testing (English).bat"
goto MENU

:OPEN_WEB
echo.
echo Step 6 - Opening Web Interface...
echo =======================================
echo Opening web interface in browser...
echo.
echo Checking if servers are running...

powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:5000/api/health' -TimeoutSec 3 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel%==0 (
    echo Backend server is running
    powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:3002' -TimeoutSec 3 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
    if %errorlevel%==0 (
        echo Frontend server is running
        echo Opening web interface...
        start http://localhost:3002
        echo Web interface opened in browser
    ) else (
        echo Frontend server is not running
        echo Would you like to start the frontend server? (Y/N)
        set /p startfrontend="Start frontend? "
        if /i "%startfrontend%"=="Y" (
            start "Frontend Server" cmd /k "npm run dev"
            timeout /t 5 /nobreak >nul
            start http://localhost:3002
        )
    )
) else (
    echo Backend server is not running
    echo Would you like to start both servers? (Y/N)
    set /p startboth="Start servers? "
    if /i "%startboth%"=="Y" (
        start "Backend Server" cmd /k "npm run server"
        timeout /t 3 /nobreak >nul
        start "Frontend Server" cmd /k "npm run dev"
        timeout /t 5 /nobreak >nul
        start http://localhost:3002
    )
)
pause
goto MENU

:SYSTEM_STATUS
echo.
echo Step 7 - System Status Check...
echo =======================================
echo Checking complete system status...
echo.
echo Backend Server (Port 5000):
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:5000/api/health' -TimeoutSec 3 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel%==0 (
    echo ✓ Backend server is running and healthy
) else (
    echo ✗ Backend server is not responding
)

echo.
echo Frontend Server (Port 3002):
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:3002' -TimeoutSec 3 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel%==0 (
    echo ✓ Frontend server is running
) else (
    echo ✗ Frontend server is not responding
)

echo.
echo Database Status:
if exist "server\database\restaurant.db" (
    echo Database file exists
    cd /d "%~dp0\server"
    node -e "const{getDb}=require('./database/db');const db=getDb();db.get('SELECT COUNT(*) as count FROM menu_items',[],(err,result)=>{if(err)console.log('Database connection failed');else console.log('Database accessible - '+result.count+' menu items');process.exit(0);});" 2>nul
) else (
    echo Database file not found
)

echo.
echo Node.js Processes:
tasklist /fi "imagename eq node.exe" 2>nul | find "node.exe" >nul
if %errorlevel%==0 (
    echo Node.js processes are running:
    tasklist /fi "imagename eq node.exe" | findstr "node.exe"
) else (
    echo No Node.js processes running
)

echo.
echo Required Files:
if exist "package.json" (echo package.json - OK) else (echo package.json - MISSING)
if exist "server\index.js" (echo server\index.js - OK) else (echo server\index.js - MISSING)
if exist "src\app\page.tsx" (echo src\app\page.tsx - OK) else (echo src\app\page.tsx - MISSING)

echo.
echo System status check completed!
pause
goto MENU

:HELP
echo.
echo Step 8 - Help and Documentation
echo =======================================
echo Usage instructions and documentation
echo.
echo Quick Start Guide:
echo =======================================
echo 1. First time setup:
echo    🔧 Run Step 0 → [3] Database Reset and Setup (Complete setup)
echo    ⚡ OR Run [1] Server Management → [3] Start Both Servers
echo    📊 OR Run [3] Data Management → [7] Insert Complete Sample Data
echo    🌐 Run [5] Open Web Interface
echo.
echo 2. Daily usage:
echo    🔧 Use [1] Server Management for server control
echo    🗄️ Use [2] Database Management to check data
echo    🧪 Use [4] API Testing to verify APIs
echo    📋 Use [6] System Status for health checks
echo.
echo 3. Navigation:
echo    ✅ All sub-menus return to this main menu
echo    🔄 Use [0] or Exit options to return here
echo    📖 Use [7] Help for detailed instructions
echo.
echo Technical Information:
echo =======================================
echo - Backend Server: http://localhost:5000
echo - Frontend Server: http://localhost:3002
echo - Database: SQLite (server/database/restaurant.db)
echo - Admin Login: admin / admin123
echo.
echo Available Batch Files:
echo =======================================
echo - Step 1 - Main Launcher (English).bat (Main launcher)
echo - Step 2 - Server Management (English).bat (Server management)
echo - Step 3 - Database Management (English).bat (Database operations)
echo - Step 4 - Data Management (English).bat (Data insertion)
echo - Step 5 - API Testing (English).bat (API testing)
echo.
echo Troubleshooting:
echo =======================================
echo - If servers won't start: Check if ports 3002/5000 are free
echo - If database errors: Check server/database/ folder exists
echo - If API errors: Ensure backend server is running first
echo - If web interface won't load: Check both servers are running
echo.
echo Support:
echo =======================================
echo - Check console logs in server windows
echo - Use Step 7 for system status
echo - Use Step 5 to test individual APIs
echo.
pause
goto MENU

:INVALID
echo.
echo Invalid option! Please select 1-8.
pause
goto MENU

:EXIT
echo.
echo ================================================================
echo                    Thank You!                                 
echo                                                               
echo              Thanks for using Restaurant System              
echo                                                               
echo                    Have a great day!                         
echo ================================================================
echo.
pause
exit
