'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import Navigation from '../../components/Navigation';
import Image from 'next/image';
import { io, Socket } from 'socket.io-client';

interface MenuItem {
  id: number;
  name: string;
  name_mm: string;
  description?: string;
  description_mm?: string;
  price: number;
  image_url?: string;
  is_available: boolean;
  is_today_special?: boolean;
  category_id: number;
  category_name: string;
  category_name_mm: string;
}

interface Category {
  id: number;
  name: string;
  name_mm: string;
  description?: string;
  image_url?: string;
  icon?: string;
  items: MenuItem[];
}

interface CartItem {
  menu_item_id: number;
  name: string;
  name_mm: string;
  price: number;
  quantity: number;
  special_instructions?: string;
}

export default function MenuPage() {
  const searchParams = useSearchParams();
  const [categories, setCategories] = useState<Category[]>([]);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [showCart, setShowCart] = useState(false);
  const [settings, setSettings] = useState<any>({});
  const [menuItems, setMenuItems] = useState<any[]>([]);
  const categoriesScrollRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  const [tableNumber, setTableNumber] = useState<string | null>(null);
  const [showPaymentConfirm, setShowPaymentConfirm] = useState(false);
  const [customerOrders, setCustomerOrders] = useState<any[]>([]);
  const [showOrderHistory, setShowOrderHistory] = useState(false);
  const [tableOrderData, setTableOrderData] = useState<any>(null);
  const [customerNotifications, setCustomerNotifications] = useState<any[]>([]);
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isCartSyncing, setIsCartSyncing] = useState(false);

  useEffect(() => {
    fetchMenu();
    fetchSettings();

    // Check for category parameter from URL
    const categoryParam = searchParams.get('category');
    if (categoryParam) {
      setSelectedCategory(parseInt(categoryParam));
    }

    // Check for table parameter from URL
    const tableParam = searchParams.get('table');
    if (tableParam) {
      setTableNumber(tableParam);
    }
  }, [searchParams]);

  useEffect(() => {
    if (tableNumber) {
      fetchTableOrderData();
      fetchCustomerNotifications();

      // Initialize Socket.IO connection
      const newSocket = io('http://localhost:5000');
      setSocket(newSocket);

      // Join table room for cart synchronization
      newSocket.emit('join-table', tableNumber);

      // Listen for cart updates from other users
      newSocket.on('cart-updated', (data) => {
        console.log('Cart updated by another user:', data);
        if (!isCartSyncing) {
          setCart(data.cart);
        }
      });

      // Listen for cart sync requests
      newSocket.on('cart-sync-request', (data) => {
        console.log('Cart sync requested');
        // Send current cart state to new user
        newSocket.emit('cart-sync-response', {
          tableNumber: data.tableNumber,
          cart: cart
        });
      });

      // Listen for cart sync responses
      newSocket.on('cart-synced', (data) => {
        console.log('Cart synced:', data);
        setCart(data.cart);
      });

      // Listen for cart clear events
      newSocket.on('cart-cleared', (data) => {
        console.log('Cart cleared:', data.message);
        setCart([]);
        alert(data.message);
      });

      // Listen for customer notifications
      newSocket.on('notification-received', (data) => {
        console.log('Notification received:', data);
        if (data.type === 'waiter_coming' || data.type === 'payment_coming') {
          alert(data.message);
        }
      });

      // Listen for order updates
      newSocket.on('order-created', (orderData) => {
        console.log('Order created:', orderData);
        if (orderData.table_number === tableNumber) {
          fetchCustomerOrders();
          fetchTableOrderData();
        }
      });

      newSocket.on('order-status-updated', (data) => {
        console.log('Order status updated:', data);
        if (data.order.table_number === tableNumber) {
          fetchCustomerOrders();
          fetchTableOrderData();
        }
      });

      newSocket.on('order-item-updated', (data) => {
        console.log('Order item updated:', data);
        if (data.order.table_number === tableNumber) {
          fetchCustomerOrders();
          fetchTableOrderData();
        }
      });

      // Listen for order item updates
      newSocket.on('order-item-updated', (data) => {
        console.log('Order item updated:', data);
        // Refresh customer orders when items are updated
        fetchCustomerOrders();
      });

      // Poll for notifications every 10 seconds as backup
      const interval = setInterval(() => {
        fetchCustomerNotifications();
      }, 10000);

      return () => {
        clearInterval(interval);
        newSocket.disconnect();
      };
    }
  }, [tableNumber]);

  // Add mouse wheel scroll functionality for categories
  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      if (categoriesScrollRef.current) {
        e.preventDefault();
        categoriesScrollRef.current.scrollLeft += e.deltaY;
      }
    };

    const categoriesElement = categoriesScrollRef.current;
    if (categoriesElement) {
      categoriesElement.addEventListener('wheel', handleWheel, { passive: false });

      return () => {
        categoriesElement.removeEventListener('wheel', handleWheel);
      };
    }
  }, []);

  // Mouse drag handlers for categories
  const handleMouseDown = (e: React.MouseEvent) => {
    // Don't interfere with button clicks
    if ((e.target as HTMLElement).closest('button, a, [role="button"]')) {
      return;
    }

    if (categoriesScrollRef.current) {
      setIsDragging(true);
      setStartX(e.pageX - categoriesScrollRef.current.offsetLeft);
      setScrollLeft(categoriesScrollRef.current.scrollLeft);
      categoriesScrollRef.current.style.cursor = 'grabbing';
    }
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
    if (categoriesScrollRef.current) {
      categoriesScrollRef.current.style.cursor = 'grab';
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    if (categoriesScrollRef.current) {
      categoriesScrollRef.current.style.cursor = 'grab';
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !categoriesScrollRef.current) return;
    e.preventDefault();
    const x = e.pageX - categoriesScrollRef.current.offsetLeft;
    const walk = (x - startX) * 2; // Scroll speed multiplier
    categoriesScrollRef.current.scrollLeft = scrollLeft - walk;
  };

  const fetchMenu = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/menu/full');
      if (response.ok) {
        const data = await response.json();
        setCategories(data);

        // Extract all menu items from categories
        const allItems = data.flatMap((category: any) => category.items || []);
        setMenuItems(allItems);

        if (data.length > 0) {
          setSelectedCategory(data[0].id);
        }
      } else {
        console.error('Failed to fetch menu:', response.status);
        // Add some sample data for testing
        const sampleData = [
          {
            id: 1,
            name: 'Rice Dishes',
            name_mm: 'ထမင်းများ',
            items: [
              {
                id: 1,
                name: 'Fried Rice',
                name_mm: 'ကြော်ထမင်း',
                price: 3000,
                is_available: true,
                category_id: 1,
                category_name: 'Rice Dishes',
                category_name_mm: 'ထမင်းများ'
              }
            ]
          }
        ];
        setCategories(sampleData);
        setSelectedCategory(1);
      }
    } catch (error) {
      console.error('Error fetching menu:', error);
      // Add sample data when API is not available
      const sampleData = [
        {
          id: 1,
          name: 'Rice Dishes',
          name_mm: 'ထမင်းများ',
          items: [
            {
              id: 1,
              name: 'Fried Rice',
              name_mm: 'ကြော်ထမင်း',
              price: 3000,
              is_available: true,
              category_id: 1,
              category_name: 'Rice Dishes',
              category_name_mm: 'ထမင်းများ'
            }
          ]
        }
      ];
      setCategories(sampleData);
      setSelectedCategory(1);
    } finally {
      setLoading(false);
    }
  };

  const fetchSettings = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
    }
  };

  const addToCart = (item: MenuItem) => {
    const existingItem = cart.find(cartItem => cartItem.menu_item_id === item.id);
    let newCart;

    if (existingItem) {
      newCart = cart.map(cartItem =>
        cartItem.menu_item_id === item.id
          ? { ...cartItem, quantity: cartItem.quantity + 1 }
          : cartItem
      );
    } else {
      newCart = [...cart, {
        menu_item_id: item.id,
        name: item.name,
        name_mm: item.name_mm,
        price: item.price,
        quantity: 1
      }];
    }

    setCart(newCart);

    // Broadcast cart update to other users at the same table
    if (socket && tableNumber) {
      socket.emit('cart-update', {
        tableNumber,
        cart: newCart,
        action: 'add',
        item: {
          menu_item_id: item.id,
          name_mm: item.name_mm,
          quantity: existingItem ? existingItem.quantity + 1 : 1
        }
      });
    }
  };

  const updateCartQuantity = (menu_item_id: number, quantity: number) => {
    let newCart;
    const item = cart.find(item => item.menu_item_id === menu_item_id);

    if (quantity <= 0) {
      newCart = cart.filter(item => item.menu_item_id !== menu_item_id);
    } else {
      newCart = cart.map(item =>
        item.menu_item_id === menu_item_id
          ? { ...item, quantity }
          : item
      );
    }

    setCart(newCart);

    // Broadcast cart update to other users at the same table
    if (socket && tableNumber) {
      socket.emit('cart-update', {
        tableNumber,
        cart: newCart,
        action: quantity <= 0 ? 'remove' : 'update',
        item: {
          menu_item_id,
          name_mm: item?.name_mm || '',
          quantity
        }
      });
    }
  };

  const getTotalAmount = () => {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const getTotalItems = () => {
    return cart.reduce((total, item) => total + item.quantity, 0);
  };

  const handlePlaceOrder = async () => {
    if (cart.length === 0) {
      alert('အမှာများ မရှိပါ');
      return;
    }

    try {
      const orderData = {
        customer_name: `Table ${tableNumber || 'Walk-in'}`,
        table_number: tableNumber || 'Walk-in',
        items: cart.map(item => ({
          menu_item_id: item.menu_item_id,
          quantity: item.quantity
        })),
        payment_method: 'cash',
        notes: 'Direct order from menu'
      };

      const response = await fetch('http://localhost:5000/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      });

      if (response.ok) {
        // Clear cart locally
        setCart([]);
        setShowCart(false);

        // Broadcast order placement to other users at the same table
        if (socket && tableNumber) {
          socket.emit('new-order', orderData);
        }

        alert('အမှာပေးပြီးပါပြီ! ကျေးဇူးတင်ပါတယ်');
      } else {
        throw new Error('Order submission failed');
      }
    } catch (error) {
      console.error('Error submitting order:', error);
      alert('အမှာပေးရာတွင် အမှားရှိပါတယ်။ ကျေးဇူးပြု၍ ပြန်လည်ကြိုးစားပါ။');
    }
  };

  const fetchCustomerOrders = async () => {
    if (!tableNumber) return;

    try {
      const response = await fetch(`http://localhost:5000/api/orders?table_number=${tableNumber}`);
      if (response.ok) {
        const data = await response.json();
        setCustomerOrders(data.filter((order: any) => order.status !== 'delivered' && order.status !== 'cancelled'));
      }
    } catch (error) {
      console.error('Error fetching customer orders:', error);
    }
  };

  const fetchTableOrderData = async () => {
    if (!tableNumber) return;

    try {
      const response = await fetch(`http://localhost:5000/api/orders?table_number=${tableNumber}&group_by_table=true`);
      if (response.ok) {
        const data = await response.json();
        if (data.length > 0) {
          setTableOrderData(data[0]);
        }
      }
    } catch (error) {
      console.error('Error fetching table order data:', error);
    }
  };

  const cancelOrderItem = async (orderId: number, itemId: number) => {
    try {
      const response = await fetch(`http://localhost:5000/api/orders/${orderId}/items/${itemId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        // Refresh orders after cancellation
        await fetchCustomerOrders();
        await fetchTableOrderData();
        alert('မီနူးပယ်ဖျက်ပြီးပါပြီ');
      } else {
        alert('မီနူးပယ်ဖျက်ရာတွင် အမှားရှိပါတယ်');
      }
    } catch (error) {
      console.error('Error cancelling order item:', error);
      alert('မီနူးပယ်ဖျက်ရာတွင် အမှားရှိပါတယ်');
    }
  };

  const cancelWholeOrder = async (orderId: number) => {
    try {
      const response = await fetch(`http://localhost:5000/api/orders/${orderId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'cancelled' }),
      });

      if (response.ok) {
        // Refresh orders after cancellation
        await fetchCustomerOrders();
        await fetchTableOrderData();
        alert('အမှာစာပယ်ဖျက်ပြီးပါပြီ');
      } else {
        alert('အမှာစာပယ်ဖျက်ရာတွင် အမှားရှိပါတယ်');
      }
    } catch (error) {
      console.error('Error cancelling order:', error);
      alert('အမှာစာပယ်ဖျက်ရာတွင် အမှားရှိပါတယ်');
    }
  };

  const handlePaymentRequest = async () => {
    if (!tableNumber) {
      alert('စားပွဲနံပါတ် လိုအပ်ပါသည်');
      return;
    }

    // Fetch current orders first
    await fetchCustomerOrders();
    setShowPaymentConfirm(true);
  };

  const confirmPaymentRequest = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/payment-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          table_number: tableNumber,
          message: `Table ${tableNumber} ငွေရှင်းပါမယ်ခင်ဗျာ`
        }),
      });

      if (response.ok) {
        setShowPaymentConfirm(false);
        alert('ငွေရှင်းရန် တောင်းဆိုမှု ပေးပို့ပြီးပါပြီ');
      } else {
        throw new Error('Payment request failed');
      }
    } catch (error) {
      console.error('Error sending payment request:', error);
      alert('ငွေရှင်းရန် တောင်းဆိုမှု ပေးပို့ရာတွင် အမှားရှိပါတယ်');
    }
  };

  const fetchCustomerNotifications = async () => {
    if (!tableNumber) return;

    try {
      const response = await fetch(`http://localhost:5000/api/customer-notifications?table_number=${tableNumber}`);
      if (response.ok) {
        const data = await response.json();
        const newNotifications = data.filter((notification: any) => !notification.read);

        // Show alerts for new notifications
        newNotifications.forEach((notification: any) => {
          if (notification.type === 'waiter_coming' || notification.type === 'payment_coming') {
            alert(notification.message);
            // Mark as read
            markNotificationAsRead(notification.id);
          }
        });

        setCustomerNotifications(data);
      }
    } catch (error) {
      console.error('Error fetching customer notifications:', error);
    }
  };

  const markNotificationAsRead = async (notificationId: number) => {
    try {
      await fetch(`http://localhost:5000/api/customer-notifications/${notificationId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ read: true }),
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const handleBellAlert = async () => {
    if (!tableNumber) {
      alert('စားပွဲနံပါတ် လိုအပ်ပါသည်');
      return;
    }

    try {
      const response = await fetch('http://localhost:5000/api/bell-alerts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          table_number: tableNumber,
          message: `Table ${tableNumber} မှ လှန်းခေါ်နေပါသည်`
        }),
      });

      if (response.ok) {
        alert('လှန်းခေါ်မှု ပေးပို့ပြီးပါပြီ');
      } else {
        throw new Error('Bell alert failed');
      }
    } catch (error) {
      console.error('Error sending bell alert:', error);
      alert('လှန်းခေါ်မှု ပေးပို့ရာတွင် အမှားရှိပါတယ်');
    }
  };

  const getCategoryIcon = (categoryName: string, dbIcon?: string) => {
    // Database မှာ သိမ်းထားတဲ့ icon ကို ဦးစားပေးသုံးမယ်
    if (dbIcon && dbIcon.trim() !== '') {
      return dbIcon;
    }

    const iconMap: { [key: string]: string } = {
      // Rice & Noodles
      'ထမင်းများ': '🍚',
      'ခေါက်ဆွဲများ': '🍜',
      'မုန့်ဟင်းခါး': '🍲',
      'ကောက်ညှင်းများ': '🌾',

      // Curry & Main Dishes
      'ဟင်းများ': '🍛',
      'ကြီးကြီးဟင်းများ': '🥘',
      'အသားဟင်းများ': '🍖',
      'ငါးဟင်းများ': '🐟',
      'ကြက်သားဟင်းများ': '🍗',
      'ဝက်သားဟင်းများ': '🥓',

      // Salads & Vegetables
      'သုပ်များ': '🥗',
      'လပက်သုပ်': '🥬',
      'ဟင်းသီးဟင်းရွက်များ': '🥕',
      'သစ်သီးများ': '🍎',

      // Snacks & Appetizers
      'မုန့်များ': '🥖',
      'ကော်ပြန့်များ': '🌯',
      'ဆမူဆာများ': '🥟',
      'ကြော်ချက်များ': '🍤',
      'အမွှေးအကြိုင်များ': '🧄',

      // Desserts & Sweets
      'အချိုများ': '🍰',
      'ရွှေရင်အေး': '🍧',
      'မုန့်လုံးများ': '🍡',
      'ကိတ်မုန့်များ': '🎂',
      'ရေခဲမုန့်များ': '🍦',
      'ဖာလူဒါ': '🥤',

      // Beverages
      'ယမကာများ': '🥤',
      'လက်ဖက်ရည်': '🍵',
      'ကော်ဖီ': '☕',
      'သစ်သီးရည်များ': '🧃',
      'နို့ထွက်ပစ္စည်းများ': '🥛',
      'ရေခဲများ': '🧊',
      'အရက်များ': '🍺',
      'ဝိုင်များ': '🍷',

      // Seafood
      'ပင်လယ်စာများ': '🦐',
      'ငါးများ': '🐟',
      'ကဏန်းများ': '🦀',
      'ပုစွန်များ': '🦑',

      // Meat & Protein
      'အသားများ': '🍖',
      'ကြက်သားများ': '🍗',
      'ဝက်သားများ': '🥓',
      'နွားသားများ': '🥩',
      'ဆိတ်သားများ': '🐐',

      // Bread & Bakery
      'ပေါင်မုန့်များ': '🍞',
      'နံပြားများ': '🥞',
      'ဘစ်ကစ်များ': '🍪',
      'ကြက်ကွက်များ': '🧇',

      // Utensils & Accessories
      'ပန်းကန်များ': '🍽️',
      'ဇွန်းများ': '🥄',
      'ခက်ရင်းများ': '🍴',
      'ဖန်ခွက်များ': '🥃',
      'အရက်ခွက်များ': '🍻',
      'တစ်ရှူးများ': '🧻',
      'ထုပ်ပိုးများ': '🥡',
      'သန့်ရှင်းရေးပစ္စည်းများ': '🧽',
      'တူများ': '🥢',
      'ရေခွက်များ': '🍶',
      'အိတ်များ': '🛍️'
    };

    // Check for exact matches first
    if (iconMap[categoryName]) {
      return iconMap[categoryName];
    }

    // Check for partial matches
    for (const [key, icon] of Object.entries(iconMap)) {
      if (categoryName.includes(key.replace('များ', '')) || categoryName.includes(key)) {
        return icon;
      }
    }

    return '🍽️'; // Default icon
  };

  const getDisplayItems = () => {
    if (selectedCategory === null) {
      // Show all items from all categories
      return menuItems.filter(item => item.is_available);
    } else {
      // Show items from selected category
      const selectedCat = categories.find(cat => cat.id === selectedCategory);
      return selectedCat?.items?.filter(item => item.is_available) || [];
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center bg-gray-800 rounded-2xl p-8 border border-gray-700">
          <div className="text-4xl mb-4">🍜</div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="font-medium text-gray-300">မီနူးများ ရယူနေသည်...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 relative">
      {/* Background Food Pattern */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-10 left-10 text-6xl">🍜</div>
        <div className="absolute top-20 right-20 text-4xl">🍛</div>
        <div className="absolute top-40 left-1/4 text-5xl">🥘</div>
        <div className="absolute top-60 right-1/3 text-3xl">🍲</div>
        <div className="absolute bottom-40 left-20 text-4xl">🥗</div>
        <div className="absolute bottom-20 right-10 text-5xl">🍱</div>
        <div className="absolute bottom-60 left-1/2 text-3xl">🍳</div>
        <div className="absolute top-1/2 left-10 text-4xl">🥟</div>
        <div className="absolute top-1/3 right-10 text-3xl">🍤</div>
        <div className="absolute top-80 left-1/3 text-4xl">🍝</div>
        <div className="absolute bottom-80 right-1/4 text-3xl">🥙</div>
        <div className="absolute top-1/4 left-1/2 text-5xl">🍕</div>
      </div>

      {/* Navigation - Hide nav links on menu page */}
      <Navigation settings={settings} showNavLinks={false} />

      {/* Action Buttons - Fixed Position */}
      <div className="fixed top-32 right-4 z-50 space-y-3">
        {/* Cart Button */}
        <button
          onClick={() => setShowCart(true)}
          className="relative bg-blue-600 text-white px-4 py-2 rounded-full hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl block w-full"
        >
          <span className="text-lg mr-2">🛒</span>
          <span className="font-medium">အမှာ ({getTotalItems()})</span>
          {getTotalItems() > 0 && (
            <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse">
              {getTotalItems()}
            </span>
          )}
          {tableNumber && (
            <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse border-2 border-white"></div>
          )}
        </button>



        {/* Payment Request Button */}
        {tableNumber && (
          <button
            onClick={handlePaymentRequest}
            className="bg-green-600 text-white px-4 py-2 rounded-full hover:bg-green-700 transition-all duration-300 shadow-lg hover:shadow-xl block w-full"
          >
            <span className="text-lg mr-2">💰</span>
            <span className="font-medium text-sm">ငွေရှင်းမယ်</span>
          </button>
        )}

        {/* Bell Alert Button */}
        {tableNumber && (
          <button
            onClick={handleBellAlert}
            className="bg-orange-600 text-white px-4 py-2 rounded-full hover:bg-orange-700 transition-all duration-300 shadow-lg hover:shadow-xl block w-full"
          >
            <span className="text-lg mr-2">🔔</span>
            <span className="font-medium text-sm">လှန်းခေါ်မယ်</span>
          </button>
        )}
      </div>

      <div className="max-w-5xl mx-auto px-4 py-6">
        {/* Home Button - Top Left */}
        <div className="mb-4">
          <form action="/" method="get" className="inline-block">
            <button
              type="submit"
              onClick={() => {
                console.log('Home button clicked - form submit');
              }}
              className="inline-flex items-center bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded-full transition-all duration-300 shadow-lg text-sm font-medium active:scale-95 cursor-pointer border-2 border-green-500 hover:border-green-400 min-w-[120px] justify-center select-none z-10 relative"
              style={{ userSelect: 'none', WebkitUserSelect: 'none' }}
            >
              <span className="text-lg mr-2">🏠</span>
              <span>ပင်မစာမျက်နှာ</span>
            </button>
          </form>
        </div>

        {/* Restaurant Header */}
        <div className="text-center mb-6">
          <div className="flex items-center justify-center mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center text-white text-2xl font-bold shadow-lg">
              🍽️
            </div>
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">မြန်မာ့အရသာ စားသောက်ဆိုင်</h1>
          <p className="text-sm text-gray-300 mb-1">Myanmar Taste Restaurant</p>
          <p className="text-sm text-gray-400 mb-3">ကြိုက်တဲ့ အစားအသောက်များကို ရွေးချယ်ပါ</p>
          {tableNumber && (
            <div className="bg-blue-600/20 border border-blue-500/30 rounded-lg p-3 mb-3">
              <p className="text-blue-300 font-medium">🪑 စားပွဲ: {tableNumber}</p>
              <p className="text-blue-200 text-xs">Table Number: {tableNumber}</p>
            </div>
          )}

          {/* Menu Statistics */}
          <div className="flex justify-center space-x-4 text-xs">
            <div className="bg-gray-800/60 px-3 py-1 rounded-full border border-gray-600">
              <span className="text-blue-400 font-medium">
                📊 စုစုပေါင်း: {menuItems.filter(item => item.is_available).length} ခု
              </span>
            </div>
            <div className="bg-gray-800/60 px-3 py-1 rounded-full border border-gray-600">
              <span className="text-yellow-400 font-medium">
                ⭐ ယနေ့အထူး: {menuItems.filter(item => item.is_today_special && item.is_available).length} ခု
              </span>
            </div>
            <div className="bg-gray-800/60 px-3 py-1 rounded-full border border-gray-600">
              <span className="text-green-400 font-medium">
                📂 အမျိုးအစား: {categories.length} ခု
              </span>
            </div>
          </div>
        </div>

        {/* Categories */}
        <div className="mb-8">
          <h3 className="text-lg font-bold text-white mb-4 text-center">📂 အမျိုးအစားများ</h3>
          <div className="bg-gray-800/60 backdrop-blur-sm rounded-2xl p-4 border border-gray-700">
            <div
              ref={categoriesScrollRef}
              className="flex overflow-x-auto pb-2 space-x-3 scrollbar-hide cursor-grab active:cursor-grabbing select-none"
              style={{ scrollBehavior: 'smooth' }}
              onMouseDown={handleMouseDown}
              onMouseLeave={handleMouseLeave}
              onMouseUp={handleMouseUp}
              onMouseMove={handleMouseMove}
            >
              <button
                onClick={() => setSelectedCategory(null)}
                className={`flex-shrink-0 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 border-2 ${
                  selectedCategory === null
                    ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white border-orange-400 shadow-lg transform scale-105'
                    : 'bg-gray-700/80 text-gray-300 border-gray-600 hover:bg-gray-600/80 hover:border-gray-500 hover:text-white'
                }`}
              >
                <span className="text-lg mr-1">🍽️</span>
                အားလုံး
              </button>
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex-shrink-0 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 border-2 whitespace-nowrap ${
                    selectedCategory === category.id
                      ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white border-orange-400 shadow-lg transform scale-105'
                      : 'bg-gray-700/80 text-gray-300 border-gray-600 hover:bg-gray-600/80 hover:border-gray-500 hover:text-white'
                  }`}
                >
                  <span className="text-lg mr-1">{getCategoryIcon(category.name_mm, category.icon)}</span>
                  {category.name_mm}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Menu Items */}
        <div className="mb-8">
          <div className="mb-6">
            <div className="bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4 border border-gray-700">
              <h2 className="text-xl font-bold text-center text-white">
                {selectedCategory === null
                  ? '🍽️ အားလုံး မီနူးများ'
                  : `${categories.find(cat => cat.id === selectedCategory)?.icon || '🍽️'} ${categories.find(cat => cat.id === selectedCategory)?.name_mm || 'မီနူးများ'}`
                }
              </h2>
              <p className="text-sm text-center text-gray-300 mt-1">
                {getDisplayItems().length} ခု ရရှိနိုင်ပါသည်
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {getDisplayItems().map((item) => (
              <div key={item.id} className="bg-gray-800/80 backdrop-blur-sm rounded-xl border border-gray-700 hover:bg-gray-700/80 transition-all duration-300 hover:border-gray-600 overflow-hidden">
                {/* Image Section */}
                <div className="h-55 bg-gradient-to-br from-gray-700 to-gray-800 relative overflow-hidden flex items-center justify-center">
                  {item.image_url ? (
                    <Image
                      src={item.image_url.startsWith('http') ? item.image_url : `http://localhost:5000${item.image_url}`}
                      alt={item.name}
                      fill
                      className="object-contain hover:scale-105 transition-transform duration-300"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                        if (e.currentTarget.parentElement) {
                          e.currentTarget.parentElement.innerHTML = '<div class="text-4xl opacity-50">🍽️</div>';
                        }
                      }}
                    />
                  ) : (
                    <div className="text-4xl opacity-50">
                      {getCategoryIcon(item.category_name_mm || 'အထွေထွေ')}
                    </div>
                  )}
                  {item.is_today_special && (
                    <div className="absolute top-2 right-2 bg-yellow-500 text-yellow-900 text-xs px-2 py-1 rounded-full font-bold">
                      ⭐ အထူး
                    </div>
                  )}
                </div>

                {/* Content Section */}
                <div className="p-4">
                  <h3 className="text-base font-bold text-white mb-1 line-clamp-1">{item.name_mm}</h3>
                  <p className="text-xs text-gray-300 mb-2 line-clamp-1">{item.name}</p>
                  <p className="text-xs text-gray-400 mb-3 line-clamp-2">{item.description_mm}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-bold text-green-400">{item.price.toLocaleString()} ကျပ်</span>
                    <button
                      onClick={() => addToCart(item)}
                      disabled={!item.is_available}
                      className={`px-3 py-1.5 rounded-lg transition-colors text-xs font-medium ${
                        item.is_available
                          ? 'bg-blue-600 hover:bg-blue-700 text-white'
                          : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                      }`}
                    >
                      {item.is_available ? '+ ထည့်ရန်' : 'မရရှိ'}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {getDisplayItems().length === 0 && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🍽️</div>
              <h3 className="text-xl font-bold text-gray-300 mb-2">မီနူးများ မရှိသေးပါ</h3>
              <p className="text-gray-400">ဤအမျိုးအစားတွင် ရရှိနိုင်သော မီနူးများ မရှိပါ</p>
            </div>
          )}
        </div>
      </div>

      {/* Cart Modal */}
      {showCart && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gray-800/95 backdrop-blur-sm rounded-3xl max-w-md w-full max-h-[85vh] flex flex-col shadow-2xl border border-gray-700">
            {/* Header */}
            <div className="p-6 text-white bg-gradient-to-r from-blue-600 to-blue-700 rounded-t-3xl flex-shrink-0">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-xl font-bold">🛒 သင့်အမှာများ</h3>
                  <div className="flex items-center gap-2">
                    <p className="text-white/80 text-sm">Order Summary</p>
                    {tableNumber && (
                      <div className="flex items-center gap-1 bg-white/20 rounded-full px-2 py-1">
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span className="text-xs">Real-time Sync</span>
                      </div>
                    )}
                  </div>
                </div>
                <button
                  onClick={() => setShowCart(false)}
                  className="bg-white/20 hover:bg-white/30 rounded-full p-2 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto p-6">
              {cart.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-6xl mb-4">🛒</div>
                  <p className="text-gray-300">အမှာများ မရှိသေးပါ</p>
                  <p className="text-gray-400 text-sm mt-2">မီနူးမှ အစားအသောက်များ ရွေးချယ်ပါ</p>
                  {tableNumber && (
                    <div className="mt-4 p-3 bg-blue-500/20 rounded-lg border border-blue-500/30">
                      <p className="text-blue-300 text-xs">
                        🔄 Real-time sync ဖွင့်ထားပါသည်
                      </p>
                      <p className="text-blue-200 text-xs mt-1">
                        စားပွဲ {tableNumber} မှ အခြားသူများ၏ အမှာများကို တစ်ချိန်တည်းမြင်ရပါမည်
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  {cart.map((item) => (
                    <div key={item.menu_item_id} className="bg-gray-700 rounded-2xl p-4 border border-gray-600">
                      <div className="flex justify-between items-start mb-3">
                        <div className="flex-1">
                          <h4 className="font-bold text-white">{item.name_mm}</h4>
                          <p className="text-sm text-gray-300">{item.price.toLocaleString()} ကျပ်</p>
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-green-400">{(item.price * item.quantity).toLocaleString()} ကျပ်</p>
                        </div>
                      </div>
                      <div className="flex items-center justify-center space-x-4">
                        <button
                          onClick={() => updateCartQuantity(item.menu_item_id, item.quantity - 1)}
                          className="w-10 h-10 rounded-full bg-gray-600 hover:bg-gray-500 flex items-center justify-center font-bold transition-colors text-white"
                        >
                          −
                        </button>
                        <span className="w-12 text-center font-bold text-lg text-white">{item.quantity}</span>
                        <button
                          onClick={() => updateCartQuantity(item.menu_item_id, item.quantity + 1)}
                          className="w-10 h-10 rounded-full bg-gray-600 hover:bg-gray-500 flex items-center justify-center font-bold transition-colors text-white"
                        >
                          +
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Footer with buttons */}
            {cart.length > 0 && (
              <div className="p-6 text-white bg-gradient-to-r from-blue-600 to-blue-700 rounded-b-3xl flex-shrink-0">
                <div className="flex justify-between items-center mb-4">
                  <div>
                    <p className="text-white/80 text-sm">စုစုပေါင်း</p>
                    <p className="text-2xl font-bold">{getTotalAmount().toLocaleString()} ကျပ်</p>
                  </div>
                  <div className="text-right">
                    <p className="text-white/80 text-sm">စုစုပေါင်း အရေအတွက်</p>
                    <p className="text-xl font-bold">{getTotalItems()} ခု</p>
                  </div>
                </div>
                <button
                  onClick={handlePlaceOrder}
                  className="w-full bg-white py-4 px-6 rounded-2xl font-bold hover:bg-gray-50 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 text-blue-600"
                >
                  🚀 အမှာတင်ရန်
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Order History Modal */}
      {showOrderHistory && tableOrderData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-white">🍽️ စားပွဲနံပါတ် {tableNumber} - အမှာစာများ</h2>
              <button
                onClick={() => setShowOrderHistory(false)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ✕
              </button>
            </div>

            <div className="space-y-4">
              {tableOrderData.orders?.map((order: any, orderIndex: number) => (
                <div key={order.id} className="bg-gray-700 p-4 rounded-lg">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h3 className="text-white font-semibold">အမှာစာ #{orderIndex + 1}</h3>
                      <div className="text-xs text-gray-400">
                        {new Date(order.created_at).toLocaleString('my-MM')}
                      </div>
                      <div className="text-xs text-gray-400">
                        အခြေအနေ: <span className={`font-semibold ${
                          order.status === 'pending' ? 'text-yellow-400' :
                          order.status === 'preparing' ? 'text-blue-400' :
                          order.status === 'ready' ? 'text-green-400' :
                          order.status === 'delivered' ? 'text-gray-400' :
                          'text-red-400'
                        }`}>
                          {order.status === 'pending' ? 'စောင့်ဆိုင်းနေသည်' :
                           order.status === 'preparing' ? 'ပြင်ဆင်နေသည်' :
                           order.status === 'ready' ? 'အဆင်သင့်ဖြစ်ပြီ' :
                           order.status === 'delivered' ? 'ပေးပို့ပြီး' :
                           'ပယ်ဖျက်ပြီး'}
                        </span>
                      </div>
                    </div>
                    {order.status === 'pending' && (
                      <button
                        onClick={() => cancelWholeOrder(order.id)}
                        className="bg-red-600 text-white px-3 py-1 rounded text-xs hover:bg-red-500"
                      >
                        ❌ အမှာစာပယ်ဖျက်ရန်
                      </button>
                    )}
                  </div>

                  <div className="space-y-2">
                    {order.items?.map((item: any) => (
                      <div key={item.id} className="flex justify-between items-center bg-gray-600 p-2 rounded">
                        <div className="flex-1">
                          <div className="text-white text-sm">{item.menu_item_name}</div>
                          <div className="text-gray-300 text-xs">
                            {item.price?.toLocaleString()} ကျပ် × {item.quantity}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-green-400 font-semibold text-sm">
                            {(item.price * item.quantity).toLocaleString()} ကျပ်
                          </div>
                          {order.status === 'pending' && (
                            <button
                              onClick={() => cancelOrderItem(order.id, item.id)}
                              className="bg-red-500 text-white px-2 py-1 rounded text-xs mt-1 hover:bg-red-400"
                            >
                              ❌ ပယ်ဖျက်ရန်
                            </button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="text-right mt-3 pt-2 border-t border-gray-600">
                    <div className="text-green-400 font-bold">
                      စုစုပေါင်း: {order.total_amount?.toLocaleString()} ကျပ်
                    </div>
                  </div>
                </div>
              ))}

              <div className="bg-gray-700 p-4 rounded-lg border-2 border-green-500">
                <div className="flex justify-between items-center">
                  <span className="text-white font-bold text-lg">စားပွဲစုစုပေါင်းငွေပမာဏ:</span>
                  <span className="text-green-400 font-bold text-xl">
                    {tableOrderData.total_amount?.toLocaleString()} ကျပ်
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Payment Confirmation Modal */}
      {showPaymentConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-2xl max-w-md w-full max-h-[80vh] overflow-y-auto border border-gray-700">
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-green-600 to-blue-600 p-4 text-white rounded-t-2xl">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-bold">💰 ငွေရှင်းရန်</h3>
                <button
                  onClick={() => setShowPaymentConfirm(false)}
                  className="text-white hover:text-gray-300 text-xl"
                >
                  ✕
                </button>
              </div>
              <p className="text-green-100 text-sm mt-1">🪑 စားပွဲ: {tableNumber}</p>
            </div>

            {/* Order Summary */}
            <div className="p-4">
              <h4 className="text-white font-bold mb-3">📋 သင့်အမှာများ</h4>

              {customerOrders.length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-4xl mb-2">📋</div>
                  <p className="text-gray-300">အမှာများ မရှိပါ</p>
                </div>
              ) : (
                <div className="space-y-3 mb-4">
                  {customerOrders.map((order) => (
                    <div key={order.id} className="bg-gray-700 rounded-lg p-3 border border-gray-600">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <p className="text-white font-medium">အမှာ #{order.id}</p>
                          <p className="text-gray-300 text-xs">
                            {new Date(order.created_at).toLocaleString('my-MM')}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-green-400 font-bold">{order.total_amount.toLocaleString()} ကျပ်</p>
                          <p className="text-gray-400 text-xs">{order.status}</p>
                        </div>
                      </div>

                      {/* Order Items */}
                      {order.items && (
                        <div className="space-y-2">
                          {order.items.map((item: any) => (
                            <div key={item.id} className="bg-gray-600 rounded-lg p-3 border border-gray-500">
                              <div className="flex justify-between items-start mb-2">
                                <div className="flex-1">
                                  <p className="text-white font-medium text-sm">{item.name_mm}</p>
                                  <p className="text-gray-300 text-xs">x{item.quantity}</p>
                                </div>
                                <div className="text-right">
                                  <p className="text-green-400 font-semibold text-sm">
                                    {item.total_price.toLocaleString()} ကျပ်
                                  </p>
                                  <div className="flex items-center gap-1 mt-1">
                                    {item.status === 'completed' ? (
                                      <span className="text-xs bg-green-600 text-white px-2 py-1 rounded">
                                        ✅ ချက်ပြီး
                                      </span>
                                    ) : item.status === 'cooking' ? (
                                      <span className="text-xs bg-yellow-600 text-white px-2 py-1 rounded">
                                        🍳 ချက်နေ
                                      </span>
                                    ) : (
                                      <span className="text-xs bg-gray-500 text-white px-2 py-1 rounded">
                                        ⏳ စောင့်နေ
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </div>

                              {/* Action Buttons */}
                              <div className="flex gap-2 mt-2">
                                {item.status === 'completed' ? (
                                  <button
                                    disabled
                                    className="flex-1 bg-green-600 text-white py-2 px-3 rounded text-xs opacity-50 cursor-not-allowed"
                                  >
                                    ✅ အတည်ပြုပြီး
                                  </button>
                                ) : (
                                  <button
                                    onClick={() => cancelOrderItem(order.id, item.id)}
                                    className="flex-1 bg-red-500 text-white py-2 px-3 rounded text-xs hover:bg-red-400 transition-colors"
                                  >
                                    ❌ ပယ်ဖျက်ရန်
                                  </button>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {/* Total Amount */}
              {customerOrders.length > 0 && (
                <div className="bg-gradient-to-r from-green-600 to-blue-600 rounded-lg p-4 mb-4">
                  <div className="flex justify-between items-center text-white">
                    <span className="font-bold">စုစုပေါင်း:</span>
                    <span className="text-xl font-bold">
                      {customerOrders.reduce((total, order) => total + order.total_amount, 0).toLocaleString()} ကျပ်
                    </span>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-3">
                <button
                  onClick={() => setShowPaymentConfirm(false)}
                  className="flex-1 bg-gray-600 text-white py-3 rounded-xl font-medium hover:bg-gray-700 transition-colors"
                >
                  ပယ်ဖျက်
                </button>
                <button
                  onClick={confirmPaymentRequest}
                  className="flex-1 bg-gradient-to-r from-green-600 to-blue-600 text-white py-3 rounded-xl font-medium hover:from-green-700 hover:to-blue-700 transition-all"
                >
                  💰 ငွေရှင်းမယ်
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
