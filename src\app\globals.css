@import "tailwindcss";

:root {
  --background: #fefefe;
  --foreground: #2d2d2d;
}

/* Custom scrollbar styles */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* LCD Marquee Animation */
@keyframes marquee {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.animate-marquee {
  animation: marquee 30s linear infinite;
}

.animate-marquee:hover {
  animation-play-state: paused;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-mono: 'Courier New', monospace;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  line-height: 1.5;
}

/* Custom scrollbar hide */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
  40%, 43% { transform: translate3d(0, -30px, 0); }
  70% { transform: translate3d(0, -15px, 0); }
  90% { transform: translate3d(0, -4px, 0); }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out;
}

.animate-bounce-custom {
  animation: bounce 1s infinite;
}

/* Custom button styles */
.btn-primary {
  @apply bg-gradient-to-r from-orange-600 to-red-600 text-white font-bold py-3 px-6 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300;
}

.btn-secondary {
  @apply bg-white text-orange-600 font-bold py-3 px-6 rounded-2xl border-2 border-orange-600 hover:bg-orange-50 transition-all duration-300;
}

/* Card styles */
.card {
  @apply bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-orange-200 p-6;
}

.card-hover {
  @apply hover:shadow-xl hover:scale-105 transition-all duration-300;
}

/* Myanmar font support */
@font-face {
  font-family: 'Myanmar';
  src: local('Myanmar Text'), local('Pyidaungsu'), local('Padauk');
  unicode-range: U+1000-109F, U+AA60-AA7F, U+A9E0-A9FF;
}

.myanmar-text {
  font-family: 'Myanmar', 'Segoe UI', sans-serif;
}
