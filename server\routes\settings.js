const express = require('express');
const router = express.Router();
const { getDb } = require('../database/db');

// Get all settings
router.get('/', (req, res) => {
  const db = getDb();

  db.all('SELECT * FROM settings ORDER BY key', (err, rows) => {
    if (err) {
      console.error('Error fetching settings:', err);
      res.status(500).json({ error: 'Failed to fetch settings' });
      return;
    }

    // Convert to key-value object
    const settings = {};
    rows.forEach(row => {
      // Return just the value for simpler handling in frontend
      settings[row.key] = row.value || '';
    });

    res.json(settings);
  });
});

// Get specific setting
router.get('/:key', (req, res) => {
  const db = getDb();
  const { key } = req.params;
  
  db.get('SELECT * FROM settings WHERE key = ?', [key], (err, row) => {
    if (err) {
      console.error('Error fetching setting:', err);
      res.status(500).json({ error: 'Failed to fetch setting' });
      return;
    }
    
    if (!row) {
      res.status(404).json({ error: 'Setting not found' });
      return;
    }
    
    res.json(row);
  });
});

// Update setting
router.put('/:key', (req, res) => {
  const db = getDb();
  const { key } = req.params;
  const { value } = req.body;
  
  if (!value && value !== '') {
    res.status(400).json({ error: 'Value is required' });
    return;
  }
  
  db.run(
    'UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?',
    [value, key],
    function(err) {
      if (err) {
        console.error('Error updating setting:', err);
        res.status(500).json({ error: 'Failed to update setting' });
        return;
      }
      
      if (this.changes === 0) {
        res.status(404).json({ error: 'Setting not found' });
        return;
      }
      
      // Emit real-time update
      const io = req.app.get('io');
      if (io) {
        io.emit('settings-updated', { [key]: value });
      }

      res.json({
        message: 'Setting updated successfully',
        key,
        value,
        updated_at: new Date().toISOString()
      });
    }
  );
});

// Update multiple settings
router.put('/', (req, res) => {
  const db = getDb();
  const settings = req.body;
  
  if (!settings || typeof settings !== 'object') {
    res.status(400).json({ error: 'Settings object is required' });
    return;
  }
  
  const updates = Object.entries(settings);
  let completed = 0;
  let errors = [];
  
  if (updates.length === 0) {
    res.status(400).json({ error: 'No settings to update' });
    return;
  }
  
  updates.forEach(([key, value]) => {
    db.run(
      'UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?',
      [value, key],
      function(err) {
        completed++;
        
        if (err) {
          errors.push({ key, error: err.message });
        }
        
        if (completed === updates.length) {
          if (errors.length > 0) {
            res.status(500).json({ 
              message: 'Some settings failed to update',
              errors 
            });
          } else {
            // Emit real-time update for all settings
            const io = req.app.get('io');
            if (io) {
              io.emit('settings-updated', settings);
            }

            res.json({
              message: 'All settings updated successfully',
              updated: updates.length
            });
          }
        }
      }
    );
  });
});

module.exports = router;
