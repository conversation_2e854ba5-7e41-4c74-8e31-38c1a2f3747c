@echo off
setlocal enabledelayedexpansion
title Step 4 - Restaurant System Data Inserter
color 0C

echo.
echo ================================================================
echo                Step 4 - Restaurant System                     
echo                    Data Inserter                              
echo                                                               
echo    Step 4: Insert Sample Data and Test Data                  
echo ================================================================
echo.

:MENU
echo Step 4 - Data Insertion Options:
echo.
echo [1] Insert Sample Categories (10 items)
echo [2] Insert Sample Menu Items (40 items)
echo [3] Insert Sample Users (5 users)
echo [4] Insert Sample Tables (10 tables)
echo [5] Insert Sample Settings (12 settings)
echo [6] Insert Sample Orders (5 orders)
echo [7] Insert Complete Sample Data (All at once)
echo [8] Insert Test Data
echo [9] View Current Data Status
echo [0] Back to Main Menu
echo.
set /p choice="Select option (0-9): "

if "%choice%"=="1" goto INSERT_CATEGORIES
if "%choice%"=="2" goto INSERT_MENU
if "%choice%"=="3" goto INSERT_USERS
if "%choice%"=="4" goto INSERT_TABLES
if "%choice%"=="5" goto INSERT_SETTINGS
if "%choice%"=="6" goto INSERT_ORDERS
if "%choice%"=="7" (
    echo.
    echo Step 4.7 - Inserting Complete Sample Data...
    echo =======================================
    echo This will insert all sample data at once!
    echo.
    echo What will be inserted:
    echo    - 10 Categories
    echo    - 40 Menu Items ^(10 special items^)
    echo    - 5 Users
    echo    - 10 Tables
    echo    - 12 Settings
    echo    - 5 Orders
    echo.
    set /p confirm="Continue? (Y/N): "
    if /i "!confirm!"=="Y" (
        echo.
        echo Step 1/6: Inserting Categories...
        call :INSERT_CATEGORIES_SILENT
        echo Categories inserted successfully!
        echo Step 2/6: Inserting Menu Items...
        call :INSERT_MENU_SILENT
        echo Menu items inserted successfully!
        echo Step 3/6: Inserting Users...
        call :INSERT_USERS_SILENT
        echo Users inserted successfully!
        echo Step 4/6: Inserting Tables...
        call :INSERT_TABLES_SILENT
        echo Tables inserted successfully!
        echo Step 5/6: Inserting Settings...
        call :INSERT_SETTINGS_SILENT
        echo Settings inserted successfully!
        echo Step 6/6: Inserting Orders...
        call :INSERT_ORDERS_SILENT
        echo Orders inserted successfully!
        echo.
        echo Complete sample data insertion finished!
        echo Your restaurant system is now ready to use!
        echo.
        echo What was inserted:
        echo   - 10 Categories ^(Rice, Noodles, Curries, Salads, Desserts, etc.^)
        echo   - 40 Menu Items with prices ^(10 special items^)
        echo   - 5 Users ^(admin, waiters, kitchen, cashier^)
        echo   - 10 Tables ^(T001-T010^)
        echo   - 12 System Settings
        echo   - 5 Sample Orders
        echo.
        echo You can now open the web interface to see the data!
        pause
    )
    goto MENU
)
if "%choice%"=="8" goto INSERT_TEST
if "%choice%"=="9" goto VIEW_STATUS
if "%choice%"=="0" goto EXIT
goto INVALID

:INSERT_CATEGORIES
echo.
echo Step 4.1 - Inserting Sample Categories...
echo =======================================
echo Inserting 10 categories:
echo    - Rice Dishes, Noodles, Curries, Salads, Desserts
echo    - Beverages, Snacks, Meat Dishes, Seafood, Vegetables
cd /d "%~dp0\server"
node -e "const{getDb}=require('./database/db');const db=getDb();const categories=[[1,'Rice Dishes','ထမင်းများ','🍚',1],[2,'Noodles','ခေါက်ဆွဲများ','🍜',2],[3,'Curries','ဟင်းများ','🍛',3],[4,'Salads','သုပ်များ','🥗',4],[5,'Desserts','အချိုများ','🍰',5],[6,'Beverages','ယမကာများ','🥤',6],[7,'Snacks','မုန့်များ','🥖',7],[8,'Meat Dishes','အသားများ','🍖',8],[9,'Seafood','ပင်လယ်စာများ','🐟',9],[10,'Vegetables','ဟင်းသီးဟင်းရွက်များ','🥬',10]];let completed=0;categories.forEach(cat=>{db.run('INSERT OR REPLACE INTO categories(id,name,name_mm,icon,sort_order,is_active)VALUES(?,?,?,?,?,1)',cat,(err)=>{if(err)console.error('Error:',err);else console.log('Added: '+cat[1]+' with icon: '+cat[3]);completed++;if(completed===categories.length){console.log('\nAll categories with single icons inserted successfully!');process.exit(0);}});});"
pause
goto MENU

:INSERT_MENU
echo.
echo Step 4.2 - Inserting Sample Menu Items...
echo =======================================
echo Inserting 40 menu items across 10 categories:
echo    - Rice Dishes (5), Noodles (7), Curries (6), Salads (5)
echo    - Desserts (6), Beverages (4), Snacks (3), Meat Dishes (4)
echo    - Including 10 special menu items
echo.
echo This may take a moment...
cd /d "%~dp0\server"
node -e "const{getDb}=require('./database/db');const db=getDb();console.log('Inserting 40 menu items...');const items=[[1,'Fried Rice','ထမင်းကြော်','Delicious fried rice','အရသာရှိသော ထမင်းကြော်',3000,1,0],[1,'Plain Rice','ထမင်းဖြူ','Steamed white rice','ပြုတ်ထမင်းဖြူ',1500,1,0],[1,'Chicken Fried Rice','ကြက်သားထမင်းကြော်','Delicious chicken fried rice','အရသာရှိသော ကြက်သားထမင်းကြော်',3500,1,1],[1,'Pork Fried Rice','ဝက်သားထမင်းကြော်','Fried rice with pork','ဝက်သားနှင့် ထမင်းကြော်',3200,1,0],[1,'Shrimp Fried Rice','ပုစွန်ထမင်းကြော်','Fried rice with shrimp','ပုစွန်နှင့် ထမင်းကြော်',4000,1,1],[2,'Mohinga','မုန့်ဟင်းခါး','Traditional Myanmar fish noodle soup','မြန်မာ့ရိုးရာ ငါးခေါက်ဆွဲ',2500,1,1],[2,'Shan Noodles','ရှမ်းခေါက်ဆွဲ','Traditional Shan style noodles','ရှမ်းပြည်နယ်စတိုင် ခေါက်ဆွဲ',2800,1,1],[2,'Coconut Noodles','အုန်းခေါက်ဆွဲ','Noodles in coconut milk','အုန်းရည်နှင့် ခေါက်ဆွဲ',3000,1,0],[2,'Dry Noodles','ခေါက်ဆွဲခြောက်','Dry mixed noodles','ရောစပ်ခေါက်ဆွဲ',2500,1,0],[2,'Rakhine Noodles','ရခိုင်ခေါက်ဆွဲ','Rakhine style fish noodles','ရခိုင်စတိုင် ငါးခေါက်ဆွဲ',3200,1,1],[2,'Nan Gyi Thoke','နန်းကြီးသုပ်','Thick rice noodle salad','ခေါက်ဆွဲထူသုပ်',2800,1,0],[2,'Khauk Swe Thoke','ခေါက်ဆွဲသုပ်','Noodle salad','ခေါက်ဆွဲသုပ်ရောစပ်',2800,1,0],[3,'Chicken Curry','ကြက်သားဟင်း','Spicy chicken curry','စပ်သော ကြက်သားဟင်း',4000,1,1],[3,'Pork Curry','ဝက်သားဟင်း','Spicy pork curry','စပ်သော ဝက်သားဟင်း',4200,1,1],[3,'Fish Curry','ငါးဟင်း','Traditional fish curry','ရိုးရာ ငါးဟင်း',3800,1,0],[3,'Beef Curry','နွားသားဟင်း','Rich beef curry','နွားသားဟင်းချို',4500,1,1],[3,'Mutton Curry','သိုးသားဟင်း','Tender mutton curry','သိုးသားဟင်းချို',5000,1,0],[3,'Prawn Curry','ပုစွန်ဟင်း','Spicy prawn curry','စပ်သော ပုစွန်ဟင်း',4800,1,1],[4,'Tea Leaf Salad','လပက်သုပ်','Traditional tea leaf salad','မြန်မာ့ရိုးရာ လပက်သုပ်',3000,1,1],[4,'Ginger Salad','ချင်းသုပ်','Fresh ginger salad','ချင်းလတ်သုပ်',2500,1,0],[4,'Tomato Salad','ခရမ်းချဉ်သုပ်','Fresh tomato salad','ခရမ်းချဉ်လတ်သုပ်',2200,1,0],[4,'Cucumber Salad','သခွားသီးသုပ်','Refreshing cucumber salad','သခွားသီးလတ်သုပ်',2000,1,0],[4,'Pennywort Salad','မြက်ပုပ်သုပ်','Healthy pennywort salad','မြက်ပုပ်လတ်သုပ်',2800,1,1],[5,'Shwe Yin Aye','ရွှေရင်အေး','Traditional Myanmar dessert','မြန်မာ့ရိုးရာ အချိုပွဲ',2000,1,0],[5,'Mont Lone Yay Paw','မုန့်လုံးရေပေါ်','Glutinous rice balls in coconut milk','အုန်းရည်နှင့် မုန့်လုံး',1800,1,0],[5,'Thagu','သာကူ','Sago pudding','သာကူပေါင်း',1500,1,0],[5,'Sanwin Makin','ဆန်ဝင်မကင်း','Semolina cake','ဆန်ဝင်မုန့်',2200,1,1],[5,'Htamanay','ထမနဲ','Sticky rice cake','ကောက်ညှင်းမုန့်',2000,1,0],[5,'Ice Cream','ရေခဲမုန့်','Homemade ice cream','အိမ်လုပ်ရေခဲမုန့်',2500,1,0],[6,'Myanmar Tea','မြန်မာလက်ဖက်ရည်','Traditional Myanmar tea','မြန်မာ့ရိုးရာ လက်ဖက်ရည်',800,1,0],[6,'Lime Juice','သံပုရာရည်','Fresh lime juice','သံပုရာရည်လတ်',1200,1,0],[6,'Sugarcane Juice','ကြံရည်','Fresh sugarcane juice','ကြံရည်လတ်',1500,1,0],[6,'Coconut Water','အုန်းရည်','Fresh coconut water','အုန်းရည်လတ်',1800,1,0],[7,'Samosa','ဆမူဆာ','Crispy samosa','ဆမူဆာကြွပ်',1500,1,0],[7,'Spring Roll','ကော်ပြန့်','Fresh spring roll','ကော်ပြန့်လတ်',2000,1,0],[7,'Htamin Jin','ထမင်းချဉ်','Fermented rice snack','ထမင်းချဉ်မုန့်',1200,1,0],[8,'Grilled Chicken','ကြက်သားကင်','BBQ grilled chicken','ကြက်သားကင်ပေါင်း',5500,1,0],[8,'Grilled Pork','ဝက်သားကင်','BBQ grilled pork','ဝက်သားကင်ပေါင်း',6000,1,0],[8,'Beef Steak','နွားသားစတိတ်','Tender beef steak','နွားသားစတိတ်ပြား',7500,1,0],[8,'Lamb Chops','သိုးသားကင်','Grilled lamb chops','သိုးသားကင်ပေါင်း',8000,1,0]];let completed=0;items.forEach(item=>{db.run('INSERT INTO menu_items(category_id,name,name_mm,description,description_mm,price,is_available,is_today_special,is_active)VALUES(?,?,?,?,?,?,?,?,1)',item,(err)=>{if(err)console.error('Error:',err);else console.log('Added: '+item[2]+' ('+item[1]+') - '+item[5]+' MMK'+(item[7]?' ⭐':''));completed++;if(completed===items.length){console.log('\nAll 40 menu items inserted successfully!');console.log('Special items: 10');process.exit(0);}});});"
pause
goto MENU

:INSERT_USERS
echo.
echo Step 4.3 - Inserting Sample Users...
echo =======================================
echo Inserting 5 sample users:
echo    - admin (Administrator)
echo    - waiter1, waiter2 (Staff)
echo    - kitchen1 (Kitchen Staff)
echo    - cashier1 (Cashier)
cd /d "%~dp0\server"
node -e "const{getDb}=require('./database/db');const bcrypt=require('bcryptjs');const db=getDb();const users=[['admin','admin123','admin','Administrator'],['waiter1','waiter123','staff','Waiter 1'],['waiter2','waiter123','staff','Waiter 2'],['kitchen1','kitchen123','kitchen','Kitchen Staff'],['cashier1','cashier123','cashier','Cashier']];let completed=0;users.forEach(user=>{const hashedPassword=bcrypt.hashSync(user[1],10);db.run('INSERT INTO users(username,password,role,name)VALUES(?,?,?,?)',[user[0],hashedPassword,user[2],user[3]],(err)=>{if(err)console.error('Error:',err);else console.log('Added user: '+user[3]+' (@'+user[0]+') - Role: '+user[2]);completed++;if(completed===users.length){console.log('\nAll users inserted successfully!');process.exit(0);}});});"
pause
goto MENU

:INSERT_TABLES
echo.
echo Step 4.4 - Inserting Sample Tables...
echo =======================================
echo Inserting 10 sample tables:
echo    - T001 to T010
echo    - Different capacities (2-8 seats)
cd /d "%~dp0\server"
node -e "const{getDb}=require('./database/db');const db=getDb();const tables=[['T001','Table 1',4],['T002','Table 2',4],['T003','Table 3',6],['T004','Table 4',4],['T005','Table 5',2],['T006','Table 6',4],['T007','Table 7',8],['T008','Table 8',4],['T009','Table 9',4],['T010','Table 10',6]];let completed=0;tables.forEach(table=>{db.run('INSERT INTO tables(table_number,table_name,capacity,is_active)VALUES(?,?,?,1)',table,(err)=>{if(err)console.error('Error:',err);else console.log('Added table: '+table[1]+' ('+table[0]+') - Capacity: '+table[2]+' seats');completed++;if(completed===tables.length){console.log('\nAll tables inserted successfully!');process.exit(0);}});});"
pause
goto MENU

:INSERT_SETTINGS
echo.
echo Step 4.5 - Inserting Sample Settings...
echo =======================================
echo Inserting 12 sample settings:
echo    - Restaurant name, address, phone
echo    - Opening hours, currency, tax rate
cd /d "%~dp0\server"
node -e "const{getDb}=require('./database/db');const db=getDb();const settings=[['restaurant_name','A Shin Restaurant'],['restaurant_name_mm','အရှင်စားသောက်ဆိုင်'],['restaurant_title_en','Myanmar Traditional Cuisine'],['restaurant_title_mm','မြန်မာ့ရိုးရာ အစားအသောက်များ'],['address','Yangon, Myanmar'],['phone','09-***********'],['email','<EMAIL>'],['opening_hours_en','Always Open for You'],['opening_hours_mm','အမြဲဖွင့်ထားပါသည်'],['currency','MMK'],['tax_rate','5'],['service_charge','10']];let completed=0;settings.forEach(setting=>{db.run('INSERT OR REPLACE INTO settings(key,value)VALUES(?,?)',setting,(err)=>{if(err)console.error('Error:',err);else console.log('Added setting: '+setting[0]+' = '+setting[1]);completed++;if(completed===settings.length){console.log('\nAll settings inserted successfully!');process.exit(0);}});});"
pause
goto MENU

:INSERT_ORDERS
echo.
echo Step 4.6 - Inserting Sample Orders...
echo =======================================
echo Inserting 5 sample orders:
echo    - Different customers and tables
echo    - Various order amounts and statuses
cd /d "%~dp0\server"
node -e "const{getDb}=require('./database/db');const db=getDb();const orders=[['T001','John Doe',7500,'completed'],['T002','Jane Smith',4500,'pending'],['T003','Bob Johnson',6200,'preparing'],['T004','Alice Brown',3800,'completed'],['T005','Charlie Wilson',5900,'pending']];let completed=0;orders.forEach(order=>{db.run('INSERT INTO orders(table_number,customer_name,total_amount,status,created_at)VALUES(?,?,?,?,datetime(\"now\"))',order,(err)=>{if(err)console.error('Error:',err);else console.log('Added order: '+order[1]+' ('+order[0]+') - '+order[2]+' MMK - Status: '+order[3]);completed++;if(completed===orders.length){console.log('\nAll orders inserted successfully!');process.exit(0);}});});"
pause
goto MENU



:INSERT_TEST
echo.
echo Step 4.8 - Inserting Test Data...
echo =======================================
echo Inserting test data...
cd /d "%~dp0\server"
set timestamp=%time:~0,2%%time:~3,2%%time:~6,2%
set timestamp=%timestamp: =0%
node -e "const{getDb}=require('./database/db');const db=getDb();const testName='Test Item %timestamp%';const testNameMM='Test Item %timestamp%';db.run('INSERT INTO menu_items(category_id,name,name_mm,description,description_mm,price,is_available,is_today_special,is_active)VALUES(1,?,?,\"Test description\",\"Test description\",9999,1,0,1)',[testName,testNameMM],(err)=>{if(err)console.error('Error:',err);else{console.log('Test item added successfully!');console.log('Name: '+testName);console.log('Price: 9999 MMK');}process.exit(0);});"
pause
goto MENU

:VIEW_STATUS
echo.
echo Step 4.9 - Current Data Status...
echo =======================================
echo Checking current database data...
cd /d "%~dp0\server"
node -e "const{getDb}=require('./database/db');const db=getDb();console.log('CURRENT DATABASE STATUS:');console.log('=======================================');const tables=['menu_items','categories','users','orders','tables','settings'];let completed=0;tables.forEach(tableName=>{db.get('SELECT COUNT(*) as count FROM '+tableName,[],(err,result)=>{if(err)console.error('Error:',err);else console.log(tableName+': '+result.count+' rows');completed++;if(completed===tables.length){console.log('\nData status check completed!');process.exit(0);}});});"
pause
goto MENU

:INSERT_CATEGORIES_SILENT
cd /d "%~dp0\server"
node -e "const{getDb}=require('./database/db');const db=getDb();db.run('PRAGMA encoding=\"UTF-8\"');const categories=[[1,'Rice Dishes','ထမင်းများ','🍚',1],[2,'Noodles','ခေါက်ဆွဲများ','🍜',2],[3,'Curries','ဟင်းများ','🍛',3],[4,'Salads','သုပ်များ','🥗',4],[5,'Desserts','အချိုများ','🍰',5],[6,'Beverages','ယမကာများ','🥤',6],[7,'Snacks','မုန့်များ','🥖',7],[8,'Meat Dishes','အသားများ','🍖',8],[9,'Seafood','ပင်လယ်စာများ','🐟',9],[10,'Vegetables','ဟင်းသီးဟင်းရွက်များ','🥬',10]];let completed=0;categories.forEach(cat=>{db.run('INSERT OR REPLACE INTO categories(id,name,name_mm,icon,sort_order,is_active)VALUES(?,?,?,?,?,1)',cat,(err)=>{if(err)console.error('Error:',err);completed++;if(completed===categories.length){console.log('Categories inserted successfully');process.exit(0);}});});" 2>nul
goto :eof

:INSERT_MENU_SILENT
cd /d "%~dp0\server"
node -e "const{getDb}=require('./database/db');const db=getDb();db.run('PRAGMA encoding=\"UTF-8\"');const items=[[1,'Fried Rice','ထမင်းကြော်','Delicious fried rice','အရသာရှိသော ထမင်းကြော်',3000,1,0],[1,'Plain Rice','ထမင်းဖြူ','Steamed white rice','ပြုတ်ထမင်းဖြူ',1500,1,0],[1,'Chicken Fried Rice','ကြက်သားထမင်းကြော်','Delicious chicken fried rice','အရသာရှိသော ကြက်သားထမင်းကြော်',3500,1,1],[1,'Pork Fried Rice','ဝက်သားထမင်းကြော်','Fried rice with pork','ဝက်သားနှင့် ထမင်းကြော်',3200,1,0],[1,'Shrimp Fried Rice','ပုစွန်ထမင်းကြော်','Fried rice with shrimp','ပုစွန်နှင့် ထမင်းကြော်',4000,1,1],[2,'Mohinga','မုန့်ဟင်းခါး','Traditional Myanmar fish noodle soup','မြန်မာ့ရိုးရာ ငါးခေါက်ဆွဲ',2500,1,1],[2,'Shan Noodles','ရှမ်းခေါက်ဆွဲ','Traditional Shan style noodles','ရှမ်းပြည်နယ်စတိုင် ခေါက်ဆွဲ',2800,1,1],[2,'Coconut Noodles','အုန်းခေါက်ဆွဲ','Noodles in coconut milk','အုန်းရည်နှင့် ခေါက်ဆွဲ',3000,1,0],[2,'Dry Noodles','ခေါက်ဆွဲခြောက်','Dry mixed noodles','ရောစပ်ခေါက်ဆွဲ',2500,1,0],[2,'Rakhine Noodles','ရခိုင်ခေါက်ဆွဲ','Rakhine style fish noodles','ရခိုင်စတိုင် ငါးခေါက်ဆွဲ',3200,1,1],[2,'Nan Gyi Thoke','နန်းကြီးသုပ်','Thick rice noodle salad','ခေါက်ဆွဲထူသုပ်',2800,1,0],[2,'Khauk Swe Thoke','ခေါက်ဆွဲသုပ်','Noodle salad','ခေါက်ဆွဲသုပ်ရောစပ်',2800,1,0],[3,'Chicken Curry','ကြက်သားဟင်း','Spicy chicken curry','စပ်သော ကြက်သားဟင်း',4000,1,1],[3,'Pork Curry','ဝက်သားဟင်း','Spicy pork curry','စပ်သော ဝက်သားဟင်း',4200,1,1],[3,'Fish Curry','ငါးဟင်း','Traditional fish curry','ရိုးရာ ငါးဟင်း',3800,1,0],[3,'Beef Curry','နွားသားဟင်း','Rich beef curry','နွားသားဟင်းချို',4500,1,1],[3,'Mutton Curry','သိုးသားဟင်း','Tender mutton curry','သိုးသားဟင်းချို',5000,1,0],[3,'Prawn Curry','ပုစွန်ဟင်း','Spicy prawn curry','စပ်သော ပုစွန်ဟင်း',4800,1,1],[4,'Tea Leaf Salad','လပက်သုပ်','Traditional tea leaf salad','မြန်မာ့ရိုးရာ လပက်သုပ်',3000,1,1],[4,'Ginger Salad','ချင်းသုပ်','Fresh ginger salad','ချင်းလတ်သုပ်',2500,1,0],[4,'Tomato Salad','ခရမ်းချဉ်သုပ်','Fresh tomato salad','ခရမ်းချဉ်လတ်သုပ်',2200,1,0],[4,'Cucumber Salad','သခွားသီးသုပ်','Refreshing cucumber salad','သခွားသီးလတ်သုပ်',2000,1,0],[4,'Pennywort Salad','မြက်ပုပ်သုပ်','Healthy pennywort salad','မြက်ပုပ်လတ်သုပ်',2800,1,1],[5,'Shwe Yin Aye','ရွှေရင်အေး','Traditional Myanmar dessert','မြန်မာ့ရိုးရာ အချိုပွဲ',2000,1,0],[5,'Mont Lone Yay Paw','မုန့်လုံးရေပေါ်','Glutinous rice balls in coconut milk','အုန်းရည်နှင့် မုန့်လုံး',1800,1,0],[5,'Thagu','သာကူ','Sago pudding','သာကူပေါင်း',1500,1,0],[5,'Sanwin Makin','ဆန်ဝင်မကင်း','Semolina cake','ဆန်ဝင်မုန့်',2200,1,1],[5,'Htamanay','ထမနဲ','Sticky rice cake','ကောက်ညှင်းမုန့်',2000,1,0],[5,'Ice Cream','ရေခဲမုန့်','Homemade ice cream','အိမ်လုပ်ရေခဲမုန့်',2500,1,0],[6,'Myanmar Tea','မြန်မာလက်ဖက်ရည်','Traditional Myanmar tea','မြန်မာ့ရိုးရာ လက်ဖက်ရည်',800,1,0],[6,'Lime Juice','သံပုရာရည်','Fresh lime juice','သံပုရာရည်လတ်',1200,1,0],[6,'Sugarcane Juice','ကြံရည်','Fresh sugarcane juice','ကြံရည်လတ်',1500,1,0],[6,'Coconut Water','အုန်းရည်','Fresh coconut water','အုန်းရည်လတ်',1800,1,0],[7,'Samosa','ဆမူဆာ','Crispy samosa','ဆမူဆာကြွပ်',1500,1,0],[7,'Spring Roll','ကော်ပြန့်','Fresh spring roll','ကော်ပြန့်လတ်',2000,1,0],[7,'Htamin Jin','ထမင်းချဉ်','Fermented rice snack','ထမင်းချဉ်မုန့်',1200,1,0],[8,'Grilled Chicken','ကြက်သားကင်','BBQ grilled chicken','ကြက်သားကင်ပေါင်း',5500,1,0],[8,'Grilled Pork','ဝက်သားကင်','BBQ grilled pork','ဝက်သားကင်ပေါင်း',6000,1,0],[8,'Beef Steak','နွားသားစတိတ်','Tender beef steak','နွားသားစတိတ်ပြား',7500,1,0],[8,'Lamb Chops','သိုးသားကင်','Grilled lamb chops','သိုးသားကင်ပေါင်း',8000,1,0]];let completed=0;items.forEach(item=>{db.run('INSERT INTO menu_items(category_id,name,name_mm,description,description_mm,price,is_available,is_today_special,is_active)VALUES(?,?,?,?,?,?,?,?,1)',item,(err)=>{if(err)console.error('Error:',err);completed++;if(completed===items.length){console.log('Menu items inserted successfully');process.exit(0);}});});" 2>nul
goto :eof

:INSERT_USERS_SILENT
cd /d "%~dp0\server"
node -e "const{getDb}=require('./database/db');const bcrypt=require('bcryptjs');const db=getDb();db.run('PRAGMA encoding=\"UTF-8\"');const users=[['admin','admin123','admin','Administrator'],['waiter1','waiter123','staff','Waiter 1'],['waiter2','waiter123','staff','Waiter 2'],['kitchen1','kitchen123','kitchen','Kitchen Staff'],['cashier1','cashier123','cashier','Cashier']];let completed=0;users.forEach(user=>{const hashedPassword=bcrypt.hashSync(user[1],10);db.run('INSERT OR REPLACE INTO users(username,password,role,name)VALUES(?,?,?,?)',[user[0],hashedPassword,user[2],user[3]],(err)=>{if(err)console.error('Error:',err);completed++;if(completed===users.length){console.log('Users inserted successfully');process.exit(0);}});});" 2>nul
goto :eof

:INSERT_TABLES_SILENT
cd /d "%~dp0\server"
node -e "const{getDb}=require('./database/db');const db=getDb();db.run('PRAGMA encoding=\"UTF-8\"');const tables=[['T001','Table 1',4],['T002','Table 2',4],['T003','Table 3',6],['T004','Table 4',4],['T005','Table 5',2],['T006','Table 6',4],['T007','Table 7',8],['T008','Table 8',4],['T009','Table 9',4],['T010','Table 10',6]];let completed=0;tables.forEach(table=>{db.run('INSERT OR REPLACE INTO tables(table_number,table_name,capacity,is_active)VALUES(?,?,?,1)',table,(err)=>{if(err)console.error('Error:',err);completed++;if(completed===tables.length){console.log('Tables inserted successfully');process.exit(0);}});});" 2>nul
goto :eof

:INSERT_SETTINGS_SILENT
cd /d "%~dp0\server"
node -e "const{getDb}=require('./database/db');const db=getDb();db.run('PRAGMA encoding=\"UTF-8\"');const settings=[['restaurant_name','A Shin Restaurant'],['restaurant_name_mm','အရှင်စားသောက်ဆိုင်'],['restaurant_title_en','Myanmar Traditional Cuisine'],['restaurant_title_mm','မြန်မာ့ရိုးရာ အစားအသောက်များ'],['address','Yangon, Myanmar'],['phone','09-***********'],['email','<EMAIL>'],['opening_hours_en','Always Open for You'],['opening_hours_mm','အမြဲဖွင့်ထားပါသည်'],['currency','MMK'],['tax_rate','5'],['service_charge','10']];let completed=0;settings.forEach(setting=>{db.run('INSERT OR REPLACE INTO settings(key,value)VALUES(?,?)',setting,(err)=>{if(err)console.error('Error:',err);completed++;if(completed===settings.length){console.log('Settings inserted successfully');process.exit(0);}});});" 2>nul
goto :eof

:INSERT_ORDERS_SILENT
cd /d "%~dp0\server"
node -e "const{getDb}=require('./database/db');const db=getDb();db.run('PRAGMA encoding=\"UTF-8\"');const orders=[['T001','John Doe',7500,'completed'],['T002','Jane Smith',4500,'pending'],['T003','Bob Johnson',6200,'preparing'],['T004','Alice Brown',3800,'completed'],['T005','Charlie Wilson',5900,'pending']];let completed=0;orders.forEach(order=>{db.run('INSERT INTO orders(table_number,customer_name,total_amount,status,created_at)VALUES(?,?,?,?,datetime(\"now\"))',order,(err)=>{if(err)console.error('Error:',err);completed++;if(completed===orders.length){console.log('Orders inserted successfully');process.exit(0);}});});" 2>nul
goto :eof

:INVALID
echo.
echo Invalid option! Please select 0-9.
pause
goto MENU

:EXIT
echo.
echo Step 4 Complete! Returning to Main Menu...
echo Data Management completed
pause
goto :EOF
