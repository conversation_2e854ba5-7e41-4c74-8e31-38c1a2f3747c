'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';

interface Order {
  id: number;
  customer_name: string;
  customer_phone?: string;
  table_number?: string;
  total_amount: number;
  status: string;
  payment_status: string;
  created_at: string;
  items: OrderItem[];
}

interface OrderItem {
  id: number;
  name: string;
  name_mm: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  special_instructions?: string;
  status: string;
  created_at: string;
}

export default function TablePage() {
  const params = useParams();
  const tableNumber = params.tableNumber as string;
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [settings, setSettings] = useState<any>({});
  const [bellAlerts, setBellAlerts] = useState<any[]>([]);

  useEffect(() => {
    fetchTableOrders();
    fetchSettings();
    fetchBellAlerts();
    // Set up polling for real-time updates
    const interval = setInterval(() => {
      fetchTableOrders();
      fetchBellAlerts();
    }, 3000);
    return () => clearInterval(interval);
  }, [tableNumber]);

  const fetchTableOrders = async () => {
    try {
      const response = await fetch(`http://192.168.245.140:5000/api/orders?table_number=${tableNumber}&group_by_table=true`);
      if (response.ok) {
        const data = await response.json();
        if (data.length > 0) {
          // Filter out orders with payment_status 'completed'
          const activeOrders = (data[0].orders || []).filter((order: any) => order.payment_status !== 'completed');
          setOrders(activeOrders);
        } else {
          setOrders([]);
        }
      }
    } catch (error) {
      console.error('Error fetching table orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchSettings = async () => {
    try {
      const response = await fetch('http://192.168.245.140:5000/api/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
    }
  };

  const fetchBellAlerts = async () => {
    try {
      const response = await fetch(`http://192.168.245.140:5000/api/bell-alerts?table_number=${tableNumber}`);
      if (response.ok) {
        const data = await response.json();
        setBellAlerts(data);
      }
    } catch (error) {
      console.error('Error fetching bell alerts:', error);
    }
  };

  const callWaiter = async () => {
    try {
      const response = await fetch('http://192.168.245.140:5000/api/bell-alerts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          table_number: tableNumber,
          message: `စားပွဲ ${tableNumber} မှ လှန်ခေါ်နေပါသည်`
        }),
      });

      if (response.ok) {
        alert('လှန်ခေါ်ပြီးပါပြီ! ဝန်ထမ်းများ လာမည်ဖြစ်ပါသည်။');
        fetchBellAlerts();
      }
    } catch (error) {
      console.error('Error calling waiter:', error);
      alert('လှန်ခေါ်ရာတွင် အမှားရှိပါတယ်။');
    }
  };

  const cancelOrderItem = async (orderId: number, itemId: number) => {
    if (!confirm('ဤအစားအသောက်ကို ပယ်ဖျက်မှာ သေချာပါသလား?')) {
      return;
    }

    try {
      const response = await fetch(`http://192.168.245.140:5000/api/orders/${orderId}/items/${itemId}/cancel`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason: 'Customer requested cancellation' }),
      });

      if (response.ok) {
        alert('ပယ်ဖျက်တောင်းဆိုမှု ပေးပို့ပြီးပါပြီ။ Counter မှ အတည်ပြုပါလိမ့်မယ်။');
        fetchTableOrders();
      }
    } catch (error) {
      console.error('Error cancelling item:', error);
      alert('ပယ်ဖျက်ရာတွင် အမှားရှိပါတယ်။');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
      case 'confirmed': return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
      case 'preparing': return 'bg-orange-500/20 text-orange-300 border-orange-500/30';
      case 'ready': return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'served': return 'bg-purple-500/20 text-purple-300 border-purple-500/30';
      case 'cancelled': return 'bg-red-500/20 text-red-300 border-red-500/30';
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '⏳ စောင့်ဆိုင်းနေသည်';
      case 'confirmed': return '✅ အတည်ပြုပြီး';
      case 'preparing': return '👨‍🍳 ချက်နေသည်';
      case 'ready': return '🍽️ အဆင်သင့်';
      case 'served': return '🚚 ပေးပြီး';
      case 'cancelled': return '❌ ပယ်ဖျက်ပြီး';
      default: return status;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center bg-gray-800 rounded-2xl p-8 border border-gray-700">
          <div className="text-4xl mb-4">🪑</div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-300 font-medium">စားပွဲ {tableNumber} အမှာများ ရယူနေသည်...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 relative">
      {/* Background Food Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 text-6xl">🪑</div>
        <div className="absolute top-20 right-20 text-4xl">🍽️</div>
        <div className="absolute top-40 left-1/4 text-5xl">🍜</div>
        <div className="absolute top-60 right-1/3 text-3xl">🥗</div>
        <div className="absolute bottom-40 left-20 text-4xl">🍛</div>
        <div className="absolute bottom-20 right-10 text-5xl">🍱</div>
        <div className="absolute bottom-60 left-1/2 text-3xl">🔔</div>
        <div className="absolute top-80 left-1/3 text-4xl">👨‍🍳</div>
        <div className="absolute bottom-80 right-1/4 text-3xl">⏰</div>
        <div className="absolute top-1/4 left-1/2 text-5xl">🚚</div>
      </div>

      {/* Header */}
      <header className="bg-gray-800 border-b border-gray-700 sticky top-0 z-40">
        <div className="max-w-6xl mx-auto px-4 py-3">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-lg font-bold">
                🪑
              </div>
              <div>
                <h1 className="text-lg font-bold text-white">စားပွဲ {tableNumber}</h1>
                <p className="text-xs text-gray-400">Table {tableNumber}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <Link 
                href="/menu" 
                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm transition-colors"
              >
                📋 မီနူး
              </Link>
              <button
                onClick={callWaiter}
                className="bg-orange-600 hover:bg-orange-700 text-white px-3 py-2 rounded-lg text-sm transition-colors"
              >
                🔔 လှန်ခေါ်မယ်
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-4 py-6 relative z-10">
        {/* Bell Alerts */}
        {bellAlerts.length > 0 && (
          <div className="mb-6">
            {bellAlerts.map((alert) => (
              <div key={alert.id} className="bg-green-500/20 border border-green-500/30 rounded-xl p-4 mb-3">
                <div className="flex items-center gap-3">
                  <span className="text-2xl">✅</span>
                  <div>
                    <p className="text-green-300 font-medium">လာပါ့ပြီရှင်!</p>
                    <p className="text-green-200 text-sm">ဝန်ထမ်းများ လာမည်ဖြစ်ပါသည်။</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Orders */}
        {orders.length === 0 ? (
          <div className="text-center py-12">
            <div className="bg-gray-800/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-700">
              <div className="text-6xl mb-4">🪑</div>
              <h2 className="text-xl font-bold text-white mb-2">အမှာများ မရှိပါ</h2>
              <p className="text-gray-300 mb-6">ဤစားပွဲအတွက် အမှာများ မရှိသေးပါ</p>
              <Link
                href="/menu"
                className="bg-blue-600 text-white px-6 py-3 rounded-xl font-medium hover:bg-blue-700 transition-colors"
              >
                📋 မီနူးမှ မှာယူရန်
              </Link>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {orders.map((order) => (
              <div key={order.id} className="bg-gray-800/90 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-700 overflow-hidden">
                {/* Order Header */}
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-4 text-white">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-bold text-lg">အမှာ #{order.id}</h3>
                      <p className="text-blue-100 text-sm">{order.customer_name}</p>
                      <p className="text-blue-100 text-xs">
                        {new Date(order.created_at).toLocaleString('my-MM')}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-lg">{order.total_amount.toLocaleString()}</p>
                      <p className="text-blue-100 text-xs">ကျပ်</p>
                    </div>
                  </div>
                </div>

                {/* Order Items */}
                <div className="p-4">
                  <div className="space-y-3">
                    {order.items?.map((item) => (
                      <div key={item.id} className="bg-gray-700/50 rounded-lg p-3 border border-gray-600">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <p className="font-medium text-white text-sm">{item.name_mm}</p>
                            <p className="text-gray-300 text-xs">{item.name}</p>
                            <p className="text-gray-300 text-xs">x{item.quantity} × {item.unit_price.toLocaleString()} ကျပ်</p>
                            {item.special_instructions && (
                              <p className="text-yellow-300 text-xs mt-1">📝 {item.special_instructions}</p>
                            )}
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-green-400 text-sm">{item.total_price.toLocaleString()} ကျပ်</p>
                            <div className={`px-2 py-1 rounded text-xs mt-1 ${getStatusColor(item.status)}`}>
                              {getStatusText(item.status)}
                            </div>
                          </div>
                        </div>

                        {/* Cancellation Status */}
                        {item.cancellation_reason && item.cancellation_reason.includes('Customer requested') && item.status !== 'cancelled' && (
                          <div className="mt-2 p-2 bg-orange-500/20 border border-orange-500/30 rounded text-xs">
                            <p className="text-orange-300 font-medium">🚨 ပယ်ဖျက်တောင်းဆိုထားသည်</p>
                            <p className="text-orange-200 text-xs">Counter မှ အတည်ပြုရန် စောင့်ဆိုင်းနေပါသည်</p>
                          </div>
                        )}

                        {/* Cancel Button for uncooked items */}
                        {(item.status === 'pending' || item.status === 'confirmed') &&
                         (!item.cancellation_reason || !item.cancellation_reason.includes('Customer requested')) && (
                          <div className="mt-2">
                            <button
                              onClick={() => cancelOrderItem(order.id, item.id)}
                              className="bg-red-500/20 text-red-300 py-1 px-3 rounded text-xs hover:bg-red-500/30 transition-colors"
                            >
                              ❌ ပယ်ဖျက်မယ်
                            </button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
