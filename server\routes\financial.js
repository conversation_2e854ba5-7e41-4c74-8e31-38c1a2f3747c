const express = require('express');
const { getDb } = require('../database/db');
const router = express.Router();

// Get all expenses
router.get('/expenses', (req, res) => {
  const { start_date, end_date, category, limit = 50 } = req.query;
  const db = getDb();

  let query = `
    SELECT e.*, ec.name as category_name, ec.name_mm as category_name_mm, ec.icon as category_icon
    FROM expenses e
    LEFT JOIN expense_categories ec ON e.category = ec.name
    WHERE 1=1
  `;

  const params = [];

  if (start_date) {
    query += ` AND DATE(e.expense_date) >= ?`;
    params.push(start_date);
  }

  if (end_date) {
    query += ` AND DATE(e.expense_date) <= ?`;
    params.push(end_date);
  }

  if (category) {
    query += ` AND e.category = ?`;
    params.push(category);
  }

  query += ` ORDER BY e.expense_date DESC, e.created_at DESC LIMIT ?`;
  params.push(parseInt(limit));

  db.all(query, params, (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error' });
    }
    res.json(rows);
  });
});

// Add new expense
router.post('/expenses', (req, res) => {
  const {
    category,
    category_mm,
    description,
    description_mm,
    amount,
    expense_date,
    receipt_image,
    vendor_name,
    vendor_phone,
    payment_method = 'cash',
    notes,
    created_by
  } = req.body;

  const db = getDb();

  db.run(`
    INSERT INTO expenses (
      category, category_mm, description, description_mm, amount, 
      expense_date, receipt_image, vendor_name, vendor_phone, 
      payment_method, notes, created_by
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `, [
    category, category_mm, description, description_mm, amount,
    expense_date, receipt_image, vendor_name, vendor_phone,
    payment_method, notes, created_by
  ], function(err) {
    if (err) {
      return res.status(500).json({ error: 'Failed to add expense' });
    }
    res.json({ id: this.lastID, message: 'Expense added successfully' });
  });
});

// Get expense categories
router.get('/expense-categories', (req, res) => {
  const db = getDb();

  db.all(`
    SELECT * FROM expense_categories 
    WHERE is_active = 1 
    ORDER BY name
  `, (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error' });
    }
    res.json(rows);
  });
});

// Get customers
router.get('/customers', (req, res) => {
  const { search, limit = 50 } = req.query;
  const db = getDb();

  let query = `
    SELECT * FROM customers 
    WHERE 1=1
  `;

  const params = [];

  if (search) {
    query += ` AND (name LIKE ? OR name_mm LIKE ? OR phone LIKE ?)`;
    const searchTerm = `%${search}%`;
    params.push(searchTerm, searchTerm, searchTerm);
  }

  query += ` ORDER BY last_order_date DESC, created_at DESC LIMIT ?`;
  params.push(parseInt(limit));

  db.all(query, params, (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error' });
    }
    res.json(rows);
  });
});

// Add or update customer
router.post('/customers', (req, res) => {
  const { name, name_mm, phone } = req.body;
  const db = getDb();

  // Check if customer exists
  db.get(`SELECT * FROM customers WHERE phone = ?`, [phone], (err, existingCustomer) => {
    if (err) {
      return res.status(500).json({ error: 'Database error' });
    }

    if (existingCustomer) {
      // Update existing customer
      db.run(`
        UPDATE customers 
        SET name = ?, name_mm = ?, updated_at = CURRENT_TIMESTAMP
        WHERE phone = ?
      `, [name, name_mm, phone], function(err) {
        if (err) {
          return res.status(500).json({ error: 'Failed to update customer' });
        }
        res.json({ id: existingCustomer.id, message: 'Customer updated successfully' });
      });
    } else {
      // Insert new customer
      db.run(`
        INSERT INTO customers (name, name_mm, phone)
        VALUES (?, ?, ?)
      `, [name, name_mm, phone], function(err) {
        if (err) {
          return res.status(500).json({ error: 'Failed to add customer' });
        }
        res.json({ id: this.lastID, message: 'Customer added successfully' });
      });
    }
  });
});

// Get profit/loss statement
router.get('/profit-loss', (req, res) => {
  const { start_date, end_date } = req.query;
  const db = getDb();

  const params = [];
  let dateFilter = '';

  if (start_date && end_date) {
    dateFilter = ` AND DATE(created_at) BETWEEN ? AND ?`;
    params.push(start_date, end_date);
  }

  // Get revenue
  const revenueQuery = `
    SELECT 
      SUM(total_amount) as total_revenue,
      SUM(tax_amount) as total_tax,
      COUNT(*) as total_orders
    FROM orders 
    WHERE status != 'cancelled'${dateFilter}
  `;

  // Get expenses
  const expenseQuery = `
    SELECT 
      SUM(amount) as total_expenses,
      category,
      SUM(amount) as category_total
    FROM expenses 
    WHERE 1=1${dateFilter.replace('created_at', 'expense_date')}
    GROUP BY category
  `;

  // Get total expenses
  const totalExpenseQuery = `
    SELECT SUM(amount) as total_expenses
    FROM expenses 
    WHERE 1=1${dateFilter.replace('created_at', 'expense_date')}
  `;

  db.get(revenueQuery, params, (err, revenueData) => {
    if (err) {
      return res.status(500).json({ error: 'Database error' });
    }

    db.all(expenseQuery, params, (err, expensesByCategory) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      db.get(totalExpenseQuery, params, (err, totalExpenseData) => {
        if (err) {
          return res.status(500).json({ error: 'Database error' });
        }

        const totalRevenue = revenueData.total_revenue || 0;
        const totalExpenses = totalExpenseData.total_expenses || 0;
        const totalTax = revenueData.total_tax || 0;
        const grossProfit = totalRevenue - totalTax;
        const netProfit = grossProfit - totalExpenses;

        res.json({
          revenue: {
            total_revenue: totalRevenue,
            total_tax: totalTax,
            gross_revenue: grossProfit,
            total_orders: revenueData.total_orders || 0
          },
          expenses: {
            total_expenses: totalExpenses,
            by_category: expensesByCategory
          },
          profit: {
            gross_profit: grossProfit,
            net_profit: netProfit,
            profit_margin: totalRevenue > 0 ? ((netProfit / totalRevenue) * 100) : 0
          }
        });
      });
    });
  });
});

// Get financial overview
router.get('/overview', (req, res) => {
  const { period = 'today' } = req.query;
  const db = getDb();

  let dateFilter = '';
  const params = [];

  switch (period) {
    case 'today':
      dateFilter = ` AND DATE(created_at) = DATE('now')`;
      break;
    case 'week':
      dateFilter = ` AND DATE(created_at) >= DATE('now', '-7 days')`;
      break;
    case 'month':
      dateFilter = ` AND DATE(created_at) >= DATE('now', '-30 days')`;
      break;
  }

  const queries = {
    revenue: `SELECT SUM(total_amount) as total FROM orders WHERE status != 'cancelled'${dateFilter}`,
    expenses: `SELECT SUM(amount) as total FROM expenses WHERE 1=1${dateFilter.replace('created_at', 'expense_date')}`,
    orders: `SELECT COUNT(*) as total FROM orders WHERE status != 'cancelled'${dateFilter}`,
    customers: `SELECT COUNT(DISTINCT customer_phone) as total FROM orders WHERE status != 'cancelled' AND customer_phone IS NOT NULL${dateFilter}`
  };

  const results = {};
  let completed = 0;
  const totalQueries = Object.keys(queries).length;

  Object.entries(queries).forEach(([key, query]) => {
    db.get(query, params, (err, row) => {
      if (err) {
        console.error(`Error in ${key} query:`, err);
        results[key] = 0;
      } else {
        results[key] = row.total || 0;
      }

      completed++;
      if (completed === totalQueries) {
        const revenue = results.revenue || 0;
        const expenses = results.expenses || 0;
        const profit = revenue - expenses;

        res.json({
          revenue,
          expenses,
          profit,
          profit_margin: revenue > 0 ? ((profit / revenue) * 100) : 0,
          orders: results.orders || 0,
          customers: results.customers || 0
        });
      }
    });
  });
});

// Get tax settings
router.get('/tax-settings', (req, res) => {
  const db = getDb();

  db.all(`
    SELECT * FROM tax_settings 
    WHERE is_active = 1 
    ORDER BY tax_name
  `, (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error' });
    }
    res.json(rows);
  });
});

// Update tax settings
router.post('/tax-settings', (req, res) => {
  const { tax_name, tax_name_mm, tax_rate } = req.body;
  const db = getDb();

  db.run(`
    INSERT OR REPLACE INTO tax_settings (id, tax_name, tax_name_mm, tax_rate)
    VALUES (1, ?, ?, ?)
  `, [tax_name, tax_name_mm, tax_rate], function(err) {
    if (err) {
      return res.status(500).json({ error: 'Failed to update tax settings' });
    }
    res.json({ message: 'Tax settings updated successfully' });
  });
});

module.exports = router;
