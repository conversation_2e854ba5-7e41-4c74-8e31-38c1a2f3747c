const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { getDb } = require('../database/db');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// Middleware to check admin role
const requireAdmin = (req, res, next) => {
  const jwt = require('jsonwebtoken');
  const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }

    if (user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    req.user = user;
    next();
  });
};

// Get all categories (admin view) - temporarily disable auth for testing
router.get('/categories', (req, res) => {
  const db = getDb();
  
  db.all(
    'SELECT * FROM categories ORDER BY sort_order, name',
    [],
    (err, categories) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      res.json(categories);
    }
  );
});

// Create new category - temporarily disable auth for testing
router.post('/categories', upload.single('image'), (req, res) => {
  const { name, name_mm, description, icon, sort_order } = req.body;

  console.log('🔧 Creating category with data:', { name, name_mm, description, icon, sort_order });
  console.log('🔧 Received icon:', icon);

  if (!name || !name_mm) {
    return res.status(400).json({ error: 'Name and Myanmar name are required' });
  }

  const image_url = req.file ? `/uploads/${req.file.filename}` : null;
  const finalIcon = icon || '🍽️';
  const db = getDb();

  console.log('🔧 Final icon to save:', finalIcon);

  db.run(
    'INSERT INTO categories (name, name_mm, description, icon, image_url, sort_order, is_active, created_at) VALUES (?, ?, ?, ?, ?, ?, 1, datetime("now"))',
    [name, name_mm, description || null, finalIcon, image_url, sort_order || 0],
    function(err) {
      if (err) {
        console.error('❌ Error creating category:', err);
        return res.status(500).json({ error: 'Failed to create category: ' + err.message });
      }

      console.log('✅ Category created successfully with ID:', this.lastID);

      res.status(201).json({
        message: 'Category created successfully',
        category: {
          id: this.lastID,
          name,
          name_mm,
          description,
          icon: finalIcon,
          image_url,
          sort_order: sort_order || 0,
          is_active: 1
        }
      });
    }
  );
});

// Update category - temporarily disable auth for testing
router.put('/categories/:id', upload.single('image'), (req, res) => {
  const { id } = req.params;
  const { name, name_mm, description, icon, sort_order, is_active } = req.body;

  console.log('🔧 Updating category', id, 'with data:', { name, name_mm, description, icon, sort_order, is_active });
  console.log('🔧 Received icon for update:', icon);

  if (!name || !name_mm) {
    return res.status(400).json({ error: 'Name and Myanmar name are required' });
  }

  const db = getDb();

  // Get current category to preserve image if not updated
  db.get('SELECT * FROM categories WHERE id = ?', [id], (err, currentCategory) => {
    if (err) {
      console.error('❌ Database error:', err);
      return res.status(500).json({ error: 'Database error' });
    }

    if (!currentCategory) {
      return res.status(404).json({ error: 'Category not found' });
    }

    const image_url = req.file ? `/uploads/${req.file.filename}` : currentCategory.image_url;
    const finalIcon = icon || currentCategory.icon || '🍽️';
    const finalIsActive = is_active !== undefined ? is_active : currentCategory.is_active;
    const finalSortOrder = sort_order !== undefined ? sort_order : currentCategory.sort_order;

    console.log('🔧 Final values for update:', {
      finalIcon,
      finalIsActive,
      finalSortOrder,
      image_url
    });

    db.run(
      'UPDATE categories SET name = ?, name_mm = ?, description = ?, icon = ?, image_url = ?, sort_order = ?, is_active = ? WHERE id = ?',
      [name, name_mm, description || null, finalIcon, image_url, finalSortOrder, finalIsActive, id],
      function(err) {
        if (err) {
          console.error('❌ Error updating category:', err);
          return res.status(500).json({ error: 'Failed to update category: ' + err.message });
        }

        if (this.changes === 0) {
          console.log('⚠️ No rows were updated');
          return res.status(404).json({ error: 'Category not found or no changes made' });
        }

        console.log('✅ Category updated successfully, rows affected:', this.changes);

        // Return the updated category from database
        db.get('SELECT * FROM categories WHERE id = ?', [id], (err, updatedCategory) => {
          if (err) {
            console.error('❌ Error fetching updated category:', err);
            return res.status(500).json({ error: 'Category updated but failed to fetch result' });
          }

          console.log('✅ Updated category from DB:', updatedCategory);

          res.json({
            message: 'Category updated successfully',
            category: updatedCategory
          });
        });
      }
    );
  });
});

// Delete category - temporarily disable auth for testing
router.delete('/categories/:id', (req, res) => {
  const { id } = req.params;
  const db = getDb();

  // Check if category exists
  db.get('SELECT * FROM categories WHERE id = ?', [id], (err, category) => {
    if (err) {
      return res.status(500).json({ error: 'Database error' });
    }

    if (!category) {
      return res.status(404).json({ error: 'Category not found' });
    }

    // Check if category has menu items
    db.get('SELECT COUNT(*) as count FROM menu_items WHERE category_id = ?', [id], (err, result) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      if (result.count > 0) {
        return res.status(400).json({
          error: 'Cannot delete category with existing menu items',
          message: `This category has ${result.count} menu items. Please move or delete them first.`
        });
      }

      // Delete the category
      db.run('DELETE FROM categories WHERE id = ?', [id], function(err) {
        if (err) {
          return res.status(500).json({ error: 'Failed to delete category' });
        }

        res.json({
          message: 'Category deleted successfully',
          deletedId: parseInt(id)
        });
      });
    });
  });
});

// Reorder categories - temporarily disable auth for testing
router.put('/categories/reorder', (req, res) => {
  const { updates } = req.body;

  if (!updates || !Array.isArray(updates)) {
    return res.status(400).json({ error: 'Updates array is required' });
  }

  const db = getDb();

  // Begin transaction
  db.serialize(() => {
    db.run('BEGIN TRANSACTION');

    let completed = 0;
    let hasError = false;

    updates.forEach((update) => {
      if (hasError) return;

      db.run(
        'UPDATE categories SET sort_order = ? WHERE id = ?',
        [update.sort_order, update.id],
        function(err) {
          if (err) {
            hasError = true;
            db.run('ROLLBACK');
            return res.status(500).json({ error: 'Failed to reorder categories' });
          }

          completed++;
          if (completed === updates.length) {
            db.run('COMMIT');
            res.json({ message: 'Categories reordered successfully' });
          }
        }
      );
    });
  });
});

// Get all menu items (admin view)
router.get('/menu-items', requireAdmin, (req, res) => {
  const db = getDb();
  
  db.all(
    `SELECT mi.*, c.name as category_name, c.name_mm as category_name_mm 
     FROM menu_items mi 
     JOIN categories c ON mi.category_id = c.id 
     ORDER BY c.sort_order, mi.sort_order, mi.name`,
    [],
    (err, items) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      res.json(items);
    }
  );
});

// Create new menu item
router.post('/menu-items', requireAdmin, upload.single('image'), (req, res) => {
  const { 
    category_id, 
    name, 
    name_mm, 
    description, 
    description_mm, 
    price, 
    sort_order 
  } = req.body;
  
  if (!category_id || !name || !name_mm || !price) {
    return res.status(400).json({ error: 'Category, name, Myanmar name, and price are required' });
  }
  
  const image_url = req.file ? `/uploads/${req.file.filename}` : null;
  const db = getDb();
  
  db.run(
    `INSERT INTO menu_items (category_id, name, name_mm, description, description_mm, price, image_url, sort_order) 
     VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
    [category_id, name, name_mm, description, description_mm, price, image_url, sort_order || 0],
    function(err) {
      if (err) {
        return res.status(500).json({ error: 'Failed to create menu item' });
      }
      
      res.status(201).json({
        message: 'Menu item created successfully',
        item: {
          id: this.lastID,
          category_id,
          name,
          name_mm,
          description,
          description_mm,
          price,
          image_url,
          sort_order: sort_order || 0
        }
      });
    }
  );
});

// Update menu item availability
router.patch('/menu-items/:id/availability', requireAdmin, (req, res) => {
  const { id } = req.params;
  const { is_available } = req.body;
  
  const db = getDb();
  
  db.run(
    'UPDATE menu_items SET is_available = ? WHERE id = ?',
    [is_available, id],
    function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      
      if (this.changes === 0) {
        return res.status(404).json({ error: 'Menu item not found' });
      }
      
      res.json({
        message: 'Menu item availability updated successfully'
      });
    }
  );
});

// Get restaurant settings
router.get('/settings', requireAdmin, (req, res) => {
  const db = getDb();
  
  db.all(
    'SELECT * FROM settings ORDER BY key',
    [],
    (err, settings) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      
      // Convert to key-value object
      const settingsObj = {};
      settings.forEach(setting => {
        settingsObj[setting.key] = setting.value;
      });
      
      res.json(settingsObj);
    }
  );
});

// Update restaurant settings
router.put('/settings', requireAdmin, (req, res) => {
  const settings = req.body;
  const db = getDb();

  const updates = Object.entries(settings);
  let completed = 0;

  if (updates.length === 0) {
    return res.status(400).json({ error: 'No settings provided' });
  }

  updates.forEach(([key, value]) => {
    db.run(
      'UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?',
      [value, key],
      (err) => {
        if (err) {
          return res.status(500).json({ error: 'Failed to update settings' });
        }

        completed++;
        if (completed === updates.length) {
          res.json({ message: 'Settings updated successfully' });
        }
      }
    );
  });
});

// Reset database - TRUE RESET (CLEAR EVERYTHING)
router.post('/reset-database', async (req, res) => {
  try {
    console.log('🗑️ Starting TRUE DATABASE RESET (CLEAR EVERYTHING)...');

    const { getDb } = require('../database/db');
    const db = getDb();

    // Step 1: Clear ALL data completely - NO DEFAULT DATA RESTORATION
    console.log('1️⃣ CLEARING ALL DATA PERMANENTLY...');

    // Clear ALL tables completely
    const tablesToClear = ['menu_items', 'categories', 'orders', 'order_items', 'users', 'tables', 'settings'];

    for (const tableName of tablesToClear) {
      await new Promise((resolve, reject) => {
        db.run(`DELETE FROM ${tableName}`, [], (err) => {
          if (err) {
            console.error(`❌ Error clearing ${tableName}:`, err);
            reject(err);
          } else {
            console.log(`✅ Cleared ${tableName} - ALL DATA DELETED`);
            resolve();
          }
        });
      });
    }

    // Reset auto-increment counters
    await new Promise((resolve, reject) => {
      db.run('DELETE FROM sqlite_sequence', [], (err) => {
        if (err) {
          console.error('❌ Error resetting sequences:', err);
          reject(err);
        } else {
          console.log('✅ Reset auto-increment counters');
          resolve();
        }
      });
    });

    console.log('🎉 TRUE RESET COMPLETED - DATABASE IS NOW COMPLETELY EMPTY!');

    console.log('✅ TRUE DATABASE RESET COMPLETED SUCCESSFULLY');
    res.json({
      message: 'Database reset successfully',
      details: 'ALL DATA HAS BEEN PERMANENTLY DELETED. Database is now completely empty. You will need to set up everything from scratch.'
    });

  } catch (error) {
    console.error('❌ Error resetting database:', error);
    res.status(500).json({
      error: 'Failed to reset database',
      details: error.message
    });
  }
});

// Get all users (temporarily disable auth for testing)
router.get('/users', (req, res) => {
  const db = getDb();

  db.all(
    'SELECT id, username, role, name, created_at FROM users ORDER BY created_at DESC',
    [],
    (err, users) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      res.json(users);
    }
  );
});

// Create new user
router.post('/users', requireAdmin, (req, res) => {
  const { username, password, role, name } = req.body;
  const bcrypt = require('bcryptjs');

  if (!username || !password || !role || !name) {
    return res.status(400).json({ error: 'All fields are required' });
  }

  if (!['admin', 'counter', 'kitchen'].includes(role)) {
    return res.status(400).json({ error: 'Invalid role' });
  }

  const db = getDb();

  // Check if username already exists
  db.get(
    'SELECT id FROM users WHERE username = ?',
    [username],
    (err, existingUser) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      if (existingUser) {
        return res.status(400).json({ error: 'Username already exists' });
      }

      // Hash password
      bcrypt.hash(password, 10, (err, hashedPassword) => {
        if (err) {
          return res.status(500).json({ error: 'Password hashing error' });
        }

        // Insert new user
        db.run(
          'INSERT INTO users (username, password, role, name) VALUES (?, ?, ?, ?)',
          [username, hashedPassword, role, name],
          function(err) {
            if (err) {
              return res.status(500).json({ error: 'Failed to create user' });
            }

            res.status(201).json({
              message: 'User created successfully',
              user: {
                id: this.lastID,
                username,
                role,
                name
              }
            });
          }
        );
      });
    }
  );
});

// Update user password
router.put('/users/:id/password', requireAdmin, (req, res) => {
  const { id } = req.params;
  const { password } = req.body;
  const bcrypt = require('bcryptjs');

  if (!password) {
    return res.status(400).json({ error: 'Password is required' });
  }

  if (password.length < 6) {
    return res.status(400).json({ error: 'Password must be at least 6 characters' });
  }

  const db = getDb();

  // Hash new password
  bcrypt.hash(password, 10, (err, hashedPassword) => {
    if (err) {
      return res.status(500).json({ error: 'Password hashing error' });
    }

    db.run(
      'UPDATE users SET password = ? WHERE id = ?',
      [hashedPassword, id],
      function(err) {
        if (err) {
          return res.status(500).json({ error: 'Failed to update password' });
        }

        if (this.changes === 0) {
          return res.status(404).json({ error: 'User not found' });
        }

        res.json({ message: 'Password updated successfully' });
      }
    );
  });
});

// Delete user
router.delete('/users/:id', requireAdmin, (req, res) => {
  const { id } = req.params;
  const db = getDb();

  // Prevent deleting the last admin
  db.get(
    'SELECT COUNT(*) as count FROM users WHERE role = "admin"',
    [],
    (err, result) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      if (result.count <= 1) {
        // Check if this is an admin being deleted
        db.get(
          'SELECT role FROM users WHERE id = ?',
          [id],
          (err, user) => {
            if (err) {
              return res.status(500).json({ error: 'Database error' });
            }

            if (user && user.role === 'admin') {
              return res.status(400).json({ error: 'Cannot delete the last admin user' });
            }

            // Safe to delete non-admin user
            deleteUser();
          }
        );
      } else {
        // Safe to delete any user
        deleteUser();
      }
    }
  );

  function deleteUser() {
    db.run(
      'DELETE FROM users WHERE id = ?',
      [id],
      function(err) {
        if (err) {
          return res.status(500).json({ error: 'Failed to delete user' });
        }

        if (this.changes === 0) {
          return res.status(404).json({ error: 'User not found' });
        }

        res.json({ message: 'User deleted successfully' });
      }
    );
  }
});

module.exports = router;
