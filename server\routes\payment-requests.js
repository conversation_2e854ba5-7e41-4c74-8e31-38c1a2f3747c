const express = require('express');
const { getDb } = require('../database/db');

const router = express.Router();

// Create new payment request
router.post('/', (req, res) => {
  const { table_number, order_id, message, amount } = req.body;
  
  if (!table_number || !message) {
    return res.status(400).json({ error: 'Table number and message are required' });
  }
  
  const db = getDb();
  
  db.run(
    'INSERT INTO payment_requests (table_number, order_id, message, amount) VALUES (?, ?, ?, ?)',
    [table_number, order_id || null, message, amount || null],
    function(err) {
      if (err) {
        console.error('Error creating payment request:', err);
        return res.status(500).json({ error: 'Failed to create payment request' });
      }
      
      const newRequest = {
        id: this.lastID,
        table_number,
        order_id,
        message,
        amount,
        status: 'pending',
        created_at: new Date().toISOString()
      };

      // Emit real-time update to counter
      const io = req.app.get('io');
      if (io) {
        io.to('counter').emit('payment-request-created', newRequest);
      }

      res.status(201).json(newRequest);
    }
  );
});

// Get all payment requests
router.get('/', (req, res) => {
  const { table_number, status = 'pending' } = req.query;
  const db = getDb();
  
  let query = 'SELECT * FROM payment_requests WHERE status = ?';
  const params = [status];
  
  if (table_number) {
    query += ' AND table_number = ?';
    params.push(table_number);
  }
  
  query += ' ORDER BY created_at DESC';
  
  db.all(query, params, (err, requests) => {
    if (err) {
      console.error('Error fetching payment requests:', err);
      return res.status(500).json({ error: 'Failed to fetch payment requests' });
    }
    
    res.json(requests);
  });
});

// Update payment request status
router.patch('/:id', (req, res) => {
  const { id } = req.params;
  const { status } = req.body;
  
  if (!status) {
    return res.status(400).json({ error: 'Status is required' });
  }
  
  const db = getDb();
  
  db.run(
    'UPDATE payment_requests SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    [status, id],
    function(err) {
      if (err) {
        console.error('Error updating payment request:', err);
        return res.status(500).json({ error: 'Failed to update payment request' });
      }
      
      if (this.changes === 0) {
        return res.status(404).json({ error: 'Payment request not found' });
      }

      // Emit real-time update to counter
      const io = req.app.get('io');
      if (io) {
        io.to('counter').emit('payment-request-updated', { id, status });
      }

      res.json({ message: 'Payment request updated successfully' });
    }
  );
});

// Delete payment request
router.delete('/:id', (req, res) => {
  const { id } = req.params;
  const db = getDb();
  
  db.run('DELETE FROM payment_requests WHERE id = ?', [id], function(err) {
    if (err) {
      console.error('Error deleting payment request:', err);
      return res.status(500).json({ error: 'Failed to delete payment request' });
    }
    
    if (this.changes === 0) {
      return res.status(404).json({ error: 'Payment request not found' });
    }
    
    res.json({ message: 'Payment request deleted successfully' });
  });
});

module.exports = router;
