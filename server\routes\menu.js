const express = require('express');
const { getDb } = require('../database/db');

const router = express.Router();

// Get all categories
router.get('/categories', (req, res) => {
  const db = getDb();
  
  db.all(
    'SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order, name',
    [],
    (err, categories) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      res.json(categories);
    }
  );
});

// Get category by ID
router.get('/categories/:id', (req, res) => {
  const db = getDb();
  const { id } = req.params;

  db.get(
    'SELECT * FROM categories WHERE id = ? AND is_active = 1',
    [id],
    (err, category) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      if (!category) {
        return res.status(404).json({ error: 'Category not found' });
      }
      res.json(category);
    }
  );
});

// Create new category
router.post('/categories', (req, res) => {
  const db = getDb();
  const { name, name_mm, description, description_mm, image_url, icon, sort_order = 0 } = req.body;

  if (!name || !name_mm) {
    return res.status(400).json({ error: 'Name and name_mm are required' });
  }

  db.run(
    `INSERT INTO categories (name, name_mm, description, description_mm, image_url, icon, sort_order)
     VALUES (?, ?, ?, ?, ?, ?, ?)`,
    [name, name_mm, description, description_mm, image_url, icon || '🍽️', sort_order],
    function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      // Return the created category
      db.get(
        'SELECT * FROM categories WHERE id = ?',
        [this.lastID],
        (err, category) => {
          if (err) {
            return res.status(500).json({ error: 'Database error' });
          }
          res.status(201).json(category);
        }
      );
    }
  );
});

// Update category
router.put('/categories/:id', (req, res) => {
  const db = getDb();
  const { id } = req.params;
  const { name, name_mm, description, description_mm, image_url, icon, sort_order, is_active } = req.body;

  db.run(
    `UPDATE categories
     SET name = ?, name_mm = ?, description = ?, description_mm = ?, image_url = ?, icon = ?, sort_order = ?, is_active = ?
     WHERE id = ?`,
    [name, name_mm, description, description_mm, image_url, icon || '🍽️', sort_order, is_active !== undefined ? is_active : 1, id],
    function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      if (this.changes === 0) {
        return res.status(404).json({ error: 'Category not found' });
      }

      // Return the updated category
      db.get(
        'SELECT * FROM categories WHERE id = ?',
        [id],
        (err, category) => {
          if (err) {
            return res.status(500).json({ error: 'Database error' });
          }
          res.json(category);
        }
      );
    }
  );
});

// Delete category (soft delete)
router.delete('/categories/:id', (req, res) => {
  const db = getDb();
  const { id } = req.params;

  // Check if category has menu items
  db.get(
    'SELECT COUNT(*) as count FROM menu_items WHERE category_id = ? AND is_active = 1',
    [id],
    (err, result) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      if (result.count > 0) {
        return res.status(400).json({ error: 'Cannot delete category with active menu items' });
      }

      db.run(
        'UPDATE categories SET is_active = 0 WHERE id = ?',
        [id],
        function(err) {
          if (err) {
            return res.status(500).json({ error: 'Database error' });
          }

          if (this.changes === 0) {
            return res.status(404).json({ error: 'Category not found' });
          }

          res.json({ message: 'Category deleted successfully' });
        }
      );
    }
  );
});

// Get menu items by category
router.get('/categories/:categoryId/items', (req, res) => {
  const { categoryId } = req.params;
  const db = getDb();

  db.all(
    `SELECT mi.*, c.name as category_name, c.name_mm as category_name_mm
     FROM menu_items mi
     JOIN categories c ON mi.category_id = c.id
     WHERE mi.category_id = ? AND mi.is_active = 1
     ORDER BY mi.sort_order, mi.name`,
    [categoryId],
    (err, items) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      res.json(items);
    }
  );
});

// Get all menu items
router.get('/items', (req, res) => {
  const db = getDb();

  db.all(
    `SELECT mi.*, c.name as category_name, c.name_mm as category_name_mm
     FROM menu_items mi
     JOIN categories c ON mi.category_id = c.id
     WHERE mi.is_active = 1
     ORDER BY c.sort_order, mi.sort_order, mi.name`,
    [],
    (err, items) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      res.json(items);
    }
  );
});

// Get today's special items
router.get('/today-special', (req, res) => {
  const db = getDb();

  db.all(
    `SELECT mi.*, c.name as category_name, c.name_mm as category_name_mm
     FROM menu_items mi
     JOIN categories c ON mi.category_id = c.id
     WHERE mi.is_active = 1 AND mi.is_today_special = 1 AND mi.is_available = 1
     ORDER BY c.sort_order, mi.sort_order, mi.name`,
    [],
    (err, items) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      res.json(items);
    }
  );
});

// Get single menu item
router.get('/items/:id', (req, res) => {
  const { id } = req.params;
  const db = getDb();
  
  db.get(
    `SELECT mi.*, c.name as category_name, c.name_mm as category_name_mm 
     FROM menu_items mi 
     JOIN categories c ON mi.category_id = c.id 
     WHERE mi.id = ?`,
    [id],
    (err, item) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      
      if (!item) {
        return res.status(404).json({ error: 'Menu item not found' });
      }
      
      res.json(item);
    }
  );
});

// Get menu with categories and items (for customer view)
router.get('/full', (req, res) => {
  console.log('🔍 Full menu requested');
  const db = getDb();

  // Get categories first
  db.all(
    'SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order, name',
    [],
    (err, categories) => {
      if (err) {
        console.error('❌ Categories error:', err);
        return res.status(500).json({ error: 'Database error' });
      }

      console.log(`📂 Found ${categories.length} categories`);

      // Get all menu items
      db.all(
        `SELECT mi.*, c.name as category_name, c.name_mm as category_name_mm
         FROM menu_items mi
         JOIN categories c ON mi.category_id = c.id
         WHERE mi.is_active = 1 AND c.is_active = 1
         ORDER BY c.sort_order, mi.sort_order, mi.name`,
        [],
        (err, items) => {
          if (err) {
            console.error('❌ Menu items error:', err);
            return res.status(500).json({ error: 'Database error' });
          }

          console.log(`🍽️ Found ${items.length} menu items`);

          if (items.length === 0) {
            console.log('⚠️ No menu items found, checking without category filter...');

            // Try without category filter
            db.all('SELECT * FROM menu_items WHERE is_active = 1 LIMIT 5', [], (err, allItems) => {
              if (err) {
                console.error('❌ All items error:', err);
              } else {
                console.log(`📋 Total active menu items: ${allItems.length}`);
                allItems.forEach(item => {
                  console.log(`  - ${item.name_mm} (Category ID: ${item.category_id})`);
                });
              }
            });
          }

          // Group items by category
          const menuData = categories.map(category => ({
            ...category,
            items: items.filter(item => item.category_id === category.id)
          }));

          console.log(`✅ Returning menu data with ${menuData.length} categories`);
          res.json(menuData);
        }
      );
    }
  );
});

// Search menu items
router.get('/search', (req, res) => {
  const { q } = req.query;
  
  if (!q || q.trim().length < 2) {
    return res.status(400).json({ error: 'Search query must be at least 2 characters' });
  }
  
  const db = getDb();
  const searchTerm = `%${q.trim()}%`;
  
  db.all(
    `SELECT mi.*, c.name as category_name, c.name_mm as category_name_mm 
     FROM menu_items mi 
     JOIN categories c ON mi.category_id = c.id 
     WHERE mi.is_active = 1 AND mi.is_available = 1 
     AND (mi.name LIKE ? OR mi.name_mm LIKE ? OR mi.description LIKE ? OR mi.description_mm LIKE ?)
     ORDER BY mi.name`,
    [searchTerm, searchTerm, searchTerm, searchTerm],
    (err, items) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      res.json(items);
    }
  );
});

// Create new menu item
router.post('/items', (req, res) => {
  const db = getDb();
  const {
    category_id,
    name,
    name_mm,
    description,
    description_mm,
    price,
    image_url,
    is_available = 1,
    is_today_special = 0,
    is_active = 1,
    sort_order = 0
  } = req.body;

  if (!category_id || !name || !name_mm || !price) {
    return res.status(400).json({ error: 'Required fields: category_id, name, name_mm, price' });
  }

  db.run(
    `INSERT INTO menu_items (category_id, name, name_mm, description, description_mm, price, image_url, is_available, is_today_special, is_active, sort_order)
     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
    [category_id, name, name_mm, description, description_mm, price, image_url, is_available, is_today_special, is_active, sort_order],
    function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      // Return the created item with category info
      db.get(
        `SELECT mi.*, c.name as category_name, c.name_mm as category_name_mm
         FROM menu_items mi
         JOIN categories c ON mi.category_id = c.id
         WHERE mi.id = ?`,
        [this.lastID],
        (err, item) => {
          if (err) {
            return res.status(500).json({ error: 'Database error' });
          }

          // Emit real-time update
          const io = req.app.get('io');
          if (io) {
            io.emit('menu-updated', { type: 'item_created', item });
          }

          res.status(201).json(item);
        }
      );
    }
  );
});

// Update menu item
router.put('/items/:id', (req, res) => {
  const db = getDb();
  const { id } = req.params;
  const {
    category_id,
    name,
    name_mm,
    description,
    description_mm,
    price,
    image_url,
    is_available,
    is_today_special,
    is_active = 1,
    sort_order
  } = req.body;

  db.run(
    `UPDATE menu_items
     SET category_id = ?, name = ?, name_mm = ?, description = ?, description_mm = ?,
         price = ?, image_url = ?, is_available = ?, is_today_special = ?, is_active = ?, sort_order = ?
     WHERE id = ?`,
    [category_id, name, name_mm, description, description_mm, price, image_url, is_available, is_today_special, is_active, sort_order, id],
    function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      if (this.changes === 0) {
        return res.status(404).json({ error: 'Menu item not found' });
      }

      // Return the updated item with category info
      db.get(
        `SELECT mi.*, c.name as category_name, c.name_mm as category_name_mm
         FROM menu_items mi
         JOIN categories c ON mi.category_id = c.id
         WHERE mi.id = ?`,
        [id],
        (err, item) => {
          if (err) {
            return res.status(500).json({ error: 'Database error' });
          }

          // Emit real-time update
          const io = req.app.get('io');
          if (io) {
            io.emit('menu-updated', { type: 'item_updated', item });
          }

          res.json(item);
        }
      );
    }
  );
});

// Delete menu item (soft delete)
router.delete('/items/:id', (req, res) => {
  const db = getDb();
  const { id } = req.params;

  db.run(
    'UPDATE menu_items SET is_active = 0 WHERE id = ?',
    [id],
    function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      if (this.changes === 0) {
        return res.status(404).json({ error: 'Menu item not found' });
      }

      // Emit real-time update
      const io = req.app.get('io');
      if (io) {
        io.emit('menu-updated', { type: 'item_deleted', itemId: id });
      }

      res.json({ message: 'Menu item deleted successfully' });
    }
  );
});

// Toggle today special status
router.patch('/items/:id/today-special', (req, res) => {
  const db = getDb();
  const { id } = req.params;
  const { is_today_special } = req.body;

  db.run(
    'UPDATE menu_items SET is_today_special = ? WHERE id = ?',
    [is_today_special ? 1 : 0, id],
    function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      if (this.changes === 0) {
        return res.status(404).json({ error: 'Menu item not found' });
      }

      // Emit real-time update
      const io = req.app.get('io');
      if (io) {
        io.emit('menu-updated', { type: 'today_special_updated', itemId: id, is_today_special });
      }

      res.json({ message: 'Today special status updated successfully' });
    }
  );
});

// Toggle availability status
router.patch('/items/:id/availability', (req, res) => {
  const db = getDb();
  const { id } = req.params;
  const { is_available } = req.body;

  db.run(
    'UPDATE menu_items SET is_available = ? WHERE id = ?',
    [is_available ? 1 : 0, id],
    function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }

      if (this.changes === 0) {
        return res.status(404).json({ error: 'Menu item not found' });
      }

      // Emit real-time update
      const io = req.app.get('io');
      if (io) {
        io.emit('menu-updated', { type: 'availability_updated', itemId: id, is_available });
      }

      res.json({ message: 'Availability status updated successfully' });
    }
  );
});

module.exports = router;
