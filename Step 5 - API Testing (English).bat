@echo off
title Step 5 - Restaurant System API Tester
color 0E

echo.
echo ================================================================
echo                Step 5 - Restaurant System                     
echo                     API Tester                                
echo                                                               
echo    Step 5: Test and Verify All APIs                          
echo ================================================================
echo.

:MENU
echo Step 5 - API Testing Options:
echo.
echo [1] Health Check - Check server status
echo [2] Test Menu APIs - Test menu endpoints
echo [3] Test Category APIs - Test category endpoints
echo [4] Test Authentication APIs - Test auth endpoints
echo [5] Test User APIs - Test user management endpoints
echo [6] Test Order APIs - Test order management endpoints
echo [7] Test Table APIs - Test table endpoints
echo [8] Test Settings APIs - Test settings endpoints
echo [9] Test Admin APIs - Test admin endpoints
echo [0] Back to Main Menu
echo.
set /p choice="Select option (0-9): "

if "%choice%"=="1" goto HEALTH_CHECK
if "%choice%"=="2" goto TEST_MENU
if "%choice%"=="3" goto TEST_CATEGORIES
if "%choice%"=="4" goto TEST_AUTH
if "%choice%"=="5" goto TEST_USERS
if "%choice%"=="6" goto TEST_ORDERS
if "%choice%"=="7" goto TEST_TABLES
if "%choice%"=="8" goto TEST_SETTINGS
if "%choice%"=="9" goto TEST_ADMIN
if "%choice%"=="0" goto EXIT
goto INVALID

:HEALTH_CHECK
echo.
echo Step 5.1 - Health Check...
echo =======================================
echo Checking backend and frontend server status
echo.
echo Testing Backend Server (Port 5000):
echo URL: http://localhost:5000/api/health
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/health' -TimeoutSec 5; Write-Host $response; exit 0 } catch { Write-Host 'Error: Backend server not responding'; exit 1 }"
if %errorlevel%==0 (
    echo.
    echo ✓ Backend server is healthy and responding!
) else (
    echo ✗ Backend server is not responding!
    echo Please start the backend server first.
)

echo.
echo Testing Frontend Server (Port 3002):
echo URL: http://localhost:3002
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:3002' -TimeoutSec 3 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel%==0 (
    echo ✓ Frontend server is running!
) else (
    echo ✗ Frontend server is not responding!
    echo Please start the frontend server.
)
pause
goto MENU

:TEST_MENU
echo.
echo Step 5.2 - Testing Menu APIs...
echo =======================================
echo Testing menu endpoints
echo.
echo GET /api/menu (All menu items):
echo Getting all menu items...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/menu' -TimeoutSec 10; $response | Select-Object -First 5 | ConvertTo-Json -Depth 2 } catch { Write-Host 'Error: Could not fetch menu items' }"
echo.
echo.
echo GET /api/menu/1 (Specific menu item):
echo Getting menu item with ID 1...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/menu/1' -TimeoutSec 5; $response | ConvertTo-Json -Depth 2 } catch { Write-Host 'Error: Could not fetch menu item' }"
echo.
echo.
echo Menu API tests completed!
pause
goto MENU

:TEST_CATEGORIES
echo.
echo Step 5.3 - Testing Category APIs...
echo =======================================
echo Testing category endpoints
echo.
echo GET /api/categories (All categories):
echo Getting all categories...
curl -s http://localhost:5000/api/categories
echo.
echo.
echo Category API tests completed!
pause
goto MENU

:TEST_AUTH
echo.
echo Step 5.4 - Testing Authentication APIs...
echo =======================================
echo Testing authentication endpoints
echo.
echo POST /api/auth/login (Login test):
echo Testing login with admin credentials...
echo Username: admin, Password: admin123
curl -X POST http://localhost:5000/api/auth/login -H "Content-Type: application/json" -d "{\"username\":\"admin\",\"password\":\"admin123\"}"
echo.
echo.
echo Note: Copy the token from above to test protected APIs
echo.
echo Authentication API tests completed!
pause
goto MENU

:TEST_USERS
echo.
echo Step 5.5 - Testing User APIs...
echo =======================================
echo Testing user endpoints
echo.
echo GET /api/admin/users (All users - requires auth):
echo Enter your auth token (or press Enter to skip):
set /p token="Token: "
if "%token%"=="" (
    echo Skipping authenticated request
) else (
    echo Testing with provided token...
    curl -H "Authorization: Bearer %token%" http://localhost:5000/api/admin/users
    echo.
    echo User API test with authentication completed!
)
echo.
pause
goto MENU

:TEST_ORDERS
echo.
echo Step 5.6 - Testing Order APIs...
echo =======================================
echo Testing order endpoints
echo.
echo GET /api/admin/orders (All orders - requires auth):
echo Enter your auth token (or press Enter to skip):
set /p token="Token: "
if "%token%"=="" (
    echo Skipping authenticated request
) else (
    echo Testing with provided token...
    curl -H "Authorization: Bearer %token%" http://localhost:5000/api/admin/orders
    echo.
    echo Order API test with authentication completed!
)
echo.
pause
goto MENU

:TEST_TABLES
echo.
echo Step 5.7 - Testing Table APIs...
echo =======================================
echo Testing table endpoints
echo.
echo GET /api/tables (All tables):
echo Getting all tables...
curl -s http://localhost:5000/api/tables
echo.
echo.
echo Table API tests completed!
pause
goto MENU

:TEST_SETTINGS
echo.
echo Step 5.8 - Testing Settings APIs...
echo =======================================
echo Testing settings endpoints
echo.
echo GET /api/settings (All settings):
echo Getting all settings...
curl -s http://localhost:5000/api/settings
echo.
echo.
echo Settings API tests completed!
pause
goto MENU

:TEST_ADMIN
echo.
echo Step 5.9 - Testing Admin APIs...
echo =======================================
echo Testing admin endpoints
echo.
echo [1] Test Database Reset
echo [2] Test User Management
echo [3] Test Order Management
echo [4] Back to API Menu
echo.
set /p adminchoice="Select option (1-4): "

if "%adminchoice%"=="1" (
    echo.
    echo WARNING: This will reset the database!
    set /p confirm="Type 'YES' to confirm: "
    if "%confirm%"=="YES" (
        echo Testing database reset API...
        echo POST /api/admin/reset-database
        curl -X POST http://localhost:5000/api/admin/reset-database -H "Content-Type: application/json"
        echo.
        echo Database reset API test completed!
    ) else (
        echo Reset cancelled
    )
    pause
    goto MENU
)
if "%adminchoice%"=="2" (
    echo Testing user management APIs...
    echo Enter your auth token:
    set /p token="Token: "
    if not "%token%"=="" (
        echo GET /api/admin/users
        curl -H "Authorization: Bearer %token%" http://localhost:5000/api/admin/users
        echo.
        echo User management API test completed!
    ) else (
        echo No token provided, skipping test
    )
    pause
    goto MENU
)
if "%adminchoice%"=="3" (
    echo Testing order management APIs...
    echo Enter your auth token:
    set /p token="Token: "
    if not "%token%"=="" (
        echo GET /api/admin/orders
        curl -H "Authorization: Bearer %token%" http://localhost:5000/api/admin/orders
        echo.
        echo Order management API test completed!
    ) else (
        echo No token provided, skipping test
    )
    pause
    goto MENU
)
if "%adminchoice%"=="4" goto MENU
goto TEST_ADMIN

:INVALID
echo.
echo Invalid option! Please select 0-9.
pause
goto MENU

:EXIT
echo.
echo Step 5 Complete! Returning to Main Menu...
echo API Testing completed
pause
goto :EOF
