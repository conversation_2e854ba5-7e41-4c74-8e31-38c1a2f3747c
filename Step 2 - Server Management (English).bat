@echo off
title Step 2 - Restaurant System Server Manager
color 0A

echo.
echo ================================================================
echo                Step 2 - Restaurant System                     
echo                     Server Manager                            
echo                                                               
echo    Step 2: Start/Stop/Monitor Servers                        
echo ================================================================
echo.

:MENU
echo Step 2 - Server Management Options:
echo.
echo [1] Start Backend Server (Port 5000)
echo [2] Start Frontend Server (Port 3002)
echo [3] Start Both Servers
echo [4] Stop All Servers
echo [5] Check Server Status
echo [6] View Server Logs
echo [7] Back to Main Menu
echo.
set /p choice="Select option (1-7): "

if "%choice%"=="1" goto START_BACKEND
if "%choice%"=="2" goto START_FRONTEND
if "%choice%"=="3" goto START_BOTH
if "%choice%"=="4" goto STOP_SERVERS
if "%choice%"=="5" goto CHECK_STATUS
if "%choice%"=="6" goto VIEW_LOGS
if "%choice%"=="7" goto EXIT
goto INVALID

:START_BACKEND
echo.
echo Step 2.1 - Starting Backend Server...
echo =======================================
echo Backend server handles database and APIs
echo URL: http://localhost:5000
cd /d "%~dp0"
start "Backend Server - Port 5000" cmd /k "npm run server"
echo Backend server started successfully!
echo Check the new window for server logs
echo API Health Check: http://localhost:5000/api/health
pause
goto MENU

:START_FRONTEND
echo.
echo Step 2.2 - Starting Frontend Server...
echo =======================================
echo Frontend server serves user interface
echo URL: http://localhost:3002
cd /d "%~dp0"
start "Frontend Server - Port 3002" cmd /k "npm run dev"
echo Frontend server started successfully!
echo Check the new window for server logs
echo Web Interface: http://localhost:3002
pause
goto MENU

:START_BOTH
echo.
echo Step 2.3 - Starting Both Servers...
echo =======================================
echo Starting both Backend and Frontend servers
cd /d "%~dp0"
echo Starting Backend Server first...
start "Backend Server - Port 5000" cmd /k "npm run server"
echo ⏳ Waiting for backend server to start (3 seconds)...
timeout /t 3 /nobreak >nul
echo Starting Frontend Server...
start "Frontend Server - Port 3002" cmd /k "npm run dev"
echo ⏳ Waiting for frontend server to start (5 seconds)...
timeout /t 5 /nobreak >nul
echo.
echo ✅ Both servers started successfully!
echo.
echo 📋 Server Information:
echo   🔧 Backend API: http://localhost:5000
echo   🌐 Frontend Web: http://localhost:3002
echo   👨‍💼 Admin Panel: http://localhost:3002/admin
echo.
echo 📍 Default Login: admin / admin123
echo.
echo ⚠️ Note: Check the new command windows for server logs
echo    If servers fail to start, check for port conflicts
pause
goto MENU

:STOP_SERVERS
echo.
echo Step 2.4 - Stopping All Servers...
echo =======================================
echo Stopping all Node.js processes
taskkill /f /im node.exe 2>nul
taskkill /f /im npm.exe 2>nul
echo All Node.js processes stopped successfully!
echo Backend and Frontend servers stopped
pause
goto MENU

:CHECK_STATUS
echo.
echo Step 2.5 - Checking Server Status...
echo =======================================
echo Backend Server (Port 5000) Status:
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:5000/api/health' -TimeoutSec 3 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel%==0 (
    echo ✓ Backend server is running and healthy
    echo   API URL: http://localhost:5000
) else (
    echo ✗ Backend server is not running
    echo   Use option [1] to start backend server
)

echo.
echo Frontend Server (Port 3002) Status:
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:3002' -TimeoutSec 3 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel%==0 (
    echo ✓ Frontend server is running
    echo   Web URL: http://localhost:3002
) else (
    echo ✗ Frontend server is not running
    echo   Use option [2] to start frontend server
)

echo.
echo Active Node.js Processes:
tasklist /fi "imagename eq node.exe" 2>nul | find "node.exe" >nul
if %errorlevel%==0 (
    echo Node.js processes found:
    tasklist /fi "imagename eq node.exe"
) else (
    echo No Node.js processes running
    echo Use option [3] to start both servers
)
echo.
echo Press any key to return to menu...
pause
goto MENU

:VIEW_LOGS
echo.
echo Step 2.6 - Server Logs Viewer
echo =======================================
echo [1] View Backend Logs
echo [2] View Frontend Logs
echo [3] Back to Server Menu
echo.
set /p logchoice="Select option (1-3): "

if "%logchoice%"=="1" (
    echo Backend Server Logs:
    echo =======================================
    if exist server.log (
        echo Reading backend log file...
        type server.log
    ) else (
        echo No backend log file found
        echo Backend logs are shown in the server window
    )
    pause
    goto MENU
)
if "%logchoice%"=="2" (
    echo Frontend Server Logs:
    echo =======================================
    if exist frontend.log (
        echo Reading frontend log file...
        type frontend.log
    ) else (
        echo No frontend log file found
        echo Frontend logs are shown in the server window
    )
    pause
    goto MENU
)
if "%logchoice%"=="3" goto MENU
goto VIEW_LOGS

:INVALID
echo.
echo Invalid option! Please select 1-7.
pause
goto MENU

:EXIT
echo.
echo Step 2 Complete! Returning to Main Menu...
echo Server Management completed
pause
goto :EOF
