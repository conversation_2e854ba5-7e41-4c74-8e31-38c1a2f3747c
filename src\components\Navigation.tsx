'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import io from 'socket.io-client';
import { useAuth } from '../contexts/AuthContext';

interface NavigationProps {
  settings?: any;
  showNavLinks?: boolean;
  pageTitle?: string;
  pageTitleEn?: string;
}

export default function Navigation({ pageTitle, pageTitleEn, showNavLinks = false }: NavigationProps) {
  const [mounted, setMounted] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [settings, setSettings] = useState<any>({});
  const pathname = usePathname();
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    setMounted(true);

    // Fetch settings
    const fetchSettings = async () => {
      try {
        const response = await fetch('http://localhost:5000/api/settings');
        if (response.ok) {
          const data = await response.json();
          setSettings(data);
        }
      } catch (error) {
        console.error('Error fetching settings:', error);
      }
    };

    fetchSettings();

    // Setup socket connection for real-time settings updates
    const socket = io('http://localhost:5000');

    socket.on('settings-updated', (updatedSettings: any) => {
      setSettings(updatedSettings);
    });

    // Update time every second
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => {
      clearInterval(timer);
      socket.disconnect();
    };
  }, []);

  if (!mounted) {
    return null;
  }

  // Helper function to get setting value
  const getSettingValue = (key: string, defaultValue: string = '') => {
    const setting = settings?.[key];

    // Handle object format from database
    if (typeof setting === 'object' && setting !== null) {
      if (setting.value !== undefined && setting.value !== null) {
        return String(setting.value);
      }
    }

    // Handle direct string value
    if (typeof setting === 'string') {
      return setting;
    }

    return defaultValue;
  };

  // Default values if settings not loaded
  const restaurantName = getSettingValue('restaurant_name_mm', 'အရှင်စားသောက်ဆိုင်');
  const restaurantNameEn = getSettingValue('restaurant_name', 'A Shin Restaurant');
  const restaurantTitle = pageTitle || getSettingValue('restaurant_title_mm', 'မြန်မာ့အရသာ စားသောက်ဆိုင်');
  const restaurantTitleEn = pageTitleEn || getSettingValue('restaurant_title_en', 'Myanmar Traditional Cuisine');
  const restaurantLogo = getSettingValue('restaurant_logo', '');
  const restaurantIcon = getSettingValue('restaurant_icon', '🍜');
  const openingHoursMm = getSettingValue('opening_hours_mm', '၂၄ နာရီ ဖွင့်ထားသည်');
  const openingHoursEn = getSettingValue('opening_hours_en', 'Always Open for You');

  return (
    <header style={{ backgroundColor: 'rgb(29, 40, 56)' }} className="backdrop-blur-sm shadow-xl border-b-4 border-gradient-to-r from-orange-400 to-red-400 relative z-10">
      <div className="max-w-6xl mx-auto px-3 py-4">
        {/* Mobile Layout */}
        <div className="block md:hidden">
          <div className={`flex items-center mb-3 ${pageTitle ? 'justify-end' : 'justify-between'}`}>
            {!pageTitle && (
              <div className="flex items-center space-x-2">
                {restaurantLogo && restaurantLogo.trim() !== '' && restaurantLogo.startsWith('http') ? (
                  <div className="w-10 h-10 rounded-full overflow-hidden shadow-lg border-2 border-white/20">
                    <img
                      src={restaurantLogo}
                      alt="Logo"
                      className="w-full h-full object-cover"
                    />
                  </div>
                ) : (
                  <div className="w-10 h-10 bg-gradient-to-br from-orange-500 via-red-500 to-yellow-500 rounded-full flex items-center justify-center shadow-lg">
                    <span className="text-white text-sm font-bold">{restaurantIcon}</span>
                  </div>
                )}
                <div className="leading-relaxed">
                  <h1 className="text-base font-bold text-white leading-relaxed">
                    {restaurantIcon} {restaurantName}
                  </h1>
                  <p className="text-xs text-gray-300 leading-relaxed mt-1">{restaurantNameEn}</p>
                </div>
              </div>
            )}

            {/* Mobile Navigation Icons - Show nav links or admin only */}
            {showNavLinks ? (
              <div className="flex space-x-2">
                <Link href="/kitchen" className={`p-2 rounded-full transition-all duration-300 ${pathname === '/kitchen' ? 'bg-red-500 text-white' : 'bg-red-100 text-red-700 hover:bg-red-200'}`}>
                  <span className="text-sm">👨‍🍳</span>
                </Link>
                <Link href="/menu" className={`p-2 rounded-full transition-all duration-300 ${pathname === '/menu' ? 'bg-blue-500 text-white' : 'bg-blue-100 text-blue-700 hover:bg-blue-200'}`}>
                  <span className="text-sm">🍽️</span>
                </Link>
                <Link href="/" className={`p-2 rounded-full transition-all duration-300 ${pathname === '/' ? 'bg-orange-500 text-white' : 'bg-orange-100 text-orange-700 hover:bg-orange-200'}`}>
                  <span className="text-sm">🏠</span>
                </Link>
                <Link href="/admin" className={`p-2 rounded-full transition-all duration-300 ${pathname === '/admin' ? 'bg-purple-500 text-white' : 'bg-purple-100 text-purple-700 hover:bg-purple-200'}`}>
                  <span className="text-xs">⚙️</span>
                </Link>
              </div>
            ) : (
              <Link href={isAuthenticated ? "/admin" : "/login"} className={`p-2 rounded-full transition-all duration-300 ${(pathname === '/login' || pathname === '/admin') ? 'bg-purple-500 text-white' : 'bg-purple-100 text-purple-700 hover:bg-purple-200'}`}>
                <span className="text-xs">⚙️</span>
              </Link>
            )}
          </div>
          
          <div className={`${pageTitle ? 'text-left' : 'text-center'}`}>
            <h1 className="text-xl font-bold text-white mb-2 leading-relaxed">
              {pageTitle ? `🧾 ${restaurantTitle}` : `🍽️ ${restaurantTitle}`}
            </h1>
            <p className="text-sm text-gray-300 mb-3 leading-relaxed">{restaurantTitleEn}</p>
            {!pageTitle && (
              <div className="text-center">
                <p className="text-orange-300 font-bold text-sm leading-relaxed">🕐 {openingHoursMm} | {openingHoursEn}</p>
                <p className="text-gray-300 text-xs mt-1">
                  {currentTime.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: true
                  })} - {currentTime.toLocaleDateString('en-US', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric'
                  })}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Desktop Layout */}
        <div className={`hidden md:flex items-center ${pageTitle ? 'justify-between' : 'justify-between'}`}>
          {pageTitle ? (
            <div className="flex items-center space-x-3">
              <div className="leading-relaxed">
                <h1 className="text-xl font-bold text-white leading-relaxed">
                  🧾 {restaurantTitle}
                </h1>
                <p className="text-sm text-gray-300 font-medium leading-relaxed mt-1">{restaurantTitleEn}</p>
              </div>
            </div>
          ) : (
            <div className="flex items-center space-x-3">
              {restaurantLogo && restaurantLogo.trim() !== '' && restaurantLogo.startsWith('http') ? (
                <div className="w-12 h-12 rounded-full overflow-hidden shadow-lg border-2 border-white/20">
                  <img
                    src={restaurantLogo}
                    alt="Logo"
                    className="w-full h-full object-cover"
                  />
                </div>
              ) : (
                <div className="w-12 h-12 bg-gradient-to-br from-orange-500 via-red-500 to-yellow-500 rounded-full flex items-center justify-center shadow-lg">
                  <span className="text-white text-lg font-bold">{restaurantIcon}</span>
                </div>
              )}
              <div className="leading-relaxed">
                <h1 className="text-xl font-bold text-white leading-relaxed">
                  {restaurantIcon} {restaurantName}
                </h1>
                <p className="text-sm text-gray-300 font-medium leading-relaxed mt-1">{restaurantNameEn}</p>
              </div>
            </div>
          )}

          {!pageTitle && (
            <div className="text-center leading-relaxed">
              <h1 className="text-2xl font-bold text-white leading-relaxed">
                🍽️ {restaurantTitle}
              </h1>
              <p className="text-sm text-gray-300 font-medium leading-relaxed mt-1">{restaurantTitleEn}</p>
            </div>
          )}
          
          <div className="flex items-center space-x-3">
            <div className="text-right leading-relaxed">
              {!pageTitle && (
                <>
                  <p className="text-orange-300 font-bold text-base leading-relaxed">🕐 {openingHoursMm}</p>
                  <p className="text-gray-300 text-sm leading-relaxed mt-1">{openingHoursEn}</p>
                </>
              )}
              <p className="text-gray-300 text-xs mt-1">
                {currentTime.toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: true
                })}
              </p>
              <p className="text-gray-400 text-xs">
                {currentTime.toLocaleDateString('en-US', {
                  weekday: 'short',
                  month: 'short',
                  day: 'numeric'
                })}
              </p>
            </div>

            {/* Desktop Navigation - Show nav links or admin only */}
            {showNavLinks ? (
              <div className="flex space-x-2">
                <Link href="/kitchen" className={`px-3 py-2 rounded-full transition-all duration-300 text-xs font-medium ${pathname === '/kitchen' ? 'bg-red-500 text-white' : 'bg-red-100 text-red-700 hover:bg-red-200'}`}>
                  👨‍🍳 မီးဖိုချောင်
                </Link>
                <Link href="/menu" className={`px-3 py-2 rounded-full transition-all duration-300 text-xs font-medium ${pathname === '/menu' ? 'bg-blue-500 text-white' : 'bg-blue-100 text-blue-700 hover:bg-blue-200'}`}>
                  🍽️ မီနူး
                </Link>
                <Link href="/" className={`px-3 py-2 rounded-full transition-all duration-300 text-xs font-medium ${pathname === '/' ? 'bg-orange-500 text-white' : 'bg-orange-100 text-orange-700 hover:bg-orange-200'}`}>
                  🏠 ပင်မစာမျက်နှာ
                </Link>
                <Link href="/admin" className={`px-3 py-2 rounded-full transition-all duration-300 text-xs font-medium ${pathname === '/admin' ? 'bg-purple-500 text-white' : 'bg-purple-100 text-purple-700 hover:bg-purple-200'}`}>
                  ⚙️ စီမံခန့်ခွဲမှု
                </Link>
              </div>
            ) : (
              <Link href={isAuthenticated ? "/admin" : "/login"} className={`px-3 py-2 rounded-full transition-all duration-300 text-xs font-medium ${(pathname === '/login' || pathname === '/admin') ? 'bg-purple-500 text-white' : 'bg-purple-100 text-purple-700 hover:bg-purple-200'}`}>
                ⚙️ စီမံခန့်ခွဲမှု
              </Link>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
